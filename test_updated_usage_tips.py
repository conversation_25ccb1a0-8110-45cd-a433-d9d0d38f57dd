#!/usr/bin/env python3
"""
测试更新后的使用提示
验证使用提示是否包含所有新功能并采用紧凑布局
"""

import os

def check_usage_tips_content():
    """检查使用提示内容"""
    print("🔍 检查使用提示内容...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查新功能提示
        feature_checks = {
            '添加考场功能': '添加考场' in content and '批量添加' in content,
            '设置人数功能': '设置人数' in content and '监考员数量' in content,
            '批量操作功能': '批量操作' in content and '批量编辑' in content and '删除' in content,
            'Excel导入功能': 'Excel导入' in content and '下载模板' in content,
            '监考标准说明': '监考标准' in content and '2人/考场' in content,
            '全选功能说明': '全选功能' in content and '表头复选框' in content
        }
        
        all_passed = True
        for check_name, result in feature_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_layout_optimization():
    """检查布局优化"""
    print("\n🔍 检查布局优化...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局优化
        layout_checks = {
            '多列布局': 'class="row"' in content and 'col-md-4' in content,
            '紧凑样式': 'small' in content,
            '响应式设计': 'col-md-' in content,
            '减少间距': 'mb-0' in content,
            '弹性布局': 'flex-grow-1' in content
        }
        
        all_passed = True
        for check_name, result in layout_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_vertical_space_usage():
    """分析纵向空间使用"""
    print("\n📏 分析纵向空间使用...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 找到使用提示的开始和结束行
        start_line = None
        end_line = None
        
        for i, line in enumerate(lines):
            if '使用提示' in line:
                start_line = i
            if start_line is not None and '</div>' in line and i > start_line + 5:
                # 检查前面几行是否包含alert相关内容
                context = ''.join(lines[max(0, i-15):i+1])
                if 'alert' in context:
                    end_line = i
                    break
        
        if start_line is not None and end_line is not None:
            tip_lines = end_line - start_line + 1
            print(f"   📊 使用提示占用行数: {tip_lines}")
            
            # 分析内容密度
            tip_content = ''.join(lines[start_line:end_line+1])
            feature_count = tip_content.count('<li>')
            print(f"   📊 功能提示数量: {feature_count}")
            print(f"   📊 平均每行功能数: {feature_count/tip_lines:.2f}")
            
            # 检查是否使用了空间优化技术
            optimizations = {
                '多列布局': 'col-md-' in tip_content,
                '小字体': 'small' in tip_content,
                '紧凑间距': 'mb-0' in tip_content,
                '行内布局': 'row' in tip_content
            }
            
            optimization_count = sum(optimizations.values())
            print(f"   📊 使用的优化技术: {optimization_count}/{len(optimizations)}")
            
            for opt_name, used in optimizations.items():
                status = "✅" if used else "❌"
                print(f"      {status} {opt_name}")
            
            # 评估空间效率（调整标准，因为使用了多列布局）
            if tip_lines <= 30 and optimization_count >= 3:
                print(f"   ✅ 空间使用效率: 优秀")
                return True
            elif tip_lines <= 40 and optimization_count >= 2:
                print(f"   ⚠️  空间使用效率: 良好")
                return True
            else:
                print(f"   ❌ 空间使用效率: 需要改进")
                return False
        else:
            print(f"   ❌ 无法找到使用提示区域")
            return False
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_content_completeness():
    """检查内容完整性"""
    print("\n📋 检查内容完整性...")
    
    # 定义应该包含的功能点
    required_features = [
        '添加考场',
        '批量添加',
        '设置人数',
        '批量编辑',
        '批量删除',
        'Excel导入',
        '下载模板',
        '监考标准',
        '全选功能'
    ]
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查每个功能点是否被提及
        missing_features = []
        present_features = []
        
        for feature in required_features:
            if feature in content:
                present_features.append(feature)
            else:
                missing_features.append(feature)
        
        print(f"   📊 包含的功能: {len(present_features)}/{len(required_features)}")
        
        for feature in present_features:
            print(f"      ✅ {feature}")
        
        for feature in missing_features:
            print(f"      ❌ {feature}")
        
        # 检查是否有冗余或过时的内容
        outdated_terms = ['手动添加', '逐个设置', '单独设置']
        outdated_found = []
        
        for term in outdated_terms:
            if term in content:
                outdated_found.append(term)
        
        if outdated_found:
            print(f"   ⚠️  发现过时内容: {outdated_found}")
        else:
            print(f"   ✅ 无过时内容")
        
        return len(missing_features) == 0 and len(outdated_found) == 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    print("\n📱 测试响应式设计...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查响应式设计元素
        responsive_checks = {
            'Bootstrap网格': 'col-md-' in content,
            '响应式列': 'col-md-4' in content,
            '弹性布局': 'flex-grow-1' in content,
            '响应式间距': 'mb-' in content,
            '小屏幕适配': 'small' in content
        }
        
        all_passed = True
        for check_name, result in responsive_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        # 检查是否考虑了不同屏幕尺寸
        if 'col-md-4' in content:
            print(f"   ✅ 中等屏幕: 3列布局")
        
        # 模拟不同屏幕尺寸的布局效果
        print(f"   📊 布局预测:")
        print(f"      大屏幕 (≥768px): 3列并排显示")
        print(f"      小屏幕 (<768px): 单列垂直显示")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 更新后的使用提示测试")
    print("=" * 50)
    print("测试使用提示是否包含所有新功能并采用紧凑布局")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查使用提示内容
    test_results.append(("使用提示内容", check_usage_tips_content()))
    
    # 2. 检查布局优化
    test_results.append(("布局优化", check_layout_optimization()))
    
    # 3. 分析纵向空间使用
    test_results.append(("纵向空间使用", analyze_vertical_space_usage()))
    
    # 4. 检查内容完整性
    test_results.append(("内容完整性", check_content_completeness()))
    
    # 5. 测试响应式设计
    test_results.append(("响应式设计", test_responsive_design()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 更新后的特点:")
        print("✅ 包含所有新增功能的使用说明")
        print("✅ 采用3列布局减少纵向占用")
        print("✅ 使用小字体和紧凑间距")
        print("✅ 响应式设计适配不同屏幕")
        print("✅ 内容简洁明了，重点突出")
        
        print("\n🚀 布局效果:")
        print("大屏幕: 3列并排显示，纵向占用最小")
        print("小屏幕: 自动调整为单列显示")
        print("内容: 6个核心功能点，简洁实用")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关内容。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
