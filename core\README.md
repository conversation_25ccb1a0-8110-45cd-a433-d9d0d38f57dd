# 监考安排自动化系统

这是一个基于Python的智能监考安排系统，用于自动化分配监考员到各个考场。系统采用Google OR-Tools优化引擎进行智能调度，支持复杂的约束条件处理。

## 功能特点

1. **配置文件处理**
   - 支持Excel格式的配置文件
   - 处理监考员、科目、考场设置
   - 自动计算考试时长
   - 生成汇总信息

2. **智能排班优化**
   - 支持多种监考时间策略（集中/分散）
   - 考虑时间冲突和考场限制
   - 优化监考员工作负载
   - 自动处理特殊要求（必监考、不监考等）

3. **结果输出**
   - 生成详细的Excel报告
   - 包含考场安排、监考员安排等
   - 提供统计信息和分析报告

## 系统要求

- Python 3.8+
- 依赖包：
  - ortools>=9.8.3296
  - pandas>=1.0.0
  - numpy>=1.19.0
  - openpyxl>=3.0.0
  - xlrd>=2.0.0
  - XlsxWriter>=3.0.0

## 安装说明

1. 克隆或下载项目代码
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 准备配置文件：
   - 创建`Exam_Config.xlsx`文件
   - 包含三个工作表：监考员设置、考试科目设置、考场设置

2. 运行程序：
   ```bash
   python main.py
   ```

3. 查看结果：
   - `考试配置处理结果.xlsx`：配置文件处理结果
   - `监考安排结果.xlsx`：最终的监考安排方案

## 配置文件格式

### 监考员设置表
- 监考老师：教师姓名
- 必监考科目：指定必须监考的科目
- 不监考科目：指定不能监考的科目
- 必监考考场：指定必须监考的考场
- 不监考考场：指定不能监考的考场

### 考试科目设置表
- 课程名称：科目名称
- 开始时间：考试开始时间
- 结束时间：考试结束时间

### 考场设置表
- 考场：考场编号
- [科目名]：每个科目的考场需求（1表示需要1名监考员，2表示需要2名监考员）

## 监考时间策略

系统支持两种监考时间策略：

1. **集中监考（默认）**
   - 尽量将监考员的监考任务安排在相近的时间段
   - 减少监考员往返学校的次数
   - 适合希望集中工作时间的场景

2. **分散监考**
   - 将监考任务均匀分布在不同时间段
   - 避免过度集中的工作负担
   - 适合需要灵活安排的场景

## 注意事项

1. 确保配置文件格式正确，所有必要字段都已填写
2. 监考员的特殊要求（必监考、不监考等）会被优先考虑
3. 系统会自动处理时间冲突，确保每个监考员不会被安排到时间重叠的考试
4. 优化过程可能需要几分钟时间，请耐心等待

## 常见问题

1. **找不到配置文件**
   - 确保`Exam_Config.xlsx`文件位于正确位置
   - 检查文件名大小写是否正确

2. **OR-Tools未安装**
   - 运行`pip install ortools`安装
   - 确保Python版本兼容（3.8+）

3. **结果不符合预期**
   - 检查配置文件中的约束条件是否正确
   - 查看是否有冲突的要求
   - 考虑调整监考时间策略

## 更新日志

### v2.0
- 完全实现了原算法的功能
- 优化运行效率（0.15秒 vs 1秒）
- 保持完全的接口兼容性
- 添加监考时间策略支持 