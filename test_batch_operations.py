#!/usr/bin/env python3
"""
测试批量编辑和批量删除考场功能
验证考场页面的批量操作功能是否正常工作
"""

import os

def check_batch_operations_ui():
    """检查批量操作UI组件"""
    print("🔍 检查批量操作UI组件...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查UI组件
        ui_checks = {
            '批量编辑按钮': 'id="batch-edit-room-btn"' in content,
            '批量删除按钮': 'id="batch-delete-room-btn"' in content,
            '全选复选框': 'id="select-all-rooms"' in content,
            '行复选框': 'room-checkbox' in content,
            '批量编辑模态框': 'id="batchEditModal"' in content,
            '选择数量显示': 'id="selected-rooms-count"' in content,
            '批量编辑表单': 'id="batch-edit-form"' in content,
            '批量编辑输入': 'batch-edit-demand' in content,
            '确认编辑按钮': 'id="confirm-batch-edit"' in content
        }
        
        all_passed = True
        for check_name, result in ui_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_batch_operations_javascript():
    """检查批量操作JavaScript功能"""
    print("\n🔍 检查批量操作JavaScript功能...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查JavaScript功能
        js_checks = {
            '全选功能': '#select-all-rooms' in content and '.change(function()' in content,
            '复选框监听': '.room-checkbox' in content and 'change' in content,
            '按钮状态更新': 'updateBatchButtonsState' in content,
            '批量删除处理': '#batch-delete-room-btn' in content and 'click(function()' in content,
            '批量编辑处理': '#confirm-batch-edit' in content and 'click(function()' in content,
            '删除确认对话框': 'confirm(' in content,
            '数据收集': 'collectDataFromDOM()' in content,
            '表格重新渲染': 'renderRooms()' in content,
            '成功提示': 'alert-success' in content
        }
        
        all_passed = True
        for check_name, result in js_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_batch_delete_logic():
    """测试批量删除逻辑"""
    print("\n🧪 测试批量删除逻辑...")
    
    # 模拟考场数据
    rooms = [
        {'name': '1考场', 'demands': {'语文': 2, '数学': 2}},
        {'name': '2考场', 'demands': {'语文': 2, '数学': 2}},
        {'name': '3考场', 'demands': {'语文': 2, '数学': 2}},
        {'name': '4考场', 'demands': {'语文': 2, '数学': 2}},
        {'name': '5考场', 'demands': {'语文': 2, '数学': 2}}
    ]
    
    test_cases = [
        # (选中的索引列表, 期望剩余的考场名称)
        ([0, 2], ['2考场', '4考场', '5考场']),  # 删除第1和第3个
        ([1, 3], ['1考场', '3考场', '5考场']),  # 删除第2和第4个
        ([0, 1, 2], ['4考场', '5考场']),       # 删除前3个
        ([2, 3, 4], ['1考场', '2考场']),       # 删除后3个
        ([0, 1, 2, 3, 4], [])                 # 删除全部
    ]
    
    all_passed = True
    
    for selected_indices, expected_names in test_cases:
        # 模拟JavaScript删除逻辑
        test_rooms = [room.copy() for room in rooms]  # 复制数据
        
        # 从后往前删除（模拟JavaScript逻辑）
        sorted_indices = sorted(selected_indices, reverse=True)
        for index in sorted_indices:
            if 0 <= index < len(test_rooms):
                test_rooms.pop(index)
        
        # 验证结果
        actual_names = [room['name'] for room in test_rooms]
        
        if actual_names == expected_names:
            print(f"   ✅ 删除索引{selected_indices}: 剩余{actual_names}")
        else:
            print(f"   ❌ 删除索引{selected_indices}: 期望{expected_names}, 实际{actual_names}")
            all_passed = False
    
    return all_passed

def test_batch_edit_logic():
    """测试批量编辑逻辑"""
    print("\n🧪 测试批量编辑逻辑...")
    
    # 模拟考场数据
    rooms = [
        {'name': '1考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '2考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '3考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '4考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '5考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}}
    ]
    
    test_cases = [
        # (选中的索引, 编辑的需求, 期望的结果)
        ([0, 2], {'语文': 3}, {'语文': [3, 2, 3, 2, 2], '数学': [2, 2, 2, 2, 2], '英语': [1, 1, 1, 1, 1]}),
        ([1, 3], {'数学': 1, '英语': 2}, {'语文': [2, 2, 2, 2, 2], '数学': [2, 1, 2, 1, 2], '英语': [1, 2, 1, 2, 1]}),
        ([0, 1, 2], {'语文': 4}, {'语文': [4, 4, 4, 2, 2], '数学': [2, 2, 2, 2, 2], '英语': [1, 1, 1, 1, 1]})
    ]
    
    all_passed = True
    
    for selected_indices, edit_demands, expected_result in test_cases:
        # 模拟JavaScript编辑逻辑
        test_rooms = []
        for room in rooms:
            test_rooms.append({
                'name': room['name'],
                'demands': room['demands'].copy()
            })
        
        # 批量更新选中考场的监考人数
        for index in selected_indices:
            if 0 <= index < len(test_rooms):
                for subject, demand in edit_demands.items():
                    test_rooms[index]['demands'][subject] = demand
        
        # 验证结果
        actual_result = {}
        for subject in ['语文', '数学', '英语']:
            actual_result[subject] = [room['demands'][subject] for room in test_rooms]
        
        if actual_result == expected_result:
            print(f"   ✅ 编辑索引{selected_indices}, 设置{edit_demands}: 成功")
        else:
            print(f"   ❌ 编辑索引{selected_indices}, 设置{edit_demands}: 失败")
            print(f"      期望: {expected_result}")
            print(f"      实际: {actual_result}")
            all_passed = False
    
    return all_passed

def test_checkbox_state_logic():
    """测试复选框状态逻辑"""
    print("\n🧪 测试复选框状态逻辑...")
    
    # 模拟复选框状态变化
    test_cases = [
        # (总数, 选中数, 期望的全选状态)
        (5, 0, 'unchecked'),      # 无选中
        (5, 5, 'checked'),        # 全选中
        (5, 3, 'indeterminate'),  # 部分选中
        (1, 1, 'checked'),        # 单个全选
        (10, 1, 'indeterminate')  # 大量中的少数
    ]
    
    all_passed = True
    
    for total, checked, expected_state in test_cases:
        # 模拟JavaScript逻辑
        if checked == 0:
            actual_state = 'unchecked'
        elif checked == total:
            actual_state = 'checked'
        else:
            actual_state = 'indeterminate'
        
        if actual_state == expected_state:
            print(f"   ✅ 总数{total}, 选中{checked}: {actual_state}")
        else:
            print(f"   ❌ 总数{total}, 选中{checked}: 期望{expected_state}, 实际{actual_state}")
            all_passed = False
    
    return all_passed

def test_button_state_logic():
    """测试按钮状态逻辑"""
    print("\n🧪 测试按钮状态逻辑...")
    
    # 模拟按钮状态变化
    test_cases = [
        # (选中数量, 期望的按钮状态)
        (0, 'disabled'),   # 无选中时按钮禁用
        (1, 'enabled'),    # 有选中时按钮启用
        (5, 'enabled'),    # 多选时按钮启用
        (10, 'enabled')    # 大量选中时按钮启用
    ]
    
    all_passed = True
    
    for selected_count, expected_state in test_cases:
        # 模拟JavaScript逻辑
        has_selection = selected_count > 0
        actual_state = 'enabled' if has_selection else 'disabled'
        
        if actual_state == expected_state:
            print(f"   ✅ 选中{selected_count}个: 按钮{actual_state}")
        else:
            print(f"   ❌ 选中{selected_count}个: 期望{expected_state}, 实际{actual_state}")
            all_passed = False
    
    return all_passed

def test_integration_scenarios():
    """测试完整集成场景"""
    print("\n🧪 测试完整集成场景...")
    
    scenarios = [
        {
            'name': '批量删除场景',
            'initial_rooms': 5,
            'action': 'delete',
            'selected': [0, 2, 4],
            'expected_remaining': 2
        },
        {
            'name': '批量编辑场景',
            'initial_rooms': 3,
            'action': 'edit',
            'selected': [0, 1],
            'edit_data': {'语文': 3, '数学': 1},
            'expected_changes': 2
        },
        {
            'name': '混合操作场景',
            'initial_rooms': 10,
            'action': 'delete_then_edit',
            'delete_selected': [0, 1, 2],
            'edit_selected': [0, 1],  # 删除后的索引
            'expected_final': 7
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        try:
            # 创建初始考场数据
            rooms = []
            for i in range(scenario['initial_rooms']):
                rooms.append({
                    'name': f'{i+1}考场',
                    'demands': {'语文': 2, '数学': 2, '英语': 1}
                })
            
            if scenario['action'] == 'delete':
                # 模拟删除操作
                selected_indices = scenario['selected']
                sorted_indices = sorted(selected_indices, reverse=True)
                for index in sorted_indices:
                    if 0 <= index < len(rooms):
                        rooms.pop(index)
                
                if len(rooms) == scenario['expected_remaining']:
                    print(f"   ✅ 删除操作: 剩余{len(rooms)}个考场")
                else:
                    print(f"   ❌ 删除操作: 期望剩余{scenario['expected_remaining']}, 实际{len(rooms)}")
                    all_passed = False
            
            elif scenario['action'] == 'edit':
                # 模拟编辑操作
                selected_indices = scenario['selected']
                edit_data = scenario['edit_data']
                
                for index in selected_indices:
                    if 0 <= index < len(rooms):
                        for subject, demand in edit_data.items():
                            rooms[index]['demands'][subject] = demand
                
                # 验证编辑结果
                changed_count = 0
                for index in selected_indices:
                    if 0 <= index < len(rooms):
                        changed_count += 1
                
                if changed_count == scenario['expected_changes']:
                    print(f"   ✅ 编辑操作: 修改了{changed_count}个考场")
                else:
                    print(f"   ❌ 编辑操作: 期望修改{scenario['expected_changes']}, 实际{changed_count}")
                    all_passed = False
            
        except Exception as e:
            print(f"   ❌ 场景执行失败: {e}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🔧 批量编辑和批量删除考场功能测试")
    print("=" * 60)
    print("测试考场页面的批量操作功能")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查UI组件
    test_results.append(("批量操作UI组件", check_batch_operations_ui()))
    
    # 2. 检查JavaScript功能
    test_results.append(("批量操作JavaScript", check_batch_operations_javascript()))
    
    # 3. 测试批量删除逻辑
    test_results.append(("批量删除逻辑", test_batch_delete_logic()))
    
    # 4. 测试批量编辑逻辑
    test_results.append(("批量编辑逻辑", test_batch_edit_logic()))
    
    # 5. 测试复选框状态逻辑
    test_results.append(("复选框状态逻辑", test_checkbox_state_logic()))
    
    # 6. 测试按钮状态逻辑
    test_results.append(("按钮状态逻辑", test_button_state_logic()))
    
    # 7. 测试集成场景
    test_results.append(("集成场景测试", test_integration_scenarios()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 功能特点:")
        print("✅ 批量选择考场（全选/单选）")
        print("✅ 批量编辑监考人数")
        print("✅ 批量删除考场")
        print("✅ 智能按钮状态管理")
        print("✅ 复选框状态同步")
        print("✅ 操作确认和反馈")
        
        print("\n🚀 使用方法:")
        print("1. 使用复选框选择要操作的考场")
        print("2. 点击'批量编辑'设置监考人数")
        print("3. 点击'批量删除'删除选中考场")
        print("4. 全选复选框可快速选择所有考场")
        print("5. 操作完成后显示成功反馈")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
