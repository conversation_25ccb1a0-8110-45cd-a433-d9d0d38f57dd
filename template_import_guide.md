# 监考信息设置向导 - 模板下载和导入功能使用指南

## 🎯 功能概述

新增的模板下载和Excel导入功能让您可以更便捷地设置监考信息：

1. **下载模板** - 获取标准格式的Excel模板
2. **填写数据** - 在模板中填写您的数据
3. **批量导入** - 一键导入所有数据到系统

## 📝 使用步骤

### 第一步：科目设置 (http://localhost:5000/wizard/step1_subjects)

1. 点击"**下载导入模板**"按钮下载科目设置模板 (`kemu.xlsx`)
2. 在Excel中填写以下信息：
   - **课程代码**: 如 A、B、C 等
   - **课程名称**: 如语文、数学、英语等
   - **开始时间**: 格式为 `2025-02-08 09:00:00`
   - **结束时间**: 格式为 `2025-02-08 11:30:00`

3. 保存文件后，点击"**从Excel导入**"按钮上传文件
4. 系统会自动解析并导入科目数据

### 第二步：考场设置 (http://localhost:5000/wizard/step2_rooms)

1. 点击"**下载导入模板**"按钮下载考场设置模板 (`kaochang.xlsx`)
2. 在Excel中填写：
   - **考场**: 考场名称，如 1考场、2考场等
   - **各科目列**: 每个科目需要的监考员数量

3. 上传文件后系统会自动导入考场和需求数据

### 第三步：监考员设置 (http://localhost:5000/wizard/step3_proctors)

1. 点击"**下载导入模板**"按钮下载监考员设置模板 (`jiankaoyuan.xlsx`)
2. 在Excel中填写：
   - **监考老师**: 监考员姓名
   - **任教科目**: 教授的科目
   - **必监考科目**: 必须监考的科目（用逗号分隔）
   - **不监考科目**: 不能监考的科目（用逗号分隔）
   - **必监考考场**: 必须监考的考场
   - **不监考考场**: 不能监考的考场
   - **场次限制**: 最多监考场次数

3. 上传文件后系统会自动导入监考员信息

## 📋 模板文件说明

### 科目设置模板 (kemu.xlsx)
```
课程代码 | 课程名称 | 开始时间           | 结束时间
A       | 语文     | 2025-02-08 09:00:00 | 2025-02-08 11:30:00
B       | 数学     | 2025-02-08 14:00:00 | 2025-02-08 16:00:00
C       | 英语     | 2025-02-08 16:30:00 | 2025-02-08 18:30:00
```

### 考场设置模板 (kaochang.xlsx)
```
考场  | 语文 | 数学 | 英语
1考场 | 2   | 2   | 2
2考场 | 2   | 2   | 2
3考场 | 2   | 2   | 2
```

### 监考员设置模板 (jiankaoyuan.xlsx)
```
序号 | 监考老师 | 任教科目 | 必监考科目 | 不监考科目 | 必监考考场 | 不监考考场 | 场次限制
1   | 张老师   | 语文     |          |          |          |          | 5
2   | 李老师   | 数学     |          |          |          |          | 5
3   | 王老师   | 英语     |          |          |          |          | 5
```

## ⚠️ 注意事项

### 时间格式
- 支持格式：`2025-02-08 09:00:00` 或 `2025/02/08 09:00`
- 确保结束时间晚于开始时间

### 文件要求
- 文件格式：Excel (.xlsx)
- 文件大小：不超过5MB
- 编码：支持中文

### 数据验证
- 系统会自动验证数据格式
- 如有错误会显示具体错误信息
- 可以修改后重新导入

## 🔧 故障排除

### 常见问题

1. **导入失败**
   - 检查文件格式是否为Excel
   - 确认列名与模板一致
   - 检查时间格式是否正确

2. **数据显示异常**
   - 刷新页面重试
   - 检查Excel中的数据类型
   - 确保没有合并单元格

3. **模板下载失败**
   - 检查网络连接
   - 确认已登录系统
   - 尝试刷新页面

### 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. 文件格式和内容
3. 系统日志（管理员）

## 🚀 优势特点

- **批量操作**: 一次导入大量数据
- **格式统一**: 标准化的Excel模板
- **错误提示**: 详细的验证反馈
- **数据校验**: 自动检查数据完整性
- **用户友好**: 简单的操作界面

使用这些功能可以大大提高监考安排的效率，减少手动输入的工作量！ 