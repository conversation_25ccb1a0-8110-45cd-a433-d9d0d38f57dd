#!/usr/bin/env python3
"""
验证修复后的Excel文件
"""

import sys
import os
import pandas as pd

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_file_format():
    """检查文件格式"""
    test_file = 'test_fixed_format.xlsx'
    
    if not os.path.exists(test_file):
        print(f"文件 {test_file} 不存在")
        return False
    
    print(f"检查文件: {test_file}")
    
    try:
        # 检查监考员设置表
        df_proctors = pd.read_excel(test_file, sheet_name='监考员设置')
        print("\n=== 监考员设置表 ===")
        print("列名:", list(df_proctors.columns))
        print("数据:")
        print(df_proctors.to_string())
        
        # 检查必需的列
        required_columns = ['序号', '监考老师', '任教科目', '场次限制']
        missing = [col for col in required_columns if col not in df_proctors.columns]
        
        if missing:
            print(f"\n❌ 监考员设置表缺少列: {missing}")
            return False
        else:
            print("\n✅ 监考员设置表包含所有必需的列")
        
        # 检查数据完整性
        print("\n数据完整性检查:")
        all_valid = True
        for i, row in df_proctors.iterrows():
            序号 = row['序号']
            监考老师 = row['监考老师']
            场次限制 = row['场次限制']
            
            issues = []
            if pd.isna(序号):
                issues.append("序号为空")
            if pd.isna(监考老师) or str(监考老师).strip() == '':
                issues.append("监考老师为空")
            if pd.isna(场次限制):
                issues.append("场次限制为空")
            elif not isinstance(场次限制, (int, float)) or 场次限制 <= 0:
                issues.append("场次限制无效")
                
            if issues:
                print(f"  第{i+1}行问题: {', '.join(issues)}")
                all_valid = False
            else:
                print(f"  第{i+1}行: ✅ 数据完整")
        
        if not all_valid:
            return False
        
        # 检查考试科目设置表
        df_subjects = pd.read_excel(test_file, sheet_name='考试科目设置')
        print("\n=== 考试科目设置表 ===")
        print("列名:", list(df_subjects.columns))
        
        required_subject_columns = ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
        missing_subject = [col for col in required_subject_columns if col not in df_subjects.columns]
        
        if missing_subject:
            print(f"❌ 考试科目设置表缺少列: {missing_subject}")
            return False
        else:
            print("✅ 考试科目设置表包含所有必需的列")
        
        print("\n✅ 文件格式检查通过")
        return True
        
    except Exception as e:
        print(f"检查文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_validator():
    """使用验证器测试"""
    test_file = 'test_fixed_format.xlsx'
    
    try:
        from excel_validator import ExcelValidator
        
        print(f"\n使用验证器测试文件: {test_file}")
        validator = ExcelValidator(test_file)
        is_valid = validator.validate()
        
        if is_valid:
            print("✅ 验证器测试通过")
        else:
            print("❌ 验证器测试失败")
        
        if validator.errors:
            print("\n错误信息:")
            for error in validator.errors:
                print(f"  - {error}")
        
        if validator.warnings:
            print("\n警告信息:")
            for warning in validator.warnings:
                print(f"  - {warning}")
        
        return is_valid
        
    except Exception as e:
        print(f"验证器测试时出错: {e}")
        return False

def main():
    print("开始验证修复后的Excel文件...")
    
    # 1. 检查文件格式
    format_ok = check_file_format()
    
    # 2. 使用验证器测试
    validator_ok = test_with_validator()
    
    print("\n" + "="*50)
    if format_ok and validator_ok:
        print("🎉 所有测试通过！修复成功！")
    else:
        print("❌ 还有问题需要解决")
        if not format_ok:
            print("  - 文件格式检查失败")
        if not validator_ok:
            print("  - 验证器测试失败")

if __name__ == '__main__':
    main()
