#!/usr/bin/env python3
"""
测试科目页面重复导入问题修复
验证是否还有多个Excel导入实现导致重复弹窗
"""

import os
import re

def analyze_subjects_page():
    """分析科目页面的Excel导入实现"""
    print("🔍 分析科目页面Excel导入实现...")
    
    template_file = 'templates/wizard/step1_subjects.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各种Excel导入相关的模式
        patterns = {
            'Excel导入按钮点击事件': [
                r'#import-excel-btn.*\.click',
                r'import-excel-btn.*addEventListener',
                r'import-excel-btn.*onclick'
            ],
            '文件输入创建': [
                r'document\.createElement\([\'"]input[\'"]\)',
                r'input\.type\s*=\s*[\'"]file[\'"]'
            ],
            '导入函数定义': [
                r'function\s+\w*[Ii]mport\w*',
                r'function\s+handle\w*[Ff]ile\w*'
            ],
            '初始化函数调用': [
                r'initExcelImportComponent\(\)',
                r'init\w*[Ii]mport\w*\(\)'
            ],
            '独立脚本引用': [
                r'subjects-excel-import\.js',
                r'excel-import.*\.js'
            ]
        }
        
        results = {}
        total_matches = 0
        
        for category, pattern_list in patterns.items():
            matches = []
            for pattern in pattern_list:
                found = re.findall(pattern, content, re.IGNORECASE)
                matches.extend(found)
            
            results[category] = len(matches)
            total_matches += len(matches)
            
            print(f"\n--- {category} ---")
            print(f"匹配数量: {len(matches)}")
            if matches:
                for i, match in enumerate(matches[:3]):  # 只显示前3个
                    print(f"  {i+1}. {match}")
                if len(matches) > 3:
                    print(f"  ... 还有 {len(matches) - 3} 个匹配")
        
        print(f"\n=== 总结 ===")
        print(f"总匹配数: {total_matches}")
        
        # 判断是否有重复实现
        has_duplicates = False
        warning_patterns = [
            ('Excel导入按钮点击事件', 1),  # 应该只有1个
            ('导入函数定义', 1),  # 应该只有1个
            ('初始化函数调用', 0),  # 应该没有（已删除）
            ('独立脚本引用', 0)   # 应该没有（已删除）
        ]
        
        for category, expected_count in warning_patterns:
            actual_count = results.get(category, 0)
            if actual_count > expected_count:
                print(f"⚠️  {category}: 期望{expected_count}个，实际{actual_count}个")
                has_duplicates = True
            elif actual_count == expected_count:
                print(f"✅ {category}: {actual_count}个（正常）")
            else:
                print(f"❌ {category}: 期望{expected_count}个，实际{actual_count}个")
        
        return not has_duplicates
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def compare_with_rooms_page():
    """与考场页面进行对比"""
    print("\n🔍 与考场页面对比...")
    
    try:
        # 读取两个页面
        with open('templates/wizard/step1_subjects.html', 'r', encoding='utf-8') as f:
            subjects_content = f.read()
        
        with open('templates/wizard/step2_rooms.html', 'r', encoding='utf-8') as f:
            rooms_content = f.read()
        
        # 检查关键实现模式
        patterns = [
            ('动态文件输入创建', r'document\.createElement\([\'"]input[\'"]\)'),
            ('文件选择回调', r'input\.onchange\s*='),
            ('Ajax请求', r'\$\.ajax\({'),
            ('CSRF令牌设置', r'csrf_token'),
            ('成功处理', r'response\.success'),
            ('错误处理', r'error.*function')
        ]
        
        all_consistent = True
        
        for description, pattern in patterns:
            subjects_matches = len(re.findall(pattern, subjects_content, re.IGNORECASE))
            rooms_matches = len(re.findall(pattern, rooms_content, re.IGNORECASE))
            
            if subjects_matches == rooms_matches:
                print(f"✅ {description}: 科目{subjects_matches}个，考场{rooms_matches}个（一致）")
            else:
                print(f"⚠️  {description}: 科目{subjects_matches}个，考场{rooms_matches}个（不一致）")
                if subjects_matches > rooms_matches:
                    all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def check_button_id_consistency():
    """检查按钮ID一致性"""
    print("\n🔍 检查按钮ID一致性...")
    
    template_file = 'templates/wizard/step1_subjects.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找按钮定义
        button_pattern = r'id=[\'"]import-excel-btn-subjects[\'"]'
        button_matches = re.findall(button_pattern, content)
        
        # 查找JavaScript中的按钮引用
        js_pattern = r'#import-excel-btn-subjects|import-excel-btn-subjects'
        js_matches = re.findall(js_pattern, content)
        
        print(f"HTML按钮定义: {len(button_matches)}个")
        print(f"JavaScript引用: {len(js_matches)}个")
        
        if len(button_matches) == 1 and len(js_matches) >= 1:
            print("✅ 按钮ID一致性正常")
            return True
        else:
            print("❌ 按钮ID一致性异常")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 科目页面重复导入问题修复验证")
    print("=" * 50)
    print("检查是否还有多个Excel导入实现导致重复弹窗")
    print("=" * 50)
    
    test_results = []
    
    # 1. 分析科目页面实现
    test_results.append(("科目页面实现分析", analyze_subjects_page()))
    
    # 2. 与考场页面对比
    test_results.append(("与考场页面对比", compare_with_rooms_page()))
    
    # 3. 检查按钮ID一致性
    test_results.append(("按钮ID一致性", check_button_id_consistency()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 验证结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项验证通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有验证通过！")
        print("重复导入问题已修复，现在只有一个简洁的Excel导入实现。")
        print("\n📋 修复内容:")
        print("✅ 删除了独立的Excel导入脚本文件引用")
        print("✅ 删除了重复的initExcelImportComponent函数")
        print("✅ 删除了页面加载后的重复初始化代码")
        print("✅ 保留了一个简洁的jQuery实现，参考step2_rooms")
        print("✅ 确保按钮ID和JavaScript引用一致")
        return 0
    else:
        print("\n❌ 部分验证失败，可能仍有重复实现。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
