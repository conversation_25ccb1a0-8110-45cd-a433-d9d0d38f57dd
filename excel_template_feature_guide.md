# Excel模板下载和导入功能使用指南

## 🎯 功能概述

监考信息设置向导现已完全支持Excel模板下载和批量导入功能，让您可以更高效地设置监考信息：

1. **下载标准模板** - 获取符合系统要求的Excel模板文件
2. **填写监考数据** - 在Excel中批量编辑监考信息
3. **一键批量导入** - 将Excel数据快速导入到系统中

## 📋 三个向导步骤

### 第一步：科目设置 (http://localhost:5000/wizard/step1_subjects)

**模板文件**: `kemu.xlsx` (科目设置模板)
**参考工作簿**: "监考安排.xlsx" 的 "考试科目设置" 工作簿

**操作步骤**:
1. 点击 **"下载导入模板"** 按钮下载科目设置模板
2. 在Excel中填写科目信息：
   - **课程代码**: 如 A、B、C 等
   - **课程名称**: 如语文、数学、英语等  
   - **开始时间**: 格式为 `2025-02-08 09:00:00`
   - **结束时间**: 格式为 `2025-02-08 11:30:00`
3. 保存Excel文件
4. 点击 **"从Excel导入"** 按钮选择并上传文件
5. 系统自动解析并导入科目数据到表格中

**示例数据**:
```
课程代码 | 课程名称 | 开始时间              | 结束时间
A       | 语文     | 2025-02-08 09:00:00  | 2025-02-08 11:30:00
B       | 数学     | 2025-02-08 14:00:00  | 2025-02-08 16:00:00
C       | 英语     | 2025-02-08 16:30:00  | 2025-02-08 18:30:00
```

### 第二步：考场设置 (http://localhost:5000/wizard/step2_rooms)

**模板文件**: `kaochang.xlsx` (考场设置模板)
**参考工作簿**: "监考安排.xlsx" 的 "考场设置" 工作簿

**操作步骤**:
1. 点击 **"下载导入模板"** 按钮下载考场设置模板
2. 在Excel中填写考场信息：
   - **考场**: 考场名称，如 1考场、2考场等
   - **各科目列**: 每个科目需要的监考员数量
3. 保存Excel文件
4. 点击 **"从Excel导入"** 按钮上传文件
5. 系统自动解析并导入考场需求数据

**示例数据**:
```
考场   | 语文 | 数学 | 英语
1考场  | 2    | 2    | 2
2考场  | 2    | 2    | 2
3考场  | 2    | 2    | 2
```

### 第三步：监考员设置 (http://localhost:5000/wizard/step3_proctors)

**模板文件**: `jiankaoyuan.xlsx` (监考员设置模板)
**参考工作簿**: "监考安排.xlsx" 的 "监考员设置" 工作簿

**操作步骤**:
1. 点击 **"下载模板"** 按钮下载监考员设置模板
2. 在Excel中填写监考员信息：
   - **监考老师**: 监考员姓名
   - **任教科目**: 老师任教的科目
   - **必监考科目**: 必须监考的科目(可选)
   - **不监考科目**: 不能监考的科目(可选)
   - **必监考考场**: 必须监考的考场(可选)
   - **不监考考场**: 不能监考的考场(可选)
   - **场次限制**: 最大监考场次数
3. 保存Excel文件
4. 在弹出的导入对话框中选择Excel文件
5. 点击 **"导入数据"** 按钮完成导入

**示例数据**:
```
监考老师 | 任教科目 | 必监考科目 | 不监考科目 | 场次限制
张老师   | 语文     |           |           | 5
李老师   | 数学     |           |           | 5
王老师   | 英语     |           |           | 5
```

## 🔧 技术实现

### 后端路由

1. **模板下载路由**:
   ```python
   @app.route('/wizard/download-template/<template_type>')
   @login_required
   def wizard_download_template(template_type):
   ```

2. **Excel导入路由**:
   ```python
   @app.route('/wizard/import-excel/<step_type>', methods=['POST'])
   @login_required  
   def wizard_import_excel(step_type):
   ```

### 导入函数

- `import_subjects_from_excel()` - 解析科目数据
- `import_rooms_from_excel()` - 解析考场数据  
- `import_proctors_from_excel()` - 解析监考员数据

### 前端功能

- **动态文件输入**: 使用动态创建的文件输入元素确保跨浏览器兼容性
- **CSRF保护**: 所有上传请求都包含CSRF令牌
- **文件验证**: 检查文件类型(.xlsx/.xls)和大小(最大5MB)
- **实时反馈**: 上传进度提示和成功/错误消息
- **数据更新**: 导入成功后自动更新页面表格数据

## 📁 模板文件位置

所有模板文件位于 `/template-guide/` 目录下：

- `kemu.xlsx` - 科目设置模板
- `kaochang.xlsx` - 考场设置模板
- `jiankaoyuan.xlsx` - 监考员设置模板

这些模板文件是从 "监考安排.xlsx" 的对应工作簿中提取的标准格式。

## ✅ 功能特点

1. **标准化格式**: 模板文件严格按照系统要求设计
2. **批量操作**: 支持一次性导入大量数据
3. **数据验证**: 自动验证数据格式和完整性
4. **错误处理**: 详细的错误提示和处理机制
5. **用户友好**: 直观的操作界面和清晰的提示信息
6. **安全可靠**: CSRF保护和文件类型验证

## 🚀 使用建议

1. **先下载模板**: 始终使用系统提供的最新模板文件
2. **保持格式**: 不要修改模板的列名和工作表名称
3. **数据完整**: 确保必填字段都有数据
4. **时间格式**: 科目时间请使用标准格式 `YYYY-MM-DD HH:MM:SS`
5. **文件大小**: 保持Excel文件在5MB以内
6. **逐步导入**: 建议按科目→考场→监考员的顺序依次导入

通过这套完整的Excel模板下载和导入功能，您可以大大提高监考信息设置的效率！
