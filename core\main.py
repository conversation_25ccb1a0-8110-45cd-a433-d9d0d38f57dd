import os
from datetime import datetime
from logger import Logger, RunMode
from File_Processing import ExamConfigProcessor
from core import EnhancedProctorScheduler

def process_exam_config(logger, template_path):
    """处理考试配置文件"""
    processor = ExamConfigProcessor(template_path)
    if not processor.read_template_file():
        logger.error("无法读取模板文件")
        return None
        
    processor.process_proctor_settings()
    logger.data(f"监考员总数: {len(processor.proctor_df)}")
    
    processor.calculate_exam_duration()
    logger.data(f"考试科目数: {len(processor.subject_df)}")
    
    processor.create_room_setting_with_summary()
    logger.data(f"考场数量: {len(processor.room_df)}")
    
    processor.export_json_results('考试配置处理结果.json')
    logger.debug("配置文件导出完成")
    
    return processor

def schedule_proctors(logger):
    """生成监考排班方案"""
    scheduler = EnhancedProctorScheduler()
    logger.debug("调度器初始化完成")
    
    scheduler.build_enhanced_model()
    logger.debug("调度模型构建完成")
    
    scheduler.solve_enhanced_model()
    scheduler.export_enhanced_results()
    
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"监考安排_{current_time}.xlsx"
    logger.result(f"结果已保存到: {output_file}")
    
    return scheduler

def main():
    """主函数：组织业务流程"""
    # 初始化日志系统
    mode = os.getenv('EXAM_MODE', 'production')
    logger = Logger(mode)
    logger.system("监考安排自动化系统")
    
    try:
        # 1. 检查配置文件
        template_path = "Exam_Config.xlsx"
        if not os.path.exists(template_path):
            logger.error(f"找不到模板文件 {template_path}")
            return
            
        # 2. 处理考试配置
        logger.progress("处理考试配置...", step=1)
        processor = process_exam_config(logger, template_path)
        if not processor:
            return
            
        # 3. 生成排班方案
        logger.progress("生成排班方案...", step=2)
        scheduler = schedule_proctors(logger)
        
        # 4. 完成
        logger.progress("完成", step=3)
        logger.complete()
        
    except Exception as e:
        logger.error(str(e))
        raise

if __name__ == "__main__":
    main() 