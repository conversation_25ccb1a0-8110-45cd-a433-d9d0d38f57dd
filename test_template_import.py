#!/usr/bin/env python3
"""
测试模板文件导入功能
验证三个模板文件是否能正确导入
"""

import sys
import os
sys.path.append('.')

def test_template_imports():
    """测试模板文件导入"""
    print("🧪 测试模板文件导入功能...")
    
    try:
        from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
        print("✅ 成功导入应用模块")
    except ImportError as e:
        print(f"❌ 无法导入应用模块: {e}")
        return False
    
    template_files = {
        'subjects': ('template-guide/kemu.xlsx', import_subjects_from_excel, '科目'),
        'rooms': ('template-guide/kaochang.xlsx', import_rooms_from_excel, '考场'),
        'proctors': ('template-guide/jiankaoyuan.xlsx', import_proctors_from_excel, '监考员')
    }
    
    success_count = 0
    total_count = len(template_files)
    
    for step_type, (file_path, import_func, description) in template_files.items():
        print(f"\n=== 测试{description}模板导入 ===")
        print(f"文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ 模板文件不存在: {file_path}")
            continue
        
        try:
            # 测试导入
            data = import_func(file_path)
            print(f"✅ {description}导入成功")
            print(f"   导入数据数量: {len(data)}")
            
            # 显示前3条数据
            for i, item in enumerate(data[:3]):
                print(f"   {i+1}. {item}")
            
            if len(data) > 3:
                print(f"   ... 还有 {len(data) - 3} 条数据")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ {description}导入失败: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有模板文件导入测试通过！")
        return True
    else:
        print("❌ 部分模板文件导入测试失败")
        return False

def test_template_structure():
    """测试模板文件结构"""
    print("\n🔍 检查模板文件结构...")
    
    import pandas as pd
    
    template_files = {
        'kemu.xlsx': '科目设置模板',
        'kaochang.xlsx': '考场设置模板',
        'jiankaoyuan.xlsx': '监考员设置模板'
    }
    
    for filename, description in template_files.items():
        filepath = f'template-guide/{filename}'
        print(f"\n--- {description} ({filename}) ---")
        
        if not os.path.exists(filepath):
            print(f"❌ 文件不存在: {filepath}")
            continue
        
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(filepath)
            print(f"工作表: {excel_file.sheet_names}")
            
            # 读取第一个工作表
            df = pd.read_excel(filepath, sheet_name=excel_file.sheet_names[0])
            print(f"列名: {list(df.columns)}")
            print(f"数据行数: {len(df)}")
            
            # 检查是否有数据
            if len(df) > 0:
                print("✅ 模板包含示例数据")
            else:
                print("⚠️  模板为空，只有列头")
                
        except Exception as e:
            print(f"❌ 读取失败: {e}")

def main():
    """主函数"""
    print("📋 模板文件导入功能测试")
    print("=" * 50)
    
    # 测试模板文件结构
    test_template_structure()
    
    # 测试导入功能
    success = test_template_imports()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过，模板导入功能正常")
        return 0
    else:
        print("❌ 部分测试失败，需要检查模板文件或导入函数")
        return 1

if __name__ == "__main__":
    sys.exit(main())
