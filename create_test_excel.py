#!/usr/bin/env python3
"""
创建测试用的监考员Excel文件
"""

import pandas as pd

def create_test_excel():
    """创建测试用的监考员Excel文件"""
    
    # 创建测试数据
    test_data = [
        {
            '监考老师': '张三',
            '任教科目': '语文',
            '必监考科目': '语文,数学',
            '不监考科目': '英语,物理',
            '必监考考场': '1考场,2考场',
            '不监考考场': '10考场,11考场',
            '场次限制': 5
        },
        {
            '监考老师': '李四',
            '任教科目': '数学',
            '必监考科目': '数学,物理',
            '不监考科目': '语文,化学',
            '必监考考场': '3考场,4考场',
            '不监考考场': '12考场,13考场',
            '场次限制': 3
        },
        {
            '监考老师': '王五',
            '任教科目': '英语',
            '必监考科目': '英语',
            '不监考科目': '物理,化学',
            '必监考考场': '5考场',
            '不监考考场': '14考场,15考场',
            '场次限制': 4
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    output_file = 'test_proctors.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ 测试Excel文件已创建: {output_file}")
    print(f"📊 包含 {len(test_data)} 条监考员记录")
    
    # 显示数据预览
    print("\n📋 数据预览:")
    for i, row in df.iterrows():
        print(f"  {i+1}. {row['监考老师']} - {row['任教科目']} - 场次限制: {row['场次限制']}")
    
    return output_file

if __name__ == "__main__":
    create_test_excel()
