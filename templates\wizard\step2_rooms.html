{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    .table-responsive {
        max-height: 60vh;
        overflow: auto;
    }
    thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 3;
    }
    tbody th {
        position: sticky;
        left: 0;
        background-color: #f8f9fa;
        z-index: 2;
    }
    thead th:first-child {
        left: 0;
        z-index: 4;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第二步：考场与需求设置</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <i class="fas fa-info-circle fa-lg me-3 mt-1"></i>
                            <div class="flex-grow-1">
                                <h6 class="alert-heading mb-2">使用提示</h6>
                                <div class="row small">
                                    <div class="col-md-4 mb-2">
                                        <span class="text-primary fw-bold"><i class="fas fa-plus-circle me-1"></i>添加：</span>
                                        单个添加、批量添加、Excel导入
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <span class="text-warning fw-bold"><i class="fas fa-edit me-1"></i>编辑：</span>
                                        单个编辑、批量编辑、监考人数设置
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <span class="text-danger fw-bold"><i class="fas fa-trash-alt me-1"></i>删除：</span>
                                        单个删除、批量删除、全选快捷操作
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step2_rooms') }}" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="rooms_data" name="rooms_data">
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <button type="button" id="import-excel-btn-rooms" class="btn btn-outline-primary">
                                    <i class="fas fa-file-excel me-2"></i>从Excel导入
                                </button>
                                <a href="{{ url_for('wizard_download_template', template_type='rooms') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-download me-2"></i>下载考场设置模板
                                </a>
                                <div class="flex-grow-1"></div>
                                <button type="button" id="add-room-btn" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>添加考场
                                </button>
                                <button type="button" id="batch-add-room-btn" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#batchAddModal">
                                    <i class="fas fa-layer-group me-2"></i>批量添加
                                </button>
                                <button type="button" id="batch-edit-room-btn" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#batchEditModal" disabled>
                                    <i class="fas fa-edit me-2"></i>批量编辑
                                </button>
                                <button type="button" id="batch-delete-room-btn" class="btn btn-danger" disabled>
                                    <i class="fas fa-trash-alt me-2"></i>批量删除
                                </button>
                            </div>
                            <div id="import-result-rooms" class="mt-2"></div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="rooms-table">
                                <thead>
                                    <tr>
                                        <th style="width: 5%;">
                                            <input type="checkbox" id="select-all-rooms" class="form-check-input">
                                        </th>
                                        <th style="width: 20%;">考场名称</th>
                                        {% for subject in wizard_data.get('subjects', []) %}
                                        <th>{{ subject.subject_name }} (监考人数)</th>
                                        {% endfor %}
                                        <th style="width: 5%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rooms will be dynamically added here -->
                                </tbody>
                            </table>
                        </div>


                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step1_subjects') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="room-template">
    <tr>
        <td style="vertical-align: middle;">
            <input type="checkbox" class="form-check-input room-checkbox">
        </td>
        <td><input type="text" name="room_name" class="form-control" required></td>
        {% for subject in wizard_data.get('subjects', []) %}
        <td><input type="number" name="demand_{{ subject.subject_name }}" class="form-control" min="0" placeholder="数量"></td>
        {% endfor %}
        <td style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm remove-room-btn"><i class="fas fa-trash"></i></button></td>
    </tr>
</template>

<!-- 批量添加考场模态框 -->
<div class="modal fade" id="batchAddModal" tabindex="-1" aria-labelledby="batchAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchAddModalLabel">
                    <i class="fas fa-layer-group me-2"></i>批量添加考场
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batch-add-form">
                    <div class="mb-3">
                        <label for="room-count" class="form-label">
                            <i class="fas fa-hashtag me-1"></i>添加考场数量
                        </label>
                        <input type="number" class="form-control" id="room-count" min="1" max="50" value="5" required>
                        <div class="form-text">一次最多可添加50个考场</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>监考人数设置
                        </label>
                        {% for subject in wizard_data.get('subjects', []) %}
                        <div class="row mb-2">
                            <div class="col-6">
                                <label class="form-label small">{{ subject.subject_name }}</label>
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm default-demand"
                                       data-subject="{{ subject.subject_name }}" min="0" value="2" required>
                            </div>
                        </div>
                        {% endfor %}
                        <div class="form-text">为每个科目设置监考员数量</div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>预览：</strong>
                        <span id="preview-text">将添加 5 个考场</span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirm-batch-add">
                    <i class="fas fa-check me-2"></i>确认添加
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量编辑考场人数模态框 -->
<div class="modal fade" id="batchEditModal" tabindex="-1" aria-labelledby="batchEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchEditModalLabel">
                    <i class="fas fa-edit me-2"></i>批量编辑监考人数
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    已选择 <span id="selected-rooms-count">0</span> 个考场
                </div>

                <form id="batch-edit-form">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>监考人数设置
                        </label>
                        {% for subject in wizard_data.get('subjects', []) %}
                        <div class="row mb-2">
                            <div class="col-6">
                                <label class="form-label small">{{ subject.subject_name }}</label>
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm batch-edit-demand"
                                       data-subject="{{ subject.subject_name }}" min="0" value="" placeholder="保持不变">
                            </div>
                        </div>
                        {% endfor %}
                        <div class="form-text">留空表示保持该科目的监考人数不变</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="confirm-batch-edit">
                    <i class="fas fa-check me-2"></i>确认编辑
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    var wizardData = {{ wizard_data | tojson | safe }};
    var rooms = wizardData.rooms || [];
    var subjects = wizardData.subjects || [];
    var tableBody = $('#rooms-table tbody');

    function collectDataFromDOM() {
        var collectedRooms = [];
        $('#rooms-table tbody tr').each(function() {
            var roomName = $(this).find('input[name="room_name"]').val().trim();
            var demands = {};
            $(this).find('input[type="number"]').each(function() {
                var subjectName = $(this).attr('name').replace('demand_', '');
                demands[subjectName] = $(this).val();
            });
            collectedRooms.push({
                name: roomName,
                demands: demands
            });
        });
        return collectedRooms;
    }

    function renderRooms() {
        tableBody.empty();
        rooms.forEach(function(room) {
            var roomEl = $('#room-template').html();
            var $roomEl = $(roomEl);
            $roomEl.find('input[name="room_name"]').val(room.name || '');
            
            subjects.forEach(function(subject) {
                var demandValue = (room.demands && room.demands[subject.subject_name]) ? room.demands[subject.subject_name] : '';
                $roomEl.find('input[name="demand_' + subject.subject_name + '"]').val(demandValue);
            });

            tableBody.append($roomEl);
        });
    }



    // 通用Excel导入组件JavaScript
    function initExcelImportComponent() {
        const componentId = 'rooms';
        const importType = 'rooms';
        const importUrl = '/wizard/import-excel/' + importType;
        
        console.log(`初始化Excel导入组件: ${componentId}, 类型: ${importType}`);
        
        const importBtn = document.getElementById(`import-excel-btn-${componentId}`);
        const resultArea = document.getElementById(`import-result-${componentId}`);
        
        if (!importBtn || !resultArea) {
            console.error('Excel导入组件元素未找到');
            return;
        }
        
        // 导入按钮点击事件
        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`${componentId}: Excel导入按钮被点击`);
            
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.style.display = 'none';
            
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) {
                    console.log(`${componentId}: 没有选择文件`);
                    return;
                }
                
                console.log(`${componentId}: 文件已选择:`, file.name, file.size, 'bytes');
                handleFileUpload(file);
                
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };
            
            document.body.appendChild(input);
            input.click();
        });
        
        function handleFileUpload(file) {
            console.log(`${componentId}: 开始处理文件:`, file.name);
            
            const allowedTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                showImportMessage('请上传Excel文件（.xls或.xlsx格式）', 'danger');
                return;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                showImportMessage('文件大小不能超过5MB', 'danger');
                return;
            }
            
            const csrfToken = document.querySelector('input[name=csrf_token]')?.value;
            if (!csrfToken) {
                showImportMessage('CSRF令牌未找到，请刷新页面重试', 'danger');
                return;
            }
            
            showLoadingMessage();
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('csrf_token', csrfToken);
            
            fetch(importUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log(`${componentId}: 服务器响应状态:`, response.status);
                return response.json();
            })
            .then(data => {
                console.log(`${componentId}: 导入成功:`, data);
                hideLoadingMessage();
                
                if (data.success) {
                    showImportMessage(data.message || '导入成功', 'success');
                    
                    // 更新考场数据
                    rooms = data.data;
                    renderRooms();
                    
                    setTimeout(() => {
                        hideImportMessage();
                    }, 5000);
                } else {
                    showImportMessage(data.message || '导入失败', 'danger');
                }
            })
            .catch(error => {
                console.error(`${componentId}: 导入失败:`, error);
                hideLoadingMessage();
                
                let errorMessage = '导入失败，请稍后重试';
                if (error.message.includes('403')) {
                    errorMessage = '权限不足或CSRF验证失败，请刷新页面重试';
                } else if (error.message.includes('413')) {
                    errorMessage = '文件过大，请选择较小的文件';
                } else if (error.message.includes('500')) {
                    errorMessage = '服务器内部错误，请联系管理员';
                }
                
                showImportMessage(errorMessage, 'danger');
            });
        }
        
        function showLoadingMessage() {
            resultArea.innerHTML = `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideLoadingMessage() {
            const loadingAlert = resultArea.querySelector('.alert-info');
            if (loadingAlert) {
                loadingAlert.remove();
            }
        }
        
        function showImportMessage(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const iconClass = type === 'success' ? 'fa-check-circle' : 
                             type === 'danger' ? 'fa-exclamation-circle' : 
                             'fa-info-circle';
            
            resultArea.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${iconClass} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideImportMessage() {
            resultArea.innerHTML = '';
        }
        
        console.log(`Excel导入组件 ${componentId} 初始化完成`);
    }

    $('#add-room-btn').click(function() {
        rooms = collectDataFromDOM();
        var nextRoomNumber = rooms.length + 1;
        rooms.push({ name: nextRoomNumber + '考场', demands: {} });
        renderRooms();
    });

    // 批量添加考场功能
    function updateBatchAddPreview() {
        var count = parseInt($('#room-count').val()) || 0;

        if (count <= 0) {
            $('#preview-text').text('请输入有效的考场数量');
            return;
        }

        $('#preview-text').text('将添加 ' + count + ' 个考场');
    }

    // 监听批量添加表单变化
    $('#room-count').on('input change', updateBatchAddPreview);

    // 初始化预览
    updateBatchAddPreview();

    // 确认批量添加
    $('#confirm-batch-add').click(function() {
        var count = parseInt($('#room-count').val()) || 0;

        if (count <= 0 || count > 50) {
            alert('请输入有效的考场数量（1-50）');
            return;
        }

        // 收集当前数据
        rooms = collectDataFromDOM();

        // 收集监考人数设置
        var defaultDemands = {};
        $('.default-demand').each(function() {
            var subject = $(this).data('subject');
            var demand = parseInt($(this).val()) || 0;
            defaultDemands[subject] = demand;
        });

        // 计算下一个考场编号
        var nextRoomNumber = rooms.length + 1;

        // 批量添加考场
        for (var i = 0; i < count; i++) {
            var roomName = (nextRoomNumber + i) + '考场';
            rooms.push({
                name: roomName,
                demands: Object.assign({}, defaultDemands)  // 复制监考需求
            });
        }

        // 重新渲染表格
        renderRooms();

        // 关闭模态框
        $('#batchAddModal').modal('hide');

        // 显示成功消息
        var successAlert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
            .html(`
                <i class="fas fa-check-circle me-2"></i>成功批量添加了 ${count} 个考场
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `);
        $('.card-body').prepend(successAlert);

        // 5秒后自动关闭提示
        setTimeout(() => {
            successAlert.alert('close');
        }, 5000);
    });

    $(document).on('click', '.remove-room-btn', function() {
        rooms = collectDataFromDOM();
        var index = $(this).closest('tr').index();
        rooms.splice(index, 1);
        renderRooms();
    });

    $('#wizard-form').submit(function(e) {
        var finalRooms = collectDataFromDOM().filter(function(r) { return r.name; });
        
        if (finalRooms.length === 0) {
            e.preventDefault();
            alert('请至少设置一个考场。');
            return;
        }

        $('#rooms_data').val(JSON.stringify(finalRooms));
    });

    // Initial render
    renderRooms();
    
    // 批量操作功能

    // 全选/取消全选
    $('#select-all-rooms').change(function() {
        var isChecked = $(this).is(':checked');
        $('.room-checkbox').prop('checked', isChecked);
        updateBatchButtonsState();
    });

    // 监听单个复选框变化
    $(document).on('change', '.room-checkbox', function() {
        updateBatchButtonsState();

        // 更新全选复选框状态
        var totalCheckboxes = $('.room-checkbox').length;
        var checkedCheckboxes = $('.room-checkbox:checked').length;

        if (checkedCheckboxes === 0) {
            $('#select-all-rooms').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $('#select-all-rooms').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#select-all-rooms').prop('indeterminate', true);
        }
    });

    // 更新批量操作按钮状态
    function updateBatchButtonsState() {
        var selectedCount = $('.room-checkbox:checked').length;
        var hasSelection = selectedCount > 0;

        $('#batch-edit-room-btn').prop('disabled', !hasSelection);
        $('#batch-delete-room-btn').prop('disabled', !hasSelection);

        // 更新批量编辑模态框中的选择数量
        $('#selected-rooms-count').text(selectedCount);
    }

    // 批量删除确认
    $('#batch-delete-room-btn').click(function() {
        var selectedCount = $('.room-checkbox:checked').length;

        if (selectedCount === 0) {
            alert('请先选择要删除的考场');
            return;
        }

        if (confirm(`确定要删除选中的 ${selectedCount} 个考场吗？此操作不可撤销。`)) {
            // 收集当前数据
            rooms = collectDataFromDOM();

            // 获取选中的行索引（从后往前删除，避免索引变化）
            var selectedIndices = [];
            $('.room-checkbox:checked').each(function() {
                var rowIndex = $(this).closest('tr').index();
                selectedIndices.push(rowIndex);
            });

            // 从后往前删除
            selectedIndices.sort(function(a, b) { return b - a; });
            selectedIndices.forEach(function(index) {
                rooms.splice(index, 1);
            });

            // 重新渲染表格
            renderRooms();

            // 显示成功消息
            var successAlert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                .html(`
                    <i class="fas fa-check-circle me-2"></i>成功删除了 ${selectedCount} 个考场
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `);
            $('.card-body').prepend(successAlert);

            // 5秒后自动关闭提示
            setTimeout(() => {
                successAlert.alert('close');
            }, 5000);
        }
    });

    // 批量编辑确认
    $('#confirm-batch-edit').click(function() {
        var selectedCount = $('.room-checkbox:checked').length;

        if (selectedCount === 0) {
            alert('请先选择要编辑的考场');
            return;
        }

        // 收集编辑的监考人数
        var editDemands = {};
        var hasChanges = false;

        $('.batch-edit-demand').each(function() {
            var subject = $(this).data('subject');
            var value = $(this).val().trim();

            if (value !== '') {
                var demand = parseInt(value) || 0;
                editDemands[subject] = demand;
                hasChanges = true;
            }
        });

        if (!hasChanges) {
            alert('请至少设置一个科目的监考人数');
            return;
        }

        // 收集当前数据
        rooms = collectDataFromDOM();

        // 获取选中的行索引
        var selectedIndices = [];
        $('.room-checkbox:checked').each(function() {
            var rowIndex = $(this).closest('tr').index();
            selectedIndices.push(rowIndex);
        });

        // 批量更新选中考场的监考人数
        selectedIndices.forEach(function(index) {
            if (rooms[index]) {
                Object.keys(editDemands).forEach(function(subject) {
                    rooms[index].demands[subject] = editDemands[subject];
                });
            }
        });

        // 重新渲染表格
        renderRooms();

        // 关闭模态框
        $('#batchEditModal').modal('hide');

        // 清空编辑表单
        $('.batch-edit-demand').val('');

        // 显示成功消息
        var changedSubjects = Object.keys(editDemands);
        var successAlert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
            .html(`
                <i class="fas fa-check-circle me-2"></i>成功编辑了 ${selectedCount} 个考场的监考人数 (${changedSubjects.join(', ')})
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `);
        $('.card-body').prepend(successAlert);

        // 5秒后自动关闭提示
        setTimeout(() => {
            successAlert.alert('close');
        }, 5000);
    });

    // 初始化Excel导入组件
    initExcelImportComponent();
});
</script>
{% endblock %}