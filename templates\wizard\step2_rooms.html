{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    .table-responsive {
        max-height: 60vh;
        overflow: auto;
    }
    thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 3;
    }
    tbody th {
        position: sticky;
        left: 0;
        background-color: #f8f9fa;
        z-index: 2;
    }
    thead th:first-child {
        left: 0;
        z-index: 4;
    }

    /* 大规模考场显示样式 */
    .rooms-grid-container {
        height: 70vh;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        position: relative;
        overflow: hidden;
        background: white;
    }

    .rooms-toolbar {
        padding: 1rem;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .fixed-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 50px;
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        display: flex;
        z-index: 3;
    }

    .header-corner {
        width: 200px;
        padding: 15px;
        border-right: 1px solid #dee2e6;
        font-weight: bold;
        background: #e9ecef;
        display: flex;
        align-items: center;
    }

    .header-subjects {
        flex: 1;
        display: flex;
        overflow-x: auto;
    }

    .subject-header {
        min-width: 120px;
        max-width: 150px;
        padding: 10px 8px;
        border-right: 1px solid #dee2e6;
        text-align: center;
        font-weight: bold;
        font-size: 0.9rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .scrollable-content {
        position: absolute;
        top: 50px;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
    }

    .fixed-first-column {
        width: 200px;
        background: #f8f9fa;
        border-right: 2px solid #dee2e6;
        overflow-y: auto;
        z-index: 2;
    }

    .room-name-cell {
        height: 45px;
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
        background: white;
        display: flex;
        align-items: center;
        font-size: 0.9rem;
    }

    .room-name-cell:nth-child(even) {
        background: #f8f9fa;
    }

    .data-grid {
        flex: 1;
        overflow: auto;
        position: relative;
    }

    .virtual-row {
        height: 45px;
        display: flex;
        border-bottom: 1px solid #dee2e6;
        position: absolute;
        width: 100%;
    }

    .virtual-row:nth-child(even) {
        background: #f8f9fa;
    }

    .data-cell {
        min-width: 120px;
        max-width: 150px;
        padding: 3px;
        border-right: 1px solid #dee2e6;
        display: flex;
        align-items: center;
    }

    .data-cell input {
        width: 100%;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 6px 8px;
        text-align: center;
        font-size: 0.9rem;
    }

    .data-cell input:focus {
        outline: none;
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .rooms-stats {
        padding: 0.5rem 1rem;
        background: #e9ecef;
        border-top: 1px solid #dee2e6;
        font-size: 0.9rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .display-mode-toggle {
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第二步：考场与需求设置</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <i class="fas fa-info-circle fa-2x me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-2">使用提示</h5>
                                <ul class="mb-0 ps-3">
                                    <li>为每个考场设置名称，并填写各科目需要的监考员数量</li>
                                    <li>可以通过Excel批量导入考场信息</li>
                                    <li>监考员数量通常为2人一个考场，特殊情况可调整</li>
                                    <li>可以添加或删除考场</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step2_rooms') }}" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="rooms_data" name="rooms_data">
                        
                        <!-- 显示模式切换 -->
                        <div class="display-mode-toggle">
                            <div class="btn-group" role="group" aria-label="显示模式">
                                <input type="radio" class="btn-check" name="display-mode" id="grid-mode" value="grid" checked>
                                <label class="btn btn-outline-primary" for="grid-mode">
                                    <i class="fas fa-th me-2"></i>网格模式
                                </label>
                                <input type="radio" class="btn-check" name="display-mode" id="table-mode" value="table">
                                <label class="btn btn-outline-primary" for="table-mode">
                                    <i class="fas fa-table me-2"></i>表格模式
                                </label>
                            </div>
                            <small class="text-muted ms-3">网格模式适合大量数据，表格模式适合少量数据</small>
                        </div>

                        <div class="mb-4">
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <button type="button" id="import-excel-btn-rooms" class="btn btn-outline-primary">
                                    <i class="fas fa-file-excel me-2"></i>从Excel导入
                                </button>
                                <a href="{{ url_for('wizard_download_template', template_type='rooms') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-download me-2"></i>下载考场设置模板
                                </a>
                                <div class="flex-grow-1"></div>
                                <button type="button" id="add-room-btn" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>添加考场
                                </button>
                                <button type="button" id="batch-add-btn" class="btn btn-outline-success">
                                    <i class="fas fa-plus-circle me-2"></i>批量添加
                                </button>
                            </div>
                            <div id="import-result-rooms" class="mt-2"></div>
                        </div>
                        
                        <!-- 网格模式显示 -->
                        <div id="grid-display" class="rooms-grid-container">
                            <!-- 工具栏 -->
                            <div class="rooms-toolbar">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control form-control-sm" placeholder="搜索考场..." id="room-search">
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary btn-sm" id="batch-set-btn">
                                            <i class="fas fa-edit me-2"></i>批量设置
                                        </button>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <span class="badge bg-info" id="rooms-count">0 个考场</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 固定表头 -->
                            <div class="fixed-header">
                                <div class="header-corner">考场名称</div>
                                <div class="header-subjects" id="header-subjects">
                                    <!-- 科目表头将动态生成 -->
                                </div>
                            </div>

                            <!-- 可滚动内容 -->
                            <div class="scrollable-content">
                                <div class="fixed-first-column" id="room-names-column">
                                    <!-- 考场名称列将动态生成 -->
                                </div>
                                <div class="data-grid" id="data-grid">
                                    <!-- 数据网格将动态生成 -->
                                </div>
                            </div>

                            <!-- 统计信息 -->
                            <div class="rooms-stats">
                                <span>总考场数: <strong id="total-rooms">0</strong></span>
                                <span>总监考需求: <strong id="total-demand">0</strong></span>
                                <span>平均每考场: <strong id="avg-demand">0</strong></span>
                            </div>
                        </div>

                        <!-- 传统表格模式显示 -->
                        <div id="table-display" class="table-responsive" style="display: none;">
                            <table class="table table-bordered" id="rooms-table">
                                <thead>
                                    <tr>
                                        <th style="width: 20%;">考场名称</th>
                                        {% for subject in wizard_data.get('subjects', []) %}
                                        <th>{{ subject.subject_name }} (监考人数)</th>
                                        {% endfor %}
                                        <th style="width: 5%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rooms will be dynamically added here -->
                                </tbody>
                            </table>
                        </div>


                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step1_subjects') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="room-template">
    <tr>
        <td><input type="text" name="room_name" class="form-control" required></td>
        {% for subject in wizard_data.get('subjects', []) %}
        <td><input type="number" name="demand_{{ subject.subject_name }}" class="form-control" min="0" placeholder="数量"></td>
        {% endfor %}
        <td style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm remove-room-btn"><i class="fas fa-trash"></i></button></td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/virtual-rooms-grid.js') }}"></script>
<script>
$(document).ready(function() {
    var wizardData = {{ wizard_data | tojson | safe }};
    var rooms = wizardData.rooms || [];
    var subjects = wizardData.subjects || [];

    // 显示模式管理
    var currentDisplayMode = 'grid';
    var virtualGrid = null;
    var tableBody = $('#rooms-table tbody');

    // 初始化虚拟网格
    function initVirtualGrid() {
        const gridContainer = document.getElementById('grid-display');
        if (gridContainer && !virtualGrid) {
            virtualGrid = new VirtualRoomsGrid(gridContainer);
            virtualGrid.setData(rooms, subjects);

            // 绑定数据更新回调
            virtualGrid.onDataUpdate = function(updatedData) {
                rooms = updatedData;
                updateRoomsData();
            };
        }
    }

    // 显示模式切换
    function switchDisplayMode(mode) {
        currentDisplayMode = mode;

        if (mode === 'grid') {
            $('#grid-display').show();
            $('#table-display').hide();
            initVirtualGrid();
        } else {
            $('#grid-display').hide();
            $('#table-display').show();
            renderRooms(); // 刷新表格模式
        }
    }

    // 绑定显示模式切换事件
    $('input[name="display-mode"]').change(function() {
        switchDisplayMode($(this).val());
    });

    // 更新隐藏字段数据
    function updateRoomsData() {
        $('#rooms_data').val(JSON.stringify(rooms));
    }

    function collectDataFromDOM() {
        var collectedRooms = [];
        $('#rooms-table tbody tr').each(function() {
            var roomName = $(this).find('input[name="room_name"]').val().trim();
            var demands = {};
            $(this).find('input[type="number"]').each(function() {
                var subjectName = $(this).attr('name').replace('demand_', '');
                demands[subjectName] = $(this).val();
            });
            collectedRooms.push({
                name: roomName,
                demands: demands
            });
        });
        return collectedRooms;
    }

    function renderRooms() {
        tableBody.empty();
        rooms.forEach(function(room) {
            var roomEl = $('#room-template').html();
            var $roomEl = $(roomEl);
            $roomEl.find('input[name="room_name"]').val(room.name || '');
            
            subjects.forEach(function(subject) {
                var demandValue = (room.demands && room.demands[subject.subject_name]) ? room.demands[subject.subject_name] : '';
                $roomEl.find('input[name="demand_' + subject.subject_name + '"]').val(demandValue);
            });

            tableBody.append($roomEl);
        });
    }



    // 通用Excel导入组件JavaScript
    function initExcelImportComponent() {
        const componentId = 'rooms';
        const importType = 'rooms';
        const importUrl = '/wizard/import-excel/' + importType;
        
        console.log(`初始化Excel导入组件: ${componentId}, 类型: ${importType}`);
        
        const importBtn = document.getElementById(`import-excel-btn-${componentId}`);
        const resultArea = document.getElementById(`import-result-${componentId}`);
        
        if (!importBtn || !resultArea) {
            console.error('Excel导入组件元素未找到');
            return;
        }
        
        // 导入按钮点击事件
        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`${componentId}: Excel导入按钮被点击`);
            
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.style.display = 'none';
            
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) {
                    console.log(`${componentId}: 没有选择文件`);
                    return;
                }
                
                console.log(`${componentId}: 文件已选择:`, file.name, file.size, 'bytes');
                handleFileUpload(file);
                
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };
            
            document.body.appendChild(input);
            input.click();
        });
        
        function handleFileUpload(file) {
            console.log(`${componentId}: 开始处理文件:`, file.name);
            
            const allowedTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                showImportMessage('请上传Excel文件（.xls或.xlsx格式）', 'danger');
                return;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                showImportMessage('文件大小不能超过5MB', 'danger');
                return;
            }
            
            const csrfToken = document.querySelector('input[name=csrf_token]')?.value;
            if (!csrfToken) {
                showImportMessage('CSRF令牌未找到，请刷新页面重试', 'danger');
                return;
            }
            
            showLoadingMessage();
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('csrf_token', csrfToken);
            
            fetch(importUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log(`${componentId}: 服务器响应状态:`, response.status);
                return response.json();
            })
            .then(data => {
                console.log(`${componentId}: 导入成功:`, data);
                hideLoadingMessage();
                
                if (data.success) {
                    showImportMessage(data.message || '导入成功', 'success');
                    
                    // 更新考场数据
                    rooms = data.data;
                    renderRooms();
                    
                    setTimeout(() => {
                        hideImportMessage();
                    }, 5000);
                } else {
                    showImportMessage(data.message || '导入失败', 'danger');
                }
            })
            .catch(error => {
                console.error(`${componentId}: 导入失败:`, error);
                hideLoadingMessage();
                
                let errorMessage = '导入失败，请稍后重试';
                if (error.message.includes('403')) {
                    errorMessage = '权限不足或CSRF验证失败，请刷新页面重试';
                } else if (error.message.includes('413')) {
                    errorMessage = '文件过大，请选择较小的文件';
                } else if (error.message.includes('500')) {
                    errorMessage = '服务器内部错误，请联系管理员';
                }
                
                showImportMessage(errorMessage, 'danger');
            });
        }
        
        function showLoadingMessage() {
            resultArea.innerHTML = `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideLoadingMessage() {
            const loadingAlert = resultArea.querySelector('.alert-info');
            if (loadingAlert) {
                loadingAlert.remove();
            }
        }
        
        function showImportMessage(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const iconClass = type === 'success' ? 'fa-check-circle' : 
                             type === 'danger' ? 'fa-exclamation-circle' : 
                             'fa-info-circle';
            
            resultArea.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${iconClass} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideImportMessage() {
            resultArea.innerHTML = '';
        }
        
        console.log(`Excel导入组件 ${componentId} 初始化完成`);
    }

    $('#add-room-btn').click(function() {
        if (currentDisplayMode === 'grid') {
            var nextRoomNumber = rooms.length + 1;
            var newRoom = { name: nextRoomNumber + '考场', demands: {} };
            rooms.push(newRoom);
            virtualGrid.setData(rooms, subjects);
            updateRoomsData();
        } else {
            rooms = collectDataFromDOM();
            var nextRoomNumber = rooms.length + 1;
            rooms.push({ name: nextRoomNumber + '考场', demands: {} });
            renderRooms();
        }
    });

    // 批量添加考场
    $('#batch-add-btn').click(function() {
        var count = prompt('请输入要添加的考场数量:', '10');
        if (count && !isNaN(count)) {
            count = parseInt(count);
            if (count > 0 && count <= 100) {
                if (currentDisplayMode === 'grid') {
                    for (var i = 0; i < count; i++) {
                        var nextRoomNumber = rooms.length + 1;
                        rooms.push({ name: nextRoomNumber + '考场', demands: {} });
                    }
                    virtualGrid.setData(rooms, subjects);
                    updateRoomsData();
                } else {
                    rooms = collectDataFromDOM();
                    for (var i = 0; i < count; i++) {
                        var nextRoomNumber = rooms.length + 1;
                        rooms.push({ name: nextRoomNumber + '考场', demands: {} });
                    }
                    renderRooms();
                }
            } else {
                alert('请输入1-100之间的数字');
            }
        }
    });

    $(document).on('click', '.remove-room-btn', function() {
        rooms = collectDataFromDOM();
        var index = $(this).closest('tr').index();
        rooms.splice(index, 1);
        renderRooms();
    });

    $('#wizard-form').submit(function(e) {
        var finalRooms = collectDataFromDOM().filter(function(r) { return r.name; });
        
        if (finalRooms.length === 0) {
            e.preventDefault();
            alert('请至少设置一个考场。');
            return;
        }

        $('#rooms_data').val(JSON.stringify(finalRooms));
    });

    // 初始化显示
    function initDisplay() {
        // 根据数据量决定默认显示模式
        if (subjects.length > 6 || rooms.length > 30) {
            $('#grid-mode').prop('checked', true);
            switchDisplayMode('grid');
        } else {
            $('#table-mode').prop('checked', true);
            switchDisplayMode('table');
        }
    }

    // 初始化
    initDisplay();

    // 初始化Excel导入组件
    initExcelImportComponent();
});
</script>
{% endblock %}