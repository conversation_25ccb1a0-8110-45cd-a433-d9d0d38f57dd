{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    .table-responsive {
        max-height: 60vh;
        overflow: auto;
    }
    thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 3;
    }
    tbody th {
        position: sticky;
        left: 0;
        background-color: #f8f9fa;
        z-index: 2;
    }
    thead th:first-child {
        left: 0;
        z-index: 4;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第二步：考场与需求设置</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <i class="fas fa-info-circle fa-2x me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-2">使用提示</h5>
                                <ul class="mb-0 ps-3">
                                    <li>为每个考场设置名称，并填写各科目需要的监考员数量</li>
                                    <li>可以通过Excel批量导入考场信息</li>
                                    <li>监考员数量通常为2人一个考场，特殊情况可调整</li>
                                    <li>可以添加或删除考场</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step2_rooms') }}" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="rooms_data" name="rooms_data">
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <button type="button" id="import-excel-btn" class="btn btn-outline-primary">
                                    <i class="fas fa-file-excel me-2"></i>从Excel导入
                                </button>
                                <input type="file" id="excel-file" accept=".xlsx,.xls" class="d-none">
                                <a href="#" id="download-template-btn" class="btn btn-outline-secondary">
                                    <i class="fas fa-download me-2"></i>下载导入模板
                                </a>
                                <div class="flex-grow-1"></div>
                                <button type="button" id="add-room-btn" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>添加考场
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="rooms-table">
                                <thead>
                                    <tr>
                                        <th style="width: 20%;">考场名称</th>
                                        {% for subject in wizard_data.get('subjects', []) %}
                                        <th>{{ subject.subject_name }} (监考人数)</th>
                                        {% endfor %}
                                        <th style="width: 5%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rooms will be dynamically added here -->
                                </tbody>
                            </table>
                        </div>


                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step1_subjects') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="room-template">
    <tr>
        <td><input type="text" name="room_name" class="form-control" required></td>
        {% for subject in wizard_data.get('subjects', []) %}
        <td><input type="number" name="demand_{{ subject.subject_name }}" class="form-control" min="0" placeholder="数量"></td>
        {% endfor %}
        <td style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm remove-room-btn"><i class="fas fa-trash"></i></button></td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    var wizardData = {{ wizard_data | tojson | safe }};
    var rooms = wizardData.rooms || [];
    var subjects = wizardData.subjects || [];
    var tableBody = $('#rooms-table tbody');

    function collectDataFromDOM() {
        var collectedRooms = [];
        $('#rooms-table tbody tr').each(function() {
            var roomName = $(this).find('input[name="room_name"]').val().trim();
            var demands = {};
            $(this).find('input[type="number"]').each(function() {
                var subjectName = $(this).attr('name').replace('demand_', '');
                demands[subjectName] = $(this).val();
            });
            collectedRooms.push({
                name: roomName,
                demands: demands
            });
        });
        return collectedRooms;
    }

    function renderRooms() {
        tableBody.empty();
        rooms.forEach(function(room) {
            var roomEl = $('#room-template').html();
            var $roomEl = $(roomEl);
            $roomEl.find('input[name="room_name"]').val(room.name || '');
            
            subjects.forEach(function(subject) {
                var demandValue = (room.demands && room.demands[subject.subject_name]) ? room.demands[subject.subject_name] : '';
                $roomEl.find('input[name="demand_' + subject.subject_name + '"]').val(demandValue);
            });

            tableBody.append($roomEl);
        });
    }

    // 下载模板
    $('#download-template-btn').click(function(e) {
        e.preventDefault();
        window.location.href = '{{ url_for("wizard_download_template", template_type="rooms") }}';
    });

    // Excel导入
    $('#import-excel-btn').click(function() {
        $('#excel-file').click();
    });

    // 设置CSRF令牌
    const csrfToken = $('input[name=csrf_token]').val();
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrfToken);
            }
        }
    });

    $('#excel-file').change(function(e) {
        const file = e.target.files[0];
        if (!file) {
            return;
        }

        // 检查文件类型
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        if (!allowedTypes.includes(file.type)) {
            alert('请上传Excel文件（.xls或.xlsx格式）');
            $(this).val('');
            return;
        }

        // 检查文件大小
        if (file.size > 5 * 1024 * 1024) {  // 5MB
            alert('文件大小不能超过5MB');
            $(this).val('');
            return;
        }

        // 显示加载提示
        const loadingAlert = $('<div class="alert alert-info alert-dismissible fade show" role="alert">')
            .html(`
                <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `);
        $('.card-body').prepend(loadingAlert);

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('csrf_token', csrfToken);  // 添加CSRF令牌

        // 发送到后端处理
        $.ajax({
            url: '{{ url_for("wizard_import_excel", step_type="rooms") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                loadingAlert.remove();
                
                if (response.success) {
                    // 更新rooms数据
                    rooms = response.data;
                    renderRooms();
                    
                    // 显示成功提示
                    const alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                        .html(`
                            <i class="fas fa-check-circle me-2"></i>${response.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `);
                    $('.card-body').prepend(alert);
                    
                    setTimeout(() => {
                        alert.alert('close');
                    }, 5000);
                } else {
                    // 显示错误提示
                    const alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">')
                        .html(`
                            <i class="fas fa-exclamation-circle me-2"></i>${response.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `);
                    $('.card-body').prepend(alert);
                    
                    setTimeout(() => {
                        alert.alert('close');
                    }, 5000);
                }
            },
            error: function(xhr, status, error) {
                loadingAlert.remove();

                let errorMessage = '导入失败，请稍后重试';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                }

                const alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">')
                    .html(`
                        <i class="fas fa-exclamation-circle me-2"></i>${errorMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `);
                $('.card-body').prepend(alert);

                setTimeout(() => {
                    alert.alert('close');
                }, 5000);
            },
            complete: function() {
                // 重置文件输入
                $('#excel-file').val('');
            }
        });
    });

    $('#add-room-btn').click(function() {
        rooms = collectDataFromDOM();
        var nextRoomNumber = rooms.length + 1;
        rooms.push({ name: nextRoomNumber + '考场', demands: {} });
        renderRooms();
    });

    $(document).on('click', '.remove-room-btn', function() {
        rooms = collectDataFromDOM();
        var index = $(this).closest('tr').index();
        rooms.splice(index, 1);
        renderRooms();
    });

    $('#wizard-form').submit(function(e) {
        var finalRooms = collectDataFromDOM().filter(function(r) { return r.name; });
        
        if (finalRooms.length === 0) {
            e.preventDefault();
            alert('请至少设置一个考场。');
            return;
        }

        $('#rooms_data').val(JSON.stringify(finalRooms));
    });

    // Initial render
    renderRooms();
});
</script>
{% endblock %}