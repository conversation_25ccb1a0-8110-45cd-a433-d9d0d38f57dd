#!/usr/bin/env python3
"""
测试按钮样式一致性
验证step3_proctors页面的按钮样式是否与step2_rooms保持一致
"""

import os

def check_step2_button_styles():
    """检查step2_rooms页面的按钮样式"""
    print("🔍 检查step2_rooms页面的按钮样式...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(step2_file):
        print(f"❌ 页面模板不存在: {step2_file}")
        return False, {}
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取step2的按钮样式
        step2_styles = {
            '添加按钮': 'btn btn-primary' in content,
            '批量添加按钮': 'btn btn-success' in content,
            '批量编辑按钮': 'btn btn-warning' in content,
            '批量删除按钮': 'btn btn-danger' in content,
            '下载模板按钮': 'btn btn-outline-secondary' in content,
            '删除按钮（表格内）': 'btn btn-danger btn-sm' in content
        }
        
        print("   📊 step2_rooms按钮样式:")
        for style_name, exists in step2_styles.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {style_name}")
        
        return True, step2_styles
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, {}

def check_step3_button_styles():
    """检查step3_proctors页面的按钮样式"""
    print("\n🔍 检查step3_proctors页面的按钮样式...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    if not os.path.exists(step3_file):
        print(f"❌ 页面模板不存在: {step3_file}")
        return False, {}
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取step3的按钮样式
        step3_styles = {
            '添加监考员按钮': 'btn btn-primary' in content and 'add-proctor-btn' in content,
            '批量复制按钮': 'btn btn-success' in content and 'copySelected' in content,
            '批量编辑按钮': 'btn btn-warning' in content and 'batchEdit' in content,
            '批量删除按钮': 'btn btn-danger' in content and 'deleteSelected' in content,
            '复制按钮（表格内）': 'btn btn-success btn-sm' in content and 'copy-proctor-btn' in content,
            '删除按钮（表格内）': 'btn btn-danger btn-sm' in content and 'remove-proctor-btn' in content,
            '导入按钮': 'btn btn-light btn-sm' in content,
            '下载模板按钮': 'btn btn-light btn-sm' in content
        }
        
        print("   📊 step3_proctors按钮样式:")
        for style_name, exists in step3_styles.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {style_name}")
        
        return True, step3_styles
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, {}

def compare_button_consistency():
    """比较按钮样式一致性"""
    print("\n🔍 比较按钮样式一致性...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            step2_content = f.read()
        
        with open(step3_file, 'r', encoding='utf-8') as f:
            step3_content = f.read()
        
        # 比较一致性
        consistency_checks = {
            '主要操作按钮（实心样式）': {
                'step2': 'btn btn-primary' in step2_content and 'btn btn-success' in step2_content and 'btn btn-warning' in step2_content and 'btn btn-danger' in step2_content,
                'step3': 'btn btn-primary' in step3_content and 'btn btn-success' in step3_content and 'btn btn-warning' in step3_content and 'btn btn-danger' in step3_content,
                'description': '主要操作按钮使用实心样式'
            },
            '表格内删除按钮': {
                'step2': 'btn btn-danger btn-sm' in step2_content,
                'step3': 'btn btn-danger btn-sm' in step3_content,
                'description': '表格内删除按钮使用红色小按钮'
            },
            '按钮图标间距': {
                'step2': 'me-2' in step2_content,
                'step3': 'me-2' in step3_content,
                'description': '按钮图标使用me-2间距'
            },
            '禁用状态': {
                'step2': 'disabled' in step2_content,
                'step3': 'disabled' in step3_content,
                'description': '批量操作按钮初始状态为禁用'
            }
        }
        
        all_consistent = True
        
        for check_name, check_info in consistency_checks.items():
            step2_ok = check_info['step2']
            step3_ok = check_info['step3']
            consistent = step2_ok and step3_ok
            
            status = "✅" if consistent else "❌"
            print(f"   {status} {check_name}")
            print(f"      说明: {check_info['description']}")
            print(f"      step2: {'✅' if step2_ok else '❌'}, step3: {'✅' if step3_ok else '❌'}")
            
            if not consistent:
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 比较失败: {e}")
        return False

def check_button_layout():
    """检查按钮布局"""
    print("\n🔍 检查按钮布局...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局相关
        layout_checks = {
            '顶部操作区域': 'bg-light border-bottom p-2' in content,
            '按钮组': 'btn-group' in content,
            '按钮间距': 'me-2' in content,
            '垂直对齐': 'vertical-align: middle' in content,
            '表格内按钮组': 'btn-group btn-group-sm' in content,
            '添加按钮位置': 'add-proctor-btn' in content and 'btn btn-primary' in content,
            '批量操作按钮组': 'copySelected' in content and 'batchEdit' in content and 'deleteSelected' in content
        }
        
        all_passed = True
        for check_name, result in layout_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_button_functionality():
    """检查按钮功能"""
    print("\n🔍 检查按钮功能...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查按钮功能
        function_checks = {
            '添加监考员': 'add-proctor-btn' in content and 'click' in content,
            '批量复制': 'copySelected' in content and 'click' in content,
            '批量编辑': 'batchEdit' in content and 'click' in content,
            '批量删除': 'deleteSelected' in content and 'click' in content,
            '单个复制': 'copy-proctor-btn' in content and 'click' in content,
            '单个删除': 'remove-proctor-btn' in content and 'click' in content,
            '按钮状态更新': 'updateBatchButtonsState' in content,
            '禁用状态控制': 'prop(\'disabled\'' in content
        }
        
        all_passed = True
        for check_name, result in function_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_button_improvements():
    """分析按钮改进"""
    print("\n📊 分析按钮改进...")
    
    improvements = {
        '样式统一性': {
            'description': '与step2_rooms页面保持一致的按钮样式',
            'changes': [
                '批量操作按钮改为实心样式（btn-success, btn-warning, btn-danger）',
                '表格内按钮改为实心样式（btn-success btn-sm, btn-danger btn-sm）',
                '添加按钮图标间距（me-2）'
            ]
        },
        '布局优化': {
            'description': '优化按钮布局和位置',
            'changes': [
                '将添加按钮移到顶部操作区域',
                '移除底部重复的添加按钮',
                '批量操作按钮分组显示'
            ]
        },
        '用户体验': {
            'description': '提升按钮的用户体验',
            'changes': [
                '批量操作按钮初始状态为禁用',
                '添加垂直对齐样式',
                '统一按钮大小和间距'
            ]
        }
    }
    
    for improvement_name, improvement_info in improvements.items():
        print(f"\n   {improvement_name}:")
        print(f"      描述: {improvement_info['description']}")
        print(f"      改进:")
        for change in improvement_info['changes']:
            print(f"         ✅ {change}")
    
    return True

def main():
    """主测试函数"""
    print("🔧 按钮样式一致性测试")
    print("=" * 60)
    print("测试step3_proctors页面的按钮样式是否与step2_rooms保持一致")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查step2按钮样式
    step2_ok, step2_styles = check_step2_button_styles()
    test_results.append(("step2按钮样式", step2_ok))
    
    # 2. 检查step3按钮样式
    step3_ok, step3_styles = check_step3_button_styles()
    test_results.append(("step3按钮样式", step3_ok))
    
    # 3. 比较按钮一致性
    test_results.append(("按钮样式一致性", compare_button_consistency()))
    
    # 4. 检查按钮布局
    test_results.append(("按钮布局", check_button_layout()))
    
    # 5. 检查按钮功能
    test_results.append(("按钮功能", check_button_functionality()))
    
    # 6. 分析按钮改进
    test_results.append(("按钮改进分析", analyze_button_improvements()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 按钮样式统一完成:")
        print("✅ 批量操作按钮 - 改为实心样式，与step2保持一致")
        print("✅ 表格内操作按钮 - 使用小尺寸实心按钮")
        print("✅ 按钮布局优化 - 添加按钮移到顶部，移除重复按钮")
        print("✅ 图标间距统一 - 使用me-2间距")
        print("✅ 禁用状态控制 - 批量操作按钮初始禁用")
        
        print("\n🚀 改进效果:")
        print("• 与step2_rooms页面保持一致的视觉风格")
        print("• 更清晰的按钮层次和功能区分")
        print("• 优化的布局和用户体验")
        print("• 统一的交互模式")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
