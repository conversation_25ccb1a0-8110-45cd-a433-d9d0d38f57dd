from core_adapter import CoreAdapter
import os
import traceback

def test_core():
    try:
        # 设置测试文件路径
        input_file = os.path.join('core', 'Exam_Config.xlsx')
        output_file = 'test_output.xlsx'
        
        print(f"输入文件路径: {input_file}")
        print(f"输出文件路径: {output_file}")
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"错误：输入文件不存在: {input_file}")
            return
        
        # 创建适配器实例
        adapter = CoreAdapter(
            task_id=1,
            input_file=input_file,
            output_file=output_file
        )
        
        # 执行处理
        success = adapter.process()
        
        # 检查结果
        if success:
            print("处理成功！")
            print(f"结果文件已保存到: {output_file}")
            if os.path.exists(output_file):
                print(f"输出文件大小: {os.path.getsize(output_file)} 字节")
            else:
                print("警告：输出文件未创建")
        else:
            print("处理失败！")
    
    except Exception as e:
        print(f"发生错误: {str(e)}")
        print("详细错误信息:")
        print(traceback.format_exc())

if __name__ == '__main__':
    test_core() 