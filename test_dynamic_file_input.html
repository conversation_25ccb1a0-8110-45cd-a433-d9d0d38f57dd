<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试动态文件输入</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>测试动态文件输入方法</h1>
        <p>这个页面测试动态创建文件输入元素的方法，这是最可靠的跨浏览器解决方案。</p>
        
        <div class="card">
            <div class="card-header">
                <h5>动态文件输入测试</h5>
            </div>
            <div class="card-body">
                <button type="button" id="test-btn" class="btn btn-primary btn-lg">
                    <i class="fas fa-file-excel me-2"></i>从Excel导入 (动态方法)
                </button>
                <div id="result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>测试日志</h5>
            </div>
            <div class="card-body">
                <div id="log" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;"></div>
                <button type="button" id="clear-log" class="btn btn-sm btn-secondary mt-2">清除日志</button>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            $('#log').append(`<div>${logEntry}</div>`);
            $('#log').scrollTop($('#log')[0].scrollHeight);
            console.log(logEntry);
        }

        function updateResult(message, success = false) {
            const resultDiv = $('#result');
            resultDiv.html(`<div class="alert alert-${success ? 'success' : 'info'}">${message}</div>`);
        }

        $(document).ready(function() {
            log('页面加载完成');
            log(`浏览器: ${navigator.userAgent}`);
            
            $('#test-btn').click(function(e) {
                e.preventDefault();
                log('测试按钮被点击');
                updateResult('正在创建文件输入...');
                
                try {
                    // 创建动态文件输入元素
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.xlsx,.xls';
                    input.style.display = 'none';
                    
                    log('文件输入元素已创建');
                    
                    // 设置文件选择回调
                    input.onchange = function(e) {
                        const file = e.target.files[0];
                        if (!file) {
                            log('没有选择文件');
                            updateResult('没有选择文件');
                            return;
                        }
                        
                        log(`文件已选择: ${file.name} (${file.size} bytes, ${file.type})`);
                        updateResult(`✅ 文件已选择: ${file.name}`, true);
                        
                        // 模拟文件验证
                        const allowedTypes = [
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        ];
                        
                        if (allowedTypes.includes(file.type)) {
                            log('文件类型验证通过');
                            updateResult(`✅ 文件类型验证通过: ${file.name}`, true);
                        } else {
                            log('文件类型验证失败');
                            updateResult(`❌ 文件类型不支持: ${file.type}`);
                        }
                        
                        // 清理动态创建的元素
                        if (input.parentNode) {
                            input.parentNode.removeChild(input);
                            log('动态文件输入元素已清理');
                        }
                    };
                    
                    // 添加到DOM并触发点击
                    document.body.appendChild(input);
                    log('文件输入元素已添加到DOM');
                    
                    input.click();
                    log('文件输入点击已触发');
                    updateResult('文件对话框应该已经打开...', true);
                    
                } catch (error) {
                    log(`错误: ${error.message}`);
                    updateResult(`❌ 错误: ${error.message}`);
                }
            });
            
            $('#clear-log').click(function() {
                $('#log').empty();
                log('日志已清除');
            });
            
            log('事件绑定完成');
        });
    </script>
</body>
</html>
