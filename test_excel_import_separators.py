#!/usr/bin/env python3
"""
测试Excel导入的分隔符处理功能
验证必监考科目、不监考科目、必监考考场、不监考考场字段的多分隔符支持
"""

import re
import pandas as pd
import os

def test_separator_processing():
    """测试分隔符处理逻辑"""
    print("🔍 测试分隔符处理逻辑...")
    
    # 模拟process_list_field函数
    def process_list_field(value):
        if value and str(value).strip():
            value = str(value).strip()
            # 支持多种分隔符：。，,;|: \t\n.-_/\\()[]{}<>*&^%$#@!~`+=?
            separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
            items = re.split(separators, value)
            # 过滤空字符串并去除首尾空格
            return [item.strip() for item in items if item.strip()]
        return []
    
    # 测试用例
    test_cases = [
        {
            'name': '逗号分隔',
            'input': '语文,数学,英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '中文逗号分隔',
            'input': '语文，数学，英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '分号分隔',
            'input': '语文;数学;英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '竖线分隔',
            'input': '语文|数学|英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '空格分隔',
            'input': '语文 数学 英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '制表符分隔',
            'input': '语文\t数学\t英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '换行符分隔',
            'input': '语文\n数学\n英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '点号分隔',
            'input': '语文.数学.英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '中文句号分隔',
            'input': '语文。数学。英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '连字符分隔',
            'input': '语文-数学-英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '下划线分隔',
            'input': '语文_数学_英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '斜杠分隔',
            'input': '语文/数学/英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '反斜杠分隔',
            'input': '语文\\数学\\英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '括号分隔',
            'input': '语文(数学)英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '方括号分隔',
            'input': '语文[数学]英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '花括号分隔',
            'input': '语文{数学}英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '尖括号分隔',
            'input': '语文<数学>英语',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '混合分隔符',
            'input': '语文,数学;英语|物理 化学.生物',
            'expected': ['语文', '数学', '英语', '物理', '化学', '生物']
        },
        {
            'name': '特殊符号分隔',
            'input': '语文*数学&英语^物理%化学$生物#历史@地理!政治~信息`技术+体育=美术?音乐',
            'expected': ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '信息', '技术', '体育', '美术', '音乐']
        },
        {
            'name': '带空格的内容',
            'input': '高中语文, 初中数学 ; 小学英语',
            'expected': ['高中语文', '初中数学', '小学英语']
        },
        {
            'name': '空字符串',
            'input': '',
            'expected': []
        },
        {
            'name': '只有分隔符',
            'input': ',;|',
            'expected': []
        },
        {
            'name': '单个内容',
            'input': '语文',
            'expected': ['语文']
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        result = process_list_field(test_case['input'])
        expected = test_case['expected']
        
        if result == expected:
            print(f"   ✅ {test_case['name']}: {test_case['input']} → {result}")
        else:
            print(f"   ❌ {test_case['name']}: {test_case['input']}")
            print(f"      期望: {expected}")
            print(f"      实际: {result}")
            all_passed = False
    
    return all_passed

def test_excel_import_simulation():
    """模拟Excel导入测试"""
    print("\n🔍 模拟Excel导入测试...")
    
    # 创建测试数据
    test_data = {
        '监考老师': ['张三', '李四', '王五'],
        '任教科目': ['语文', '数学', '英语'],
        '必监考科目': [
            '语文,数学',
            '数学;物理;化学',
            '英语|历史 地理.政治'
        ],
        '不监考科目': [
            '英语。物理',
            '语文/化学',
            '数学*生物&历史'
        ],
        '必监考考场': [
            '1考场,2考场',
            '3考场;4考场;5考场',
            '6考场|7考场 8考场'
        ],
        '不监考考场': [
            '10考场。11考场',
            '12考场/13考场',
            '14考场*15考场&16考场'
        ],
        '场次限制': [5, 3, 4]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 模拟处理逻辑
    def process_list_field(field_name, row):
        if field_name in df.columns and not pd.isna(row[field_name]):
            value = str(row[field_name]).strip()
            if value:
                # 支持多种分隔符
                separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
                items = re.split(separators, value)
                return [item.strip() for item in items if item.strip()]
        return []
    
    processed_data = []
    
    for index, row in df.iterrows():
        proctor_data = {
            'name': str(row['监考老师']).strip(),
            'teaching_subject': str(row['任教科目']).strip(),
            'required_subjects': process_list_field('必监考科目', row),
            'unavailable_subjects': process_list_field('不监考科目', row),
            'required_rooms': process_list_field('必监考考场', row),
            'unavailable_rooms': process_list_field('不监考考场', row),
            'session_limit': int(row['场次限制']) if not pd.isna(row['场次限制']) else None
        }
        processed_data.append(proctor_data)
    
    # 验证处理结果
    expected_results = [
        {
            'name': '张三',
            'required_subjects': ['语文', '数学'],
            'unavailable_subjects': ['英语', '物理'],
            'required_rooms': ['1考场', '2考场'],
            'unavailable_rooms': ['10考场', '11考场']
        },
        {
            'name': '李四',
            'required_subjects': ['数学', '物理', '化学'],
            'unavailable_subjects': ['语文', '化学'],
            'required_rooms': ['3考场', '4考场', '5考场'],
            'unavailable_rooms': ['12考场', '13考场']
        },
        {
            'name': '王五',
            'required_subjects': ['英语', '历史', '地理', '政治'],
            'unavailable_subjects': ['数学', '生物', '历史'],
            'required_rooms': ['6考场', '7考场', '8考场'],
            'unavailable_rooms': ['14考场', '15考场', '16考场']
        }
    ]
    
    all_passed = True
    
    for i, (actual, expected) in enumerate(zip(processed_data, expected_results)):
        print(f"\n   监考员 {i+1}: {actual['name']}")
        
        for field in ['required_subjects', 'unavailable_subjects', 'required_rooms', 'unavailable_rooms']:
            actual_value = actual[field]
            expected_value = expected[field]
            
            if actual_value == expected_value:
                print(f"      ✅ {field}: {actual_value}")
            else:
                print(f"      ❌ {field}:")
                print(f"         期望: {expected_value}")
                print(f"         实际: {actual_value}")
                all_passed = False
    
    return all_passed

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    def process_list_field(value):
        if value and str(value).strip():
            value = str(value).strip()
            separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
            items = re.split(separators, value)
            return [item.strip() for item in items if item.strip()]
        return []
    
    edge_cases = [
        {
            'name': 'None值',
            'input': None,
            'expected': []
        },
        {
            'name': 'NaN值',
            'input': float('nan'),
            'expected': []
        },
        {
            'name': '空字符串',
            'input': '',
            'expected': []
        },
        {
            'name': '只有空格',
            'input': '   ',
            'expected': []
        },
        {
            'name': '连续分隔符',
            'input': '语文,,,,数学;;;;英语||||物理',
            'expected': ['语文', '数学', '英语', '物理']
        },
        {
            'name': '首尾分隔符',
            'input': ',语文,数学,英语,',
            'expected': ['语文', '数学', '英语']
        },
        {
            'name': '包含空格的项目',
            'input': '高中 语文, 初中 数学 , 小学 英语',
            'expected': ['高中', '语文', '初中', '数学', '小学', '英语']
        },
        {
            'name': '数字内容',
            'input': '1考场,2考场,3考场',
            'expected': ['1考场', '2考场', '3考场']
        },
        {
            'name': '特殊字符内容',
            'input': 'C++编程;C#开发;.NET框架',
            'expected': ['C', '编程', 'C', '开发', 'NET框架']
        }
    ]
    
    all_passed = True
    
    for test_case in edge_cases:
        try:
            result = process_list_field(test_case['input'])
            expected = test_case['expected']
            
            if result == expected:
                print(f"   ✅ {test_case['name']}: {result}")
            else:
                print(f"   ❌ {test_case['name']}:")
                print(f"      期望: {expected}")
                print(f"      实际: {result}")
                all_passed = False
        except Exception as e:
            print(f"   ❌ {test_case['name']}: 处理异常 - {e}")
            all_passed = False
    
    return all_passed

def analyze_separator_coverage():
    """分析分隔符覆盖度"""
    print("\n📊 分析分隔符覆盖度...")
    
    # 定义的分隔符
    defined_separators = '。，,;|: \t\n.-_/\\()[]{}<>*&^%$#@!~`+=?'
    
    # 分类分析
    separator_categories = {
        '标点符号': ['。', '，', ',', ';', ':', '.', '-', '_'],
        '空白字符': [' ', '\t', '\n'],
        '逻辑符号': ['|', '&', '^', '%', '$', '#', '@', '!', '~', '`', '+', '=', '?'],
        '括号类': ['(', ')', '[', ']', '{', '}', '<', '>'],
        '路径符号': ['/', '\\'],
        '数学符号': ['*', '+', '=', '-']
    }
    
    print("   📝 支持的分隔符分类:")
    for category, symbols in separator_categories.items():
        print(f"      {category}: {' '.join(symbols)}")
    
    # 测试每个类别
    def process_list_field(value):
        if value and str(value).strip():
            value = str(value).strip()
            separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
            items = re.split(separators, value)
            return [item.strip() for item in items if item.strip()]
        return []
    
    print("\n   🧪 分类测试:")
    for category, symbols in separator_categories.items():
        test_input = f"语文{symbols[0]}数学{symbols[1] if len(symbols) > 1 else symbols[0]}英语"
        result = process_list_field(test_input)
        expected = ['语文', '数学', '英语']
        
        if result == expected:
            print(f"      ✅ {category}: {test_input} → {result}")
        else:
            print(f"      ❌ {category}: {test_input} → {result}")
    
    return True

def main():
    """主测试函数"""
    print("🔧 Excel导入分隔符处理测试")
    print("=" * 60)
    print("测试必监考科目、不监考科目、必监考考场、不监考考场字段的多分隔符支持")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试分隔符处理逻辑
    test_results.append(("分隔符处理逻辑", test_separator_processing()))
    
    # 2. 模拟Excel导入测试
    test_results.append(("Excel导入模拟", test_excel_import_simulation()))
    
    # 3. 测试边界情况
    test_results.append(("边界情况测试", test_edge_cases()))
    
    # 4. 分析分隔符覆盖度
    test_results.append(("分隔符覆盖度", analyze_separator_coverage()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 分隔符处理功能完成:")
        print("✅ 多分隔符支持 - 支持30+种常用分隔符")
        print("✅ 智能分割 - 自动过滤空字符串和空格")
        print("✅ 边界处理 - 正确处理None、空字符串等边界情况")
        print("✅ 混合分隔符 - 支持在同一字符串中使用多种分隔符")
        print("✅ 中文支持 - 支持中文标点符号")
        
        print("\n🚀 支持的分隔符:")
        print("• 标点符号: 。，,;:.-_")
        print("• 空白字符: 空格、制表符、换行符")
        print("• 逻辑符号: |&^%$#@!~`+=?")
        print("• 括号类: ()[]{}<>")
        print("• 路径符号: /\\")
        print("• 数学符号: *+-=")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
