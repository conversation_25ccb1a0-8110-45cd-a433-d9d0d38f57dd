/**
 * 科目Excel导入功能
 * 独立脚本，避免与页面主JavaScript冲突
 */

(function() {
    'use strict';
    
    // 等待DOM完全加载
    function initSubjectsExcelImport() {
        const importBtn = document.getElementById('import-excel-btn-subjects');
        const resultArea = document.getElementById('import-result-subjects');
        
        if (!importBtn) {
            console.log('科目导入按钮未找到');
            return;
        }
        
        if (!resultArea) {
            console.log('科目导入结果区域未找到');
            return;
        }
        
        console.log('初始化科目Excel导入功能');
        
        // 移除可能存在的旧事件监听器
        importBtn.removeAttribute('onclick');
        
        // 添加新的事件监听器
        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('科目Excel导入按钮被点击');
            
            // 创建文件输入
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.style.display = 'none';
            
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) {
                    console.log('没有选择文件');
                    return;
                }
                
                console.log('文件已选择:', file.name, file.size, 'bytes');
                handleFileUpload(file);
                
                // 清理文件输入
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };
            
            document.body.appendChild(input);
            input.click();
        });
        
        function handleFileUpload(file) {
            console.log('开始处理文件:', file.name);
            
            // 验证文件类型
            const allowedTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                showMessage('请上传Excel文件（.xls或.xlsx格式）', 'danger');
                return;
            }
            
            // 验证文件大小
            if (file.size > 5 * 1024 * 1024) {
                showMessage('文件大小不能超过5MB', 'danger');
                return;
            }
            
            // 获取CSRF令牌
            const csrfToken = document.querySelector('input[name=csrf_token]');
            if (!csrfToken || !csrfToken.value) {
                showMessage('CSRF令牌未找到，请刷新页面重试', 'danger');
                return;
            }
            
            // 显示加载状态
            showLoadingMessage();
            
            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('csrf_token', csrfToken.value);
            
            // 发送请求
            fetch('/wizard/import-excel/subjects', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('服务器响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('导入响应:', data);
                hideLoadingMessage();
                
                if (data.success) {
                    showMessage(data.message || '导入成功', 'success');
                    
                    // 动态更新表格数据，而不是刷新页面
                    try {
                        updateTableData(data.data);
                        console.log('表格数据已更新');
                    } catch (error) {
                        console.error('更新表格数据失败:', error);
                        // 如果动态更新失败，则刷新页面作为后备方案
                        setTimeout(() => {
                            console.log('动态更新失败，刷新页面作为后备方案');
                            window.location.reload();
                        }, 2000);
                    }
                } else {
                    showMessage(data.message || '导入失败', 'danger');
                }
            })
            .catch(error => {
                console.error('导入失败:', error);
                hideLoadingMessage();
                
                let errorMessage = '导入失败，请稍后重试';
                if (error.message.includes('403')) {
                    errorMessage = '权限不足，请刷新页面重试';
                } else if (error.message.includes('413')) {
                    errorMessage = '文件过大，请选择较小的文件';
                } else if (error.message.includes('500')) {
                    errorMessage = '服务器内部错误，请联系管理员';
                }
                
                showMessage(errorMessage, 'danger');
            });
        }
        
        function showLoadingMessage() {
            resultArea.innerHTML = `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideLoadingMessage() {
            const loadingAlert = resultArea.querySelector('.alert-info');
            if (loadingAlert) {
                loadingAlert.remove();
            }
        }
        
        function updateTableData(importedData) {
            console.log('开始更新表格数据:', importedData);
            
            // 方法1: 尝试通过jQuery DataTable API更新
            const tableElement = $('#subjects-table');
            if (tableElement.length > 0) {
                try {
                    const dataTable = tableElement.DataTable();
                    if (dataTable) {
                        console.log('找到DataTable实例，清空并添加新数据');
                        dataTable.clear();
                        dataTable.rows.add(importedData);
                        dataTable.draw();
                        
                        // 更新隐藏字段（如果存在updateSubjectsData函数）
                        if (typeof window.updateSubjectsData === 'function') {
                            window.updateSubjectsData(importedData);
                        } else if (document.getElementById('subjects_data')) {
                            document.getElementById('subjects_data').value = JSON.stringify(importedData);
                            console.log('直接更新隐藏字段');
                        }
                        
                        return true;
                    }
                } catch (error) {
                    console.log('DataTable API更新失败:', error);
                }
            }
            
            // 方法2: 尝试通过全局变量更新
            if (typeof window.subjects !== 'undefined' && typeof window.table !== 'undefined') {
                try {
                    console.log('通过全局变量更新表格');
                    window.subjects = importedData;
                    window.table.clear();
                    window.table.rows.add(importedData);
                    window.table.draw();
                    
                    if (typeof window.updateSubjectsData === 'function') {
                        window.updateSubjectsData();
                    }
                    
                    return true;
                } catch (error) {
                    console.log('全局变量更新失败:', error);
                }
            }
            
            // 方法3: 触发自定义事件让主页面处理
            try {
                console.log('触发自定义事件');
                const event = new CustomEvent('subjectsImported', {
                    detail: { data: importedData }
                });
                document.dispatchEvent(event);
                
                // 等待一下看事件是否被处理
                setTimeout(() => {
                    const currentRows = tableElement.find('tbody tr').length;
                    if (currentRows === importedData.length) {
                        console.log('自定义事件更新成功');
                        return true;
                    }
                }, 100);
            } catch (error) {
                console.log('自定义事件失败:', error);
            }
            
            // 如果所有方法都失败，抛出错误让调用者知道需要刷新页面
            throw new Error('无法动态更新表格数据');
        }
        
        function showMessage(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const iconClass = type === 'success' ? 'fa-check-circle' : 
                             type === 'danger' ? 'fa-exclamation-circle' : 
                             'fa-info-circle';
            
            resultArea.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${iconClass} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    const alert = resultArea.querySelector(`.alert-${type}`);
                    if (alert) {
                        alert.remove();
                    }
                }, 5000);
            }
        }
        
        console.log('科目Excel导入功能初始化完成');
    }
    
    // 多种方式确保初始化执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSubjectsExcelImport);
    } else {
        initSubjectsExcelImport();
    }
    
    // 页面加载完成后再次尝试
    window.addEventListener('load', function() {
        setTimeout(initSubjectsExcelImport, 500);
    });
    
})(); 