import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class ExamConfigProcessor:
    """考试配置处理器类"""
    
    def __init__(self, template_path):
        """
        初始化处理器
        
        Args:
            template_path (str): 模板文件路径
        """
        self.template_path = template_path
        self.proctor_df = None  # 监考员设置表
        self.subject_df = None  # 考试科目设置表
        self.room_df = None     # 考场设置表
        
    def read_template_file(self):
        """
        读取Excel模板文件的所有工作表
        
        Returns:
            bool: 读取是否成功
        """
        try:
            # 读取所有工作表
            excel_file = pd.ExcelFile(self.template_path)
            print(f"成功读取模板文件: {self.template_path}")
            print(f"工作表列表: {excel_file.sheet_names}")
            
            # 读取各个工作表
            if '监考员设置' in excel_file.sheet_names:
                self.proctor_df = pd.read_excel(self.template_path, sheet_name='监考员设置')
                print(f"监考员设置表读取成功，共 {len(self.proctor_df)} 行数据")
                
            if '考试科目设置' in excel_file.sheet_names:
                self.subject_df = pd.read_excel(self.template_path, sheet_name='考试科目设置')
                print(f"考试科目设置表读取成功，共 {len(self.subject_df)} 行数据")
                
            if '考场设置' in excel_file.sheet_names:
                self.room_df = pd.read_excel(self.template_path, sheet_name='考场设置')
                print(f"考场设置表读取成功，共 {len(self.room_df)} 行数据")
                
            return True
            
        except Exception as e:
            print(f"读取模板文件时出错: {e}")
            return False
    
    def parse_subject_list(self, subject_string):
        """
        解析科目字符串，支持多种分隔符
        
        Args:
            subject_string (str): 包含科目的字符串
            
        Returns:
            list: 解析出的科目列表
        """
        if pd.isna(subject_string) or not str(subject_string).strip():
            return []
        
        # 定义支持的分隔符
        separators = ['.', '。', '，', ';', '、', '|', '/', '-', ',', ' ']
        
        # 转换为字符串并清理
        subject_string = str(subject_string).strip()
        
        # 使用正则表达式进行分割，支持多种分隔符
        import re
        pattern = '[' + re.escape(''.join(separators)) + ']+'
        subjects = re.split(pattern, subject_string)
        
        # 清理结果，去除空字符串和空白
        subjects = [s.strip() for s in subjects if s.strip()]
        
        return subjects

    def parse_room_list(self, room_string):
        """
        解析考场字符串，支持多种分隔符
        
        Args:
            room_string (str): 包含考场的字符串
            
        Returns:
            list: 解析出的考场列表
        """
        if pd.isna(room_string) or not str(room_string).strip():
            return []
        
        # 定义支持的分隔符
        separators = ['.', '。', '，', ';', '、', '|', '/', '-', ',', ' ']
        
        # 转换为字符串并清理
        room_string = str(room_string).strip()
        
        # 使用正则表达式进行分割，支持多种分隔符
        import re
        pattern = '[' + re.escape(''.join(separators)) + ']+'
        rooms = re.split(pattern, room_string)
        
        # 清理结果，去除空字符串和空白
        rooms = [s.strip() for s in rooms if s.strip()]
        
        return rooms

    def get_subject_rooms(self, subject):
        """
        获取某科目对应的考场列表
        
        Args:
            subject (str): 科目名称
            
        Returns:
            list: 该科目对应的考场列表
        """
        if self.room_df is None:
            return []
        
        # 找到该科目在考场设置表中对应的列
        subject_column = None
        room_columns = [col for col in self.room_df.columns if col != '考场']
        
        # 尝试直接匹配科目名
        if subject in self.room_df.columns:
            subject_column = subject
        else:
            # 如果没有直接匹配，按顺序匹配（适用于科目顺序对应的情况）
            subjects = []
            if self.subject_df is not None:
                if '课程名称' in self.subject_df.columns:
                    subjects = self.subject_df['课程名称'].tolist()
                elif '科目' in self.subject_df.columns:
                    subjects = self.subject_df['科目'].tolist()
                elif '科目名称' in self.subject_df.columns:
                    subjects = self.subject_df['科目名称'].tolist()
            
            if subject in subjects:
                subject_index = subjects.index(subject)
                if subject_index < len(room_columns):
                    subject_column = room_columns[subject_index]
        
        if subject_column is None:
            return []
        
        # 获取该科目值大于0的考场（包括1和2）
        subject_rooms = []
        for idx, row in self.room_df.iterrows():
            if row[subject_column] > 0:
                subject_rooms.append(str(row['考场']))
        
        return subject_rooms

    def generate_room_conflict_report(self, processed_proctor_df, subjects):
        """
        生成考场冲突和优先级报告
        
        Args:
            processed_proctor_df (pd.DataFrame): 处理后的监考员设置表
            subjects (list): 科目列表
            
        Returns:
            list: 冲突报告列表
        """
        conflicts = []
        
        for idx, row in processed_proctor_df.iterrows():
            name = row['监考老师']
            required_rooms = self.parse_room_list(row.get('必监考考场', ''))
            forbidden_rooms = self.parse_room_list(row.get('不监考考场', ''))
            required_subjects = self.parse_subject_list(row.get('必监考科目', ''))
            
            # 检查是否同时设置了必监考考场和不监考考场
            if required_rooms and forbidden_rooms:
                overlap = set(required_rooms) & set(forbidden_rooms)
                if overlap:
                    conflicts.append(f"{name}: 必监考考场和不监考考场有重叠考场 - {list(overlap)}")
                
                # 检查必监考科目的处理情况
                for subject in required_subjects:
                    subject_rooms = self.get_subject_rooms(subject)
                    req_overlap = set(required_rooms) & set(subject_rooms)
                    forb_overlap = set(forbidden_rooms) & set(subject_rooms)
                    
                    if req_overlap and forb_overlap:
                        conflicts.append(f"{name}-{subject}: 必监考考场优先生效 {list(req_overlap)}，"
                                       f"不监考考场 {list(forb_overlap)} 被忽略")
                    elif req_overlap:
                        conflicts.append(f"{name}-{subject}: 仅安排到必监考考场 {list(req_overlap)}")
            
            # 检查b+标记的详细信息
            for subject in subjects:
                column_name = f'{subject}_监考'
                mark = row[column_name]
                if mark == 'b+':
                    subject_rooms = self.get_subject_rooms(subject)
                    req_overlap = set(required_rooms) & set(subject_rooms)
                    if req_overlap:
                        conflicts.append(f"{name}-{subject}: 标记为b+ → 必监考科目，只安排到指定考场 {list(req_overlap)}")
        
        return conflicts

    def process_proctor_settings(self, required_subjects=None, forbidden_subjects=None, default_value='y'):
        """
        处理监考员设置，添加考试科目列并标记可监考科目，融入考场信息
        
        Args:
            required_subjects (list): 用户指定的必监考科目列表
            forbidden_subjects (list): 用户指定的不监考科目列表
            default_value (str): 未明确设置科目的默认值，可选：'y'(可监考)、'n'(不可监考)、''(空值)
            
        Returns:
            pd.DataFrame: 处理后的监考员设置表
            
        标记规则：
            'n' - 不可监考该科目（考场信息不适用）
            'b' - 必监考该科目，所有考场都可以
            'b+' - 必监考该科目，且有指定必监考考场（只安排到指定考场，忽略不监考考场）
            'b-' - 必监考该科目，但有考场限制（不能监考某些考场）
            'y' - 可监考该科目，所有考场都可以
            'y+' - 可监考该科目，且有指定优先考场
            'y-' - 可监考该科目，但有考场限制
        """
        if self.proctor_df is None:
            print("错误: 监考员设置表未读取")
            return None
            
        # 从考试科目设置表获取科目，如果没有就从考场设置表获取
        subjects = []
        if self.subject_df is not None:
            # 尝试不同的列名来获取科目
            if '课程名称' in self.subject_df.columns:
                subjects = self.subject_df['课程名称'].unique()
            elif '科目' in self.subject_df.columns:
                subjects = self.subject_df['科目'].unique()
            elif '科目名称' in self.subject_df.columns:
                subjects = self.subject_df['科目名称'].unique()
        
        if len(subjects) == 0 and self.room_df is not None:
            # 从考场设置表的列名中获取科目（除了'考场'列）
            subjects = [col for col in self.room_df.columns if col != '考场']
        
        if len(subjects) == 0:
            print("错误: 无法获取科目列表")
            return None
        
        print(f"找到 {len(subjects)} 个科目: {list(subjects)}")
        
        # 复制原始数据
        processed_proctor_df = self.proctor_df.copy()
        
        # 为每个科目添加列，初始化为空值
        for subject in subjects:
            processed_proctor_df[f'{subject}_监考'] = ''
        
        # 逐行处理每个监考员
        for idx, row in processed_proctor_df.iterrows():
            # 解析必监考科目
            required_list = []
            if '必监考科目' in processed_proctor_df.columns:
                required_list = self.parse_subject_list(row['必监考科目'])
            
            # 解析不监考科目
            forbidden_list = []
            if '不监考科目' in processed_proctor_df.columns:
                forbidden_list = self.parse_subject_list(row['不监考科目'])
            
            # 解析必监考考场
            required_rooms = []
            if '必监考考场' in processed_proctor_df.columns:
                required_rooms = self.parse_room_list(row['必监考考场'])
            
            # 解析不监考考场
            forbidden_rooms = []
            if '不监考考场' in processed_proctor_df.columns:
                forbidden_rooms = self.parse_room_list(row['不监考考场'])
            
            # 为每个科目设置标记
            for subject in subjects:
                column_name = f'{subject}_监考'
                
                # 第一阶段：确定基础监考能力
                basic_ability = ''
                
                # 优先级1：检查是否在必监考科目中（表格中或用户指定）
                if (subject in required_list) or (required_subjects and subject in required_subjects):
                    basic_ability = 'b'  # 必监考
                # 优先级2：检查是否在不监考科目中（表格中或用户指定）
                elif (subject in forbidden_list) or (forbidden_subjects and subject in forbidden_subjects):
                    basic_ability = 'n'  # 不可监考
                # 优先级3：其他情况使用默认值
                else:
                    basic_ability = default_value  # 可监考或默认值
                
                # 第二阶段：考场信息处理，必监考科目的必监考考场具有最高优先级
                if basic_ability == 'n':
                    final_mark = 'n'
                elif basic_ability == 'b':  # 必监考科目特殊处理
                    # 获取该科目对应的考场列表
                    subject_rooms = self.get_subject_rooms(subject)
                    
                    # 检查必监考考场 (最高优先级)
                    has_required_rooms = bool(set(required_rooms) & set(subject_rooms))
                    if has_required_rooms:
                        final_mark = 'b+'  # 必监考，只安排到指定考场，忽略不监考考场
                    else:
                        # 没有必监考考场时，才检查不监考考场
                        has_forbidden_rooms = bool(set(forbidden_rooms) & set(subject_rooms))
                        if has_forbidden_rooms:
                            final_mark = 'b-'  # 必监考，但有考场限制
                        else:
                            final_mark = 'b'   # 必监考，无考场限制
                elif basic_ability == 'y':  # 可监考科目
                    # 获取该科目对应的考场列表
                    subject_rooms = self.get_subject_rooms(subject)
                    
                    # 检查考场限制 (维持原逻辑)
                    has_forbidden_rooms = bool(set(forbidden_rooms) & set(subject_rooms))
                    has_required_rooms = bool(set(required_rooms) & set(subject_rooms))
                    
                    if has_forbidden_rooms:
                        final_mark = 'y-'  # 可监考，但有考场限制
                    elif has_required_rooms:
                        final_mark = 'y+'  # 可监考，有优先考场
                    else:
                        final_mark = 'y'   # 可监考，无特殊要求
                else:
                    # 其他情况保持原值
                    final_mark = basic_ability
                
                processed_proctor_df.at[idx, column_name] = final_mark
        
        # 统计处理结果
        stats = {}
        for subject in subjects:
            column_name = f'{subject}_监考'
            b_count = (processed_proctor_df[column_name] == 'b').sum()      # 必监考(无限制)
            b_plus_count = (processed_proctor_df[column_name] == 'b+').sum()  # 必监考(有指定考场)
            b_minus_count = (processed_proctor_df[column_name] == 'b-').sum() # 必监考(有考场限制)
            y_count = (processed_proctor_df[column_name] == 'y').sum()      # 可监考(无限制)
            y_plus_count = (processed_proctor_df[column_name] == 'y+').sum()  # 可监考(有优先考场)
            y_minus_count = (processed_proctor_df[column_name] == 'y-').sum() # 可监考(有考场限制)
            n_count = (processed_proctor_df[column_name] == 'n').sum()      # 不可监考
            empty_count = (processed_proctor_df[column_name] == '').sum()   # 空值
            other_count = len(processed_proctor_df) - (b_count + b_plus_count + b_minus_count + 
                                                      y_count + y_plus_count + y_minus_count + 
                                                      n_count + empty_count)  # 其他值
            
            stats[subject] = {
                '必监考(无限制)': b_count,
                '必监考(指定考场)': b_plus_count,
                '必监考(考场限制)': b_minus_count,
                '可监考(无限制)': y_count,
                '可监考(优先考场)': y_plus_count,
                '可监考(考场限制)': y_minus_count,
                '不监考': n_count, 
                '未设置': empty_count,
                '其他': other_count
            }
        
        print(f"监考员设置处理完成，共处理 {len(subjects)} 个科目")
        print("各科目监考员分配统计:")
        for subject, stat in stats.items():
            # 获取该科目对应的考场
            subject_rooms = self.get_subject_rooms(subject)
            room_info = f"(考场: {', '.join(subject_rooms)})" if subject_rooms else "(无考场信息)"
            
            parts = []
            if stat['必监考(无限制)'] > 0:
                parts.append(f"必监考(b)={stat['必监考(无限制)']}人")
            if stat['必监考(指定考场)'] > 0:
                parts.append(f"必监考指定考场(b+)={stat['必监考(指定考场)']}人")
            if stat['必监考(考场限制)'] > 0:
                parts.append(f"必监考有限制(b-)={stat['必监考(考场限制)']}人")
            if stat['可监考(无限制)'] > 0:
                parts.append(f"可监考(y)={stat['可监考(无限制)']}人")
            if stat['可监考(优先考场)'] > 0:
                parts.append(f"可监考优先考场(y+)={stat['可监考(优先考场)']}人")
            if stat['可监考(考场限制)'] > 0:
                parts.append(f"可监考有限制(y-)={stat['可监考(考场限制)']}人")
            if stat['不监考'] > 0:
                parts.append(f"不监考(n)={stat['不监考']}人")
            if stat['未设置'] > 0:
                parts.append(f"未设置={stat['未设置']}人")
            if stat['其他'] > 0:
                parts.append(f"其他={stat['其他']}人")
            
            print(f"  {subject} {room_info}: {', '.join(parts) if parts else '无数据'}")
        
        # 生成考场冲突报告
        conflict_report = self.generate_room_conflict_report(processed_proctor_df, subjects)
        if conflict_report:
            print("\n考场冲突和优先级处理报告:")
            for report in conflict_report:
                print(f"  {report}")
        
        self.processed_proctor_df = processed_proctor_df
        return processed_proctor_df
    
    def calculate_exam_duration(self):
        """
        计算考试科目的考试时长（分钟）
        
        Returns:
            pd.DataFrame: 包含考试时长的科目设置表
        """
        if self.subject_df is None:
            print("错误: 考试科目设置表未读取")
            return None
            
        # 复制原始数据
        processed_subject_df = self.subject_df.copy()
        
        # 检查必要的列是否存在
        required_columns = ['开始时间', '结束时间']
        if not all(col in processed_subject_df.columns for col in required_columns):
            print(f"错误: 缺少必要的时间列，需要: {required_columns}")
            return processed_subject_df
        
        # 计算考试时长
        duration_list = []
        for index, row in processed_subject_df.iterrows():
            try:
                # 处理时间格式
                start_time = row['开始时间']
                end_time = row['结束时间']
                
                # 如果是字符串格式，尝试多种格式解析
                if isinstance(start_time, str):
                    start_time = start_time.strip()
                    try:
                        # 尝试解析带日期的时间格式（如：2025/4/27 9:00）
                        start_time = datetime.strptime(start_time, '%Y/%m/%d %H:%M')
                    except:
                        try:
                            # 尝试其他日期格式
                            start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
                        except:
                            try:
                                # 尝试只有时间的格式
                                start_time = datetime.strptime(start_time, '%H:%M')
                            except:
                                try:
                                    start_time = datetime.strptime(start_time, '%H:%M:%S')
                                except:
                                    # 使用pandas解析
                                    start_time = pd.to_datetime(start_time)
                
                if isinstance(end_time, str):
                    end_time = end_time.strip()
                    try:
                        # 尝试解析带日期的时间格式（如：2025/4/27 11:30）
                        end_time = datetime.strptime(end_time, '%Y/%m/%d %H:%M')
                    except:
                        try:
                            # 尝试其他日期格式
                            end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
                        except:
                            try:
                                # 尝试只有时间的格式
                                end_time = datetime.strptime(end_time, '%H:%M')
                            except:
                                try:
                                    end_time = datetime.strptime(end_time, '%H:%M:%S')
                                except:
                                    # 使用pandas解析
                                    end_time = pd.to_datetime(end_time)
                
                # 确保都是datetime对象
                if hasattr(start_time, 'to_pydatetime'):
                    start_time = start_time.to_pydatetime()
                if hasattr(end_time, 'to_pydatetime'):
                    end_time = end_time.to_pydatetime()
                
                # 如果只有时间没有日期，使用同一天
                if hasattr(start_time, 'time') and not hasattr(start_time, 'date'):
                    start_time = datetime.combine(datetime.today().date(), start_time.time())
                if hasattr(end_time, 'time') and not hasattr(end_time, 'date'):
                    end_time = datetime.combine(datetime.today().date(), end_time.time())
                
                # 计算时长（分钟）
                if isinstance(start_time, datetime) and isinstance(end_time, datetime):
                    duration = (end_time - start_time).total_seconds() / 60
                    duration_list.append(int(duration))
                else:
                    duration_list.append(0)
                    
            except Exception as e:
                print(f"计算第 {index+1} 行考试时长时出错: {e}")
                duration_list.append(0)
        
        # 添加考试时长列
        processed_subject_df['考试时长(分钟)'] = duration_list
        
        print(f"考试时长计算完成，共处理 {len(processed_subject_df)} 个科目")
        self.processed_subject_df = processed_subject_df
        return processed_subject_df
    

    
    def create_room_setting_with_summary(self):
        """
        创建带有汇总信息的考场设置表
        
        Returns:
            pd.DataFrame: 带汇总的考场设置表
        """
        if self.room_df is None:
            print("错误: 考场设置表未读取")
            return None
        
        if not hasattr(self, 'processed_proctor_df') or self.processed_proctor_df is None:
            print("错误: 监考员设置表未处理")
            return None
        
        # 复制原始考场设置表
        room_with_summary = self.room_df.copy()
        
        # 获取科目列表（除了'考场'列）
        subject_columns = [col for col in room_with_summary.columns if col != '考场']
        
        # 获取科目名称
        subjects = []
        if self.subject_df is not None:
            if '课程名称' in self.subject_df.columns:
                subjects = self.subject_df['课程名称'].tolist()
            elif '科目' in self.subject_df.columns:
                subjects = self.subject_df['科目'].tolist()
            elif '科目名称' in self.subject_df.columns:
                subjects = self.subject_df['科目名称'].tolist()
        
        if not subjects:
            subjects = subject_columns
        
        # 计算每个科目的考场需求数量
        summary_data = {'考场': '总监考人员需求'}
        
        for col in subject_columns:
            # 统计该科目需要监考的考场数量（值大于0的考场）
            need_rooms = (room_with_summary[col] > 0).sum()
            summary_data[col] = need_rooms
        
        # 创建汇总行的DataFrame（不添加空行）
        summary_row = pd.DataFrame([summary_data])
        
        # 将汇总信息添加到考场设置表底部
        room_with_summary = pd.concat([room_with_summary, summary_row], ignore_index=True)
        
        print("考场设置表汇总信息添加完成")
        self.room_with_summary_df = room_with_summary
        return room_with_summary
    
    def display_results(self):
        """显示处理结果"""
        print("\n" + "="*50)
        print("处理结果汇总")
        print("="*50)
        
        # 显示监考员设置处理结果
        if hasattr(self, 'processed_proctor_df') and self.processed_proctor_df is not None:
            print("\n【监考员设置处理结果】")
            print(f"监考员总数: {len(self.processed_proctor_df)}")
            print("前5行数据预览:")
            print(self.processed_proctor_df.head())
        
        # 显示考试科目设置处理结果
        if hasattr(self, 'processed_subject_df') and self.processed_subject_df is not None:
            print("\n【考试科目设置处理结果】")
            print(f"考试科目总数: {len(self.processed_subject_df)}")
            if '考试时长(分钟)' in self.processed_subject_df.columns:
                valid_durations = self.processed_subject_df['考试时长(分钟)'][self.processed_subject_df['考试时长(分钟)'] > 0]
                if len(valid_durations) > 0:
                    print(f"平均考试时长: {valid_durations.mean():.1f} 分钟")
            print("科目及时长信息:")
            # 显示所有列
            print(self.processed_subject_df.to_string())
        
        # 显示带汇总的考场设置结果
        if hasattr(self, 'room_with_summary_df') and self.room_with_summary_df is not None:
            print("\n【考场设置处理结果（含汇总）】")
            print("考场设置表（底部含各学科监考人员汇总）:")
            print(self.room_with_summary_df.to_string())
        

    
    def export_results(self, output_path='处理结果.xlsx'):
        """
        导出处理结果到Excel文件和JSON文件
        
        Args:
            output_path (str): 输出文件路径（不含扩展名时会同时生成.xlsx和.json）
        """
        try:
            # 处理输出路径
            import os
            base_path = os.path.splitext(output_path)[0]
            excel_path = base_path + '.xlsx'
            json_path = base_path + '.json'
            
            # 导出Excel文件
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                # 导出监考员设置
                if hasattr(self, 'processed_proctor_df') and self.processed_proctor_df is not None:
                    # 清理监考员设置表中的"0"和"nan"值
                    cleaned_proctor_df = self.processed_proctor_df.copy()
                    
                    # 将数值0和字符串"0"替换为空值
                    cleaned_proctor_df = cleaned_proctor_df.replace([0, '0'], '')
                    
                    # 将NaN和字符串"nan"替换为空值
                    cleaned_proctor_df = cleaned_proctor_df.replace(['nan', 'NaN'], '')
                    
                    # 将pandas的NaN替换为空值
                    cleaned_proctor_df = cleaned_proctor_df.fillna('')
                    
                    cleaned_proctor_df.to_excel(writer, sheet_name='监考员设置_已处理', index=False)
                
                # 导出考试科目设置
                if hasattr(self, 'processed_subject_df') and self.processed_subject_df is not None:
                    self.processed_subject_df.to_excel(writer, sheet_name='考试科目设置_已处理', index=False)
                
                # 导出带汇总的考场设置表
                if hasattr(self, 'room_with_summary_df') and self.room_with_summary_df is not None:
                    self.room_with_summary_df.to_excel(writer, sheet_name='考场设置_已处理', index=False)
            
            # 导出JSON文件
            self.export_json_results(json_path)
            
            print(f"\n处理结果已导出到:")
            print(f"  Excel格式: {excel_path}")
            print(f"  JSON格式: {json_path}")
            
        except Exception as e:
            print(f"导出结果时出错: {e}")
    
    def export_json_results(self, json_path):
        """
        导出处理结果到JSON文件
        
        Args:
            json_path (str): JSON文件输出路径
        """
        try:
            import json
            
            # 构建JSON数据结构
            result_data = {
                "metadata": {
                    "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "processor_version": "2.0",
                    "description": "考试配置文件处理结果"
                },
                "data": {}
            }
            
            # 添加监考员设置数据
            if hasattr(self, 'processed_proctor_df') and self.processed_proctor_df is not None:
                # 转换DataFrame为字典格式，处理时间类型
                proctor_data = self.processed_proctor_df.copy()
                
                # 处理时间列
                for col in proctor_data.columns:
                    if proctor_data[col].dtype == 'datetime64[ns]':
                        proctor_data[col] = proctor_data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                
                result_data["data"]["proctor_settings"] = {
                    "description": "监考员设置",
                    "total_count": len(proctor_data),
                    "columns": list(proctor_data.columns),
                    "records": proctor_data.to_dict(orient='records')
                }
            
            # 添加考试科目设置数据
            if hasattr(self, 'subject_df') and self.subject_df is not None:
                subject_data = self.subject_df.copy()
                
                # 处理时间列 - 兼容字符串和datetime类型
                for col in subject_data.columns:
                    if subject_data[col].dtype == 'datetime64[ns]':
                        subject_data[col] = subject_data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                    elif col in ['开始时间', '结束时间'] and '考试日期' in subject_data.columns:
                        # 处理字符串类型的时间数据，组合日期和时间
                        for idx in subject_data.index:
                            date_val = subject_data.loc[idx, '考试日期']
                            time_val = subject_data.loc[idx, col]
                            
                            if pd.notna(date_val) and pd.notna(time_val):
                                # 标准化日期格式
                                date_str = str(date_val).replace('/', '-')
                                # 标准化时间格式
                                time_str = str(time_val)
                                if ':' in time_str and time_str.count(':') == 1:
                                    time_str += ':00'
                                
                                # 组合完整的datetime字符串
                                full_datetime = f"{date_str} {time_str}"
                                subject_data.loc[idx, col] = full_datetime
                
                # 计算考试时长
                if '开始时间' in subject_data.columns and '结束时间' in subject_data.columns:
                    subject_data['考试时长'] = pd.to_datetime(subject_data['结束时间']) - pd.to_datetime(subject_data['开始时间'])
                    subject_data['考试时长(分钟)'] = subject_data['考试时长'].dt.total_seconds() / 60
                    # 删除考试时长列，因为它是Timedelta对象，不能直接序列化为JSON
                    del subject_data['考试时长']
                
                # 将所有数值列转换为Python原生类型
                for col in subject_data.columns:
                    if subject_data[col].dtype in ['int64', 'float64']:
                        subject_data[col] = subject_data[col].astype(float).tolist()
                
                result_data["data"]["subject_settings"] = {
                    "description": "考试科目设置",
                    "total_count": len(subject_data),
                    "columns": list(subject_data.columns),
                    "records": subject_data.to_dict(orient='records')
                }
            
            # 添加考场设置数据（含汇总）
            if hasattr(self, 'room_df') and self.room_df is not None:
                room_data = self.room_df.copy()
                
                # 将所有数值列转换为Python原生类型
                for col in room_data.columns:
                    if room_data[col].dtype in ['int64', 'float64']:
                        room_data[col] = room_data[col].astype(float).tolist()
                
                result_data["data"]["room_settings"] = {
                    "description": "考场设置",
                    "total_count": len(room_data),
                    "columns": list(room_data.columns),
                    "records": room_data.to_dict(orient='records')
                }
            
            # 添加统计信息
            stats = self.generate_statistics()
            if stats:
                result_data["statistics"] = stats
            
            # 写入JSON文件
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            print(f"JSON数据导出成功")
            
        except Exception as e:
            print(f"导出JSON文件时出错: {e}")
    
    def generate_statistics(self):
        """
        生成统计信息
        
        Returns:
            dict: 统计信息字典
        """
        try:
            stats = {}
            
            # 监考员统计
            if hasattr(self, 'processed_proctor_df') and self.processed_proctor_df is not None:
                proctor_stats = {}
                
                # 统计各科目监考员分布
                subject_columns = [col for col in self.processed_proctor_df.columns if col.endswith('_监考')]
                subjects = [col.replace('_监考', '') for col in subject_columns]
                
                subject_stats = {}
                for subject in subjects:
                    column_name = f'{subject}_监考'
                    if column_name in self.processed_proctor_df.columns:
                        subject_stats[subject] = {
                            'b': int((self.processed_proctor_df[column_name] == 'b').sum()),
                            'b+': int((self.processed_proctor_df[column_name] == 'b+').sum()),
                            'b-': int((self.processed_proctor_df[column_name] == 'b-').sum()),
                            'y': int((self.processed_proctor_df[column_name] == 'y').sum()),
                            'y+': int((self.processed_proctor_df[column_name] == 'y+').sum()),
                            'y-': int((self.processed_proctor_df[column_name] == 'y-').sum()),
                            'n': int((self.processed_proctor_df[column_name] == 'n').sum()),
                            'empty': int((self.processed_proctor_df[column_name] == '').sum())
                        }
                
                proctor_stats = {
                    'total_proctors': len(self.processed_proctor_df),
                    'subject_distribution': subject_stats
                }
                stats['proctor_analysis'] = proctor_stats
            
            # 考场统计
            if hasattr(self, 'room_with_summary_df') and self.room_with_summary_df is not None:
                room_data = self.room_with_summary_df
                room_columns = [col for col in room_data.columns if col != '考场']
                
                room_stats = {
                    'total_rooms': len(room_data) - 1,  # 减去汇总行
                    'subjects': room_columns,
                    'room_requirements': {}
                }
                
                # 从汇总行获取每个科目的考场需求
                summary_row = room_data[room_data['考场'] == '总监考人员需求']
                if not summary_row.empty:
                    for col in room_columns:
                        room_stats['room_requirements'][col] = int(summary_row[col].iloc[0])
                
                stats['room_analysis'] = room_stats
            
            # 科目统计
            if hasattr(self, 'processed_subject_df') and self.processed_subject_df is not None:
                subject_data = self.processed_subject_df
                subject_stats = {
                    'total_subjects': len(subject_data)
                }
                
                if '考试时长(分钟)' in subject_data.columns:
                    valid_durations = subject_data['考试时长(分钟)'][subject_data['考试时长(分钟)'] > 0]
                    if len(valid_durations) > 0:
                        subject_stats['average_duration_minutes'] = float(valid_durations.mean())
                        subject_stats['total_exam_time_minutes'] = float(valid_durations.sum())
                
                stats['subject_analysis'] = subject_stats
            
            return stats
            
        except Exception as e:
            print(f"生成统计信息时出错: {e}")
            return {}


def main():
    """主函数"""
    print("考试配置文件处理程序")
    print("="*30)
    
    # 初始化处理器 - 自动检测正确的路径
    import os
    
    # 尝试不同的路径
    possible_paths = [
        r'Template\Exam_Config.xlsx',      # 从项目根目录运行
        r'..\Template\Exam_Config.xlsx',   # 从File Processing目录运行
        r'..\..\Template\Exam_Config.xlsx' # 其他情况
    ]
    
    template_path = None
    for path in possible_paths:
        if os.path.exists(path):
            template_path = path
            break
    
    if template_path is None:
        print("错误: 找不到模板文件 Exam_Config.xlsx")
        print("请确保模板文件位于 Template 目录中")
        return
    
    processor = ExamConfigProcessor(template_path)
    
    # 读取模板文件
    if not processor.read_template_file():
        print("程序退出：无法读取模板文件")
        return
    
    # 处理监考员设置
    # 主要依据表格中每个监考员的个人设置，不使用全局的必监考/不监考科目
    # 如果需要全局设置，可以在这里添加 required_subjects 和 forbidden_subjects 参数
    processor.process_proctor_settings(
        required_subjects=None,    # 不使用全局必监考科目
        forbidden_subjects=None,   # 不使用全局不监考科目
        default_value='y'          # 未明确设置的科目默认为可监考
    )
    
    # 计算考试时长
    processor.calculate_exam_duration()
    
    # 创建带汇总的考场设置表
    processor.create_room_setting_with_summary()
    
    # 显示处理结果
    processor.display_results()
    
    # 导出处理结果到当前目录
    output_path = os.path.join(os.getcwd(), '考试配置处理结果.xlsx')
    processor.export_results(output_path)
    print(f"文件已保存到: {output_path}")


if __name__ == "__main__":
    main()
