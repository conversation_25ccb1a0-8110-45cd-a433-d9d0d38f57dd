#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新app.py以使用生产环境配置
"""

import os
import re
import sys
import shutil
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('update_config')

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.bak"
    shutil.copy2(file_path, backup_path)
    logger.info(f"已备份文件: {backup_path}")
    return backup_path

def update_app_config():
    """更新app.py配置"""
    app_file = 'app.py'
    
    # 检查文件是否存在
    if not os.path.exists(app_file):
        logger.error(f"文件不存在: {app_file}")
        return False
    
    # 备份文件
    backup_file(app_file)
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新配置
    # 1. 替换SECRET_KEY
    content = re.sub(
        r"app\.config\['SECRET_KEY'\] = '.*?'",
        "app.config.from_pyfile('config.py', silent=True)",
        content
    )
    
    # 2. 替换debug=True
    content = re.sub(
        r"app\.run\(debug=True\)",
        "app.run(host='0.0.0.0', port=5000)",
        content
    )
    
    # 写入更新后的内容
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"已更新文件: {app_file}")
    return True

def main():
    """主函数"""
    try:
        if update_app_config():
            logger.info("配置更新成功")
        else:
            logger.error("配置更新失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"更新过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
