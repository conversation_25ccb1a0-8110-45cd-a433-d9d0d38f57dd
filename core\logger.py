import os
import time
from enum import Enum
from typing import Any, Dict, List, Optional
import json

class LogLevel(Enum):
    SYSTEM = "SYSTEM"      # 系统信息
    PROGRESS = "PROGRESS"  # 进度信息
    DATA = "DATA"          # 数据信息
    DEBUG = "DEBUG"        # 调试信息
    RESULT = "RESULT"      # 结果信息
    ERROR = "ERROR"        # 错误信息

class RunMode(Enum):
    TEST = "test"
    PRODUCTION = "production"

class Logger:
    def __init__(self, mode: str = "production"):
        self.mode = RunMode(mode.lower())
        self.current_progress = 0
        self.total_steps = 5
        self.start_time = time.time()
        
        # 定义不同模式下允许的日志级别
        self.mode_levels = {
            RunMode.TEST: [level for level in LogLevel],
            RunMode.PRODUCTION: [LogLevel.SYSTEM, LogLevel.PROGRESS, LogLevel.ERROR]
        }
        
        # 脱敏规则
        self.sanitize_rules = {
            "numbers": True,      # 数字脱敏
            "names": True,        # 姓名脱敏
            "terms": True         # 术语替换
        }
        
        # 术语替换字典
        self.term_mapping = {
            "监考员": "人员",
            "考场": "地点",
            "科目": "项目"
        }
    
    def _should_log(self, level: LogLevel) -> bool:
        """判断是否应该输出该级别的日志"""
        return level in self.mode_levels[self.mode]
    
    def _sanitize(self, message: str) -> str:
        """对消息进行脱敏处理"""
        if self.mode == RunMode.TEST:
            return message
            
        # 在生产模式下进行脱敏
        sanitized = message
        
        # 替换术语
        if self.sanitize_rules["terms"]:
            for term, replacement in self.term_mapping.items():
                sanitized = sanitized.replace(term, replacement)
        
        # 替换具体数字
        if self.sanitize_rules["numbers"]:
            import re
            sanitized = re.sub(r'\d+名|\d+个', 'N个', sanitized)
            sanitized = re.sub(r'\d+\.\d+%', 'N%', sanitized)
        
        # 替换姓名
        if self.sanitize_rules["names"]:
            import re
            sanitized = re.sub(r'[赵钱孙李周吴郑王][\u4e00-\u9fa5]', '***', sanitized)
        
        return sanitized
    
    def _format_message(self, level: LogLevel, message: str) -> str:
        """格式化日志消息"""
        if level == LogLevel.PROGRESS:
            return f"[{self.current_progress}/{self.total_steps}] {message}"
        elif self.mode == RunMode.TEST:
            return f"[{level.value}] {message}"
        else:
            return message
    
    def _log(self, level: LogLevel, message: str):
        """基础日志输出方法"""
        if self._should_log(level):
            formatted = self._format_message(level, message)
            sanitized = self._sanitize(formatted)
            print(sanitized)
    
    def system(self, message: str):
        """系统信息"""
        self._log(LogLevel.SYSTEM, message)
    
    def progress(self, message: str, step: Optional[int] = None):
        """进度信息"""
        if step is not None:
            self.current_progress = step
        self._log(LogLevel.PROGRESS, message)
    
    def data(self, message: str):
        """数据信息"""
        self._log(LogLevel.DATA, message)
    
    def debug(self, message: str):
        """调试信息"""
        self._log(LogLevel.DEBUG, message)
    
    def result(self, message: str):
        """结果信息"""
        self._log(LogLevel.RESULT, message)
    
    def error(self, message: str):
        """错误信息"""
        self._log(LogLevel.ERROR, f"错误: {message}")
    
    def update_progress_bar(self, percentage: float):
        """更新进度条"""
        if self.mode == RunMode.PRODUCTION:
            bar_width = 20
            filled = int(bar_width * percentage)
            bar = '=' * filled + ' ' * (bar_width - filled)
            print(f"\r[{self.current_progress}/{self.total_steps}] 处理中 [{bar}] {percentage:.0%}", end='')
            if percentage >= 1:
                print()  # 完成时换行
    
    def complete(self):
        """完成处理"""
        duration = time.time() - self.start_time
        if self.mode == RunMode.TEST:
            self._log(LogLevel.RESULT, f"处理完成，耗时: {duration:.2f}秒")
        else:
            self._log(LogLevel.PROGRESS, "任务完成") 