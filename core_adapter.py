import os
import json
import pandas as pd
from core.core import EnhancedProctorScheduler
from core.File_Processing import ExamConfigProcessor
from core.logger import Logger

class CoreAdapter:
    def __init__(self, task_id, input_file, output_file, db=None, Task=None, redis_client=None):
        self.task_id = task_id
        self.input_file = input_file
        self.output_file = output_file
        self.db = db
        self.Task = Task
        self.redis_client = redis_client
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置与网站系统兼容的日志器"""
        logger = Logger(mode="production")
        logger.add_progress_callback = self._update_progress
        return logger
    
    def _update_progress(self, progress, message):
        """更新进度到网站系统"""
        try:
            if self.db and self.Task:
                # 更新数据库进度
                task = self.Task.query.get(self.task_id)
                if task:
                    task.progress = progress
                    self.db.session.commit()
            
            if self.redis_client:
                # 更新Redis进度
                progress_key = f'task:{self.task_id}:progress'
                progress_info = {
                    'status': 'processing',
                    'progress': progress,
                    'message': message
                }
                self.redis_client.setex(progress_key, 3600, json.dumps(progress_info))
            
        except Exception as e:
            self.logger.error(f"更新进度失败: {str(e)}")
    
    def process(self):
        """处理任务的主函数"""
        try:
            # 1. 初始化处理器
            processor = ExamConfigProcessor(self.input_file)
            self.logger.system("初始化处理器完成")
            
            # 2. 读取并处理配置
            if not processor.read_template_file():
                raise Exception("无法读取模板文件")
            
            # 3. 处理监考员设置
            processor.process_proctor_settings()
            self.logger.data(f"监考员总数: {len(processor.proctor_df)}")
            
            # 4. 计算考试时长
            processor.calculate_exam_duration()
            self.logger.data(f"考试科目数: {len(processor.subject_df)}")
            
            # 5. 创建考场设置
            processor.create_room_setting_with_summary()
            self.logger.data(f"考场数量: {len(processor.room_df)}")
            
            # 6. 导出中间结果
            temp_json = os.path.join(
                os.path.dirname(self.output_file), 
                '考试配置处理结果.json'
            )
            processor.export_json_results(temp_json)
            
            # 7. 初始化调度器
            scheduler = EnhancedProctorScheduler(config_file=temp_json)
            self.logger.system("调度器初始化完成")
            
            # 8. 构建和求解模型
            scheduler.build_enhanced_model()
            scheduler.solve_enhanced_model()
            
            # 9. 导出结果
            self._export_results(scheduler)
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理失败: {str(e)}")
            return False
            
    def _export_results(self, scheduler):
        """按原系统格式导出结果"""
        with pd.ExcelWriter(self.output_file) as writer:
            # 1. 考场安排表
            room_df = scheduler._create_room_arrangement_df()
            room_df.to_excel(writer, sheet_name='考场安排', index=False)
            
            # 2. 监考员安排表
            proctor_df = scheduler._create_enhanced_proctor_df()
            proctor_df.to_excel(writer, sheet_name='监考员安排', index=False)
            
            # 3. 统计信息
            stats_df = scheduler._create_statistics_df()
            stats_df.to_excel(writer, sheet_name='统计信息', index=False) 