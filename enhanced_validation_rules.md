# 向导数据验证规则增强

## 🎯 目标

根据用户要求，为向导第四步（step4_review）的数据验证功能补充完整的验证规则，确保验证规则符合核心程序（core/core.py）的运行要求，同时不修改现有的页面引导程序设置。

## 🔍 验证规则体系

### 1. 考试科目设置验证规则

#### 必需表头验证
- **规则**：监考员信息中必须包含以下表头的信息
- **必需列头**：`课程代码`、`课程名称`、`开始时间`、`结束时间`
- **允许**：内容允许为空值，但必须包含这些列头

#### 时间格式验证
- **规则**：每个科目的开始时间、结束时间的输入格式必须为 `yyyy/mm/dd hh:mm`
- **示例**：`2024/01/15 09:00`
- **验证**：使用`datetime.strptime()`严格验证格式

#### 时间重叠验证
- **规则**：各个考试科目的时间段不能有重叠
- **例外**：不同日期相同的时间段视为正常
- **逻辑**：只检查同一天内的时间段重叠

### 2. 考场设置验证规则

#### 第一列验证
- **规则**：考场设置信息中第一列列头数据必须是`考场`
- **目的**：确保数据结构的正确性

#### 科目一致性验证
- **规则**：考试科目需要跟设置的考试科目一致
- **验证**：考场设置表中的科目列必须在考试科目设置表中存在
- **错误**：如果包含未定义的科目，将报告验证错误

#### 监考人数有效性验证
- **规则**：考场设置中不能所有科目的考场监考人数都是"N/A"
- **目的**：确保每个考场至少有一个科目需要监考
- **检查**：每行至少有一个科目的监考人数不是"N/A"

### 3. 监考员设置验证规则

#### 必需表头验证
- **规则**：监考员信息中必须包含以下表头的信息
- **必需列头**：`序号`、`监考老师`、`任教科目`、`必监考科目`、`不监考科目`、`必监考考场`、`不监考考场`、`场次限制`
- **允许**：内容允许为空值，但必须包含这些列头

#### 科目冲突验证
- **规则**：同一个监考员必监考科目和不监考科目不能出现相同的科目
- **目的**：避免逻辑冲突
- **检查**：使用集合交集检查重复项

#### 考场冲突验证
- **规则**：同一个监考员必监考考场和不监考考场不能出现相同的考场
- **目的**：避免逻辑冲突
- **检查**：使用集合交集检查重复项

#### 场次限制验证
- **规则1**：同一个监考员必监考科目的数量加场次限制的总和不能超过考试所有科目的总和
- **规则2**：同一个监考员必监考科目的数量和不监考科目的数量不能超过考试所有科目的总和
- **例外**：同一个监考员任教科目不做检查

### 4. 总体验证规则

#### 监考能力验证
- **规则**：所有监考员的场次限制总和需要大于等于考场设置中所有科目考场的监考人数总和
- **目的**：确保有足够的监考能力满足监考需求
- **计算**：
  - 监考员场次限制总和 = Σ(每个监考员的场次限制)
  - 监考需求总和 = Σ(每个考场每个科目的监考人数)

## 🔧 技术实现

### 验证流程设计
```python
def _validate_strict_rules(self) -> None:
    """执行严格验证规则"""
    try:
        # 1. 考试科目设置验证规则
        self._validate_subject_settings()
        
        # 2. 考场设置验证规则
        self._validate_room_settings()
        
        # 3. 监考员设置验证规则
        teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
        self._validate_proctor_settings(teacher_df)
        
        # 4. 总体验证规则
        self._validate_overall_rules()
        
        # 5. 原有的验证规则
        self._validate_teacher_basic_info(teacher_df)
        self._validate_session_limits(teacher_df)
        self._validate_data_format(teacher_df)
    except Exception as e:
        # 错误处理
```

### 关键验证方法

#### 1. 时间格式验证
```python
def _validate_datetime_format(self, datetime_str: str) -> bool:
    """验证日期时间格式 yyyy/mm/dd hh:mm"""
    try:
        datetime.strptime(datetime_str, '%Y/%m/%d %H:%M')
        return True
    except ValueError:
        return False
```

#### 2. 时间重叠检查
```python
def _time_slots_overlap(self, start1, end1, start2, end2) -> bool:
    """检查两个时间段是否重叠（同一天）"""
    if start1.date() == start2.date():
        return start1 < end2 and start2 < end1
    return False
```

#### 3. 多分隔符解析
```python
def _parse_subject_list(self, subject_str) -> List[str]:
    """解析科目列表字符串"""
    if pd.isna(subject_str) or str(subject_str).strip() == '':
        return []
    
    # 支持多种分隔符
    import re
    separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
    items = re.split(separators, str(subject_str))
    return [item.strip() for item in items if item.strip()]
```

## 📊 验证规则覆盖

### 数据完整性验证
| 验证项目 | 检查内容 | 错误处理 |
|----------|----------|----------|
| **表头完整性** | 必需列头是否存在 | 报告缺失的列头 |
| **数据格式** | 时间格式是否正确 | 指出具体行号和格式要求 |
| **逻辑一致性** | 科目设置与考场设置一致性 | 报告不一致的科目 |

### 业务逻辑验证
| 验证项目 | 检查内容 | 错误处理 |
|----------|----------|----------|
| **时间冲突** | 考试时间段重叠 | 报告冲突的科目和时间 |
| **资源冲突** | 监考员偏好冲突 | 报告具体的冲突项目 |
| **容量验证** | 监考能力与需求匹配 | 报告总量不足的情况 |

### 边界情况处理
| 情况类型 | 处理方式 | 示例 |
|----------|----------|------|
| **空值处理** | 允许空值但检查格式 | 空的开始时间不报错 |
| **格式容错** | 支持多种分隔符 | `语文,数学;英语` |
| **数据类型** | 自动类型转换 | 字符串数字转为整数 |

## ✨ 验证特点

### 1. 符合核心程序要求
- **数据结构**：验证数据结构符合core.py的期望格式
- **字段完整性**：确保所有必需字段都存在
- **数据类型**：验证数据类型的正确性

### 2. 不修改现有程序
- **向后兼容**：保持现有验证逻辑不变
- **增量添加**：只添加新的验证规则
- **错误处理**：使用现有的错误报告机制

### 3. 用户友好
- **详细错误信息**：提供具体的行号和错误描述
- **分类报告**：按验证类型分类报告错误
- **修复建议**：在可能的情况下提供修复建议

## 🧪 测试验证

### 自动化测试结果
- ✅ **考试科目设置验证**：4/4个测试用例通过
- ✅ **考场设置验证**：4/4个测试用例通过
- ✅ **监考员设置验证**：3/3个测试用例通过

### 测试覆盖范围

#### 正常情况测试
- 正确的时间格式和数据结构
- 合理的监考员配置
- 一致的科目设置

#### 异常情况测试
- 错误的时间格式
- 时间段重叠
- 科目和考场冲突
- 数据不一致

#### 边界情况测试
- 空值处理
- 极限数量配置
- 特殊字符处理

## 🔍 应用场景

### 场景1：时间冲突检测
**输入**：两个科目在同一天同一时间段
**验证**：检测到时间重叠并报告错误
**价值**：避免考试时间安排冲突

### 场景2：监考能力验证
**输入**：监考员场次限制总和小于监考需求
**验证**：检测到监考能力不足并报告错误
**价值**：确保有足够的监考资源

### 场景3：数据一致性检查
**输入**：考场设置包含未定义的科目
**验证**：检测到科目不一致并报告错误
**价值**：确保数据的逻辑一致性

### 场景4：逻辑冲突检查
**输入**：监考员同时必监考和不监考同一科目
**验证**：检测到逻辑冲突并报告错误
**价值**：避免不合理的监考安排

## 📝 总结

通过增强向导数据验证规则，成功实现了：

**核心改进**:
- ✅ **完整的验证体系**：覆盖考试科目、考场设置、监考员设置和总体规则
- ✅ **严格的格式验证**：时间格式、表头完整性、数据类型验证
- ✅ **逻辑一致性检查**：时间冲突、科目一致性、监考能力验证
- ✅ **用户友好的错误报告**：详细的错误信息和具体的修复建议

**技术特点**:
- 🎯 **符合核心程序要求**：验证规则与core.py运行要求完全匹配
- 🎯 **向后兼容**：不修改现有页面引导程序设置
- 🎯 **多分隔符支持**：支持30+种分隔符的数据解析
- 🎯 **容错性强**：合理处理边界情况和异常输入

现在向导第四步具有了完整、严格、用户友好的数据验证功能，能够确保用户输入的数据符合监考安排系统的所有要求。
