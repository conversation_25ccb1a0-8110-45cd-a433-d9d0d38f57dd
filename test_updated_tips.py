#!/usr/bin/env python3
"""
测试更新后的使用提示
验证三列布局的使用提示是否正确显示所有功能
"""

import os

def check_updated_tips_structure():
    """检查更新后的使用提示结构"""
    print("🔍 检查更新后的使用提示结构...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本结构
        structure_checks = {
            '使用提示标题': '使用提示' in content,
            '三列布局': 'col-md-4' in content,
            '行布局': 'class="row"' in content,
            '图标使用': 'fas fa-info-circle' in content,
            '响应式设计': 'flex-grow-1' in content
        }
        
        all_passed = True
        for check_name, result in structure_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_tips_content():
    """检查使用提示内容"""
    print("\n🔍 检查使用提示内容...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查三个主要功能区域
        content_checks = {
            # 添加考场功能
            '添加考场标题': '添加考场' in content,
            '单个添加提示': '单个添加' in content,
            '批量添加提示': '批量添加' in content,
            'Excel导入提示': 'Excel导入' in content,
            
            # 编辑考场功能
            '编辑考场标题': '编辑考场' in content,
            '单个编辑提示': '单个编辑' in content,
            '批量编辑提示': '批量编辑' in content,
            '监考人数提示': '监考人数' in content,
            
            # 删除考场功能
            '删除考场标题': '删除：' in content,
            '单个删除提示': '单个删除' in content,
            '批量删除提示': '批量删除' in content,
            '全选功能提示': '全选' in content
        }
        
        all_passed = True
        
        print("   添加考场功能:")
        for key in ['添加考场标题', '单个添加提示', '批量添加提示', 'Excel导入提示']:
            result = content_checks[key]
            status = "✅" if result else "❌"
            print(f"     {status} {key}")
            if not result:
                all_passed = False
        
        print("   编辑考场功能:")
        for key in ['编辑考场标题', '单个编辑提示', '批量编辑提示', '监考人数提示']:
            result = content_checks[key]
            status = "✅" if result else "❌"
            print(f"     {status} {key}")
            if not result:
                all_passed = False
        
        print("   删除考场功能:")
        for key in ['删除考场标题', '单个删除提示', '批量删除提示', '全选功能提示']:
            result = content_checks[key]
            status = "✅" if result else "❌"
            print(f"     {status} {key}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_visual_elements():
    """检查视觉元素"""
    print("\n🔍 检查视觉元素...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查图标和样式
        visual_checks = {
            '主图标': 'fas fa-info-circle fa-lg' in content,
            '添加图标': 'fas fa-plus-circle' in content,
            '编辑图标': 'fas fa-edit' in content,
            '删除图标': 'fas fa-trash-alt' in content,
            '颜色分类': 'text-primary' in content and 'text-warning' in content and 'text-danger' in content,
            '小字体': 'small' in content,
            '标题样式': 'alert-heading' in content
        }
        
        all_passed = True
        for check_name, result in visual_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_layout_efficiency():
    """分析布局效率"""
    print("\n📊 分析布局效率...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取使用提示部分
        start_marker = 'class="alert alert-info mb-4"'
        end_marker = '</div>\n                    </div>'
        
        start_pos = content.find(start_marker)
        end_pos = content.find(end_marker, start_pos)
        
        if start_pos != -1 and end_pos != -1:
            tips_content = content[start_pos:end_pos + len(end_marker)]
            
            # 分析布局特点
            analysis = {
                '使用三列布局': tips_content.count('col-md-4') == 3,
                '内容分组合理': '添加：' in tips_content and '编辑：' in tips_content and '删除：' in tips_content,
                '图标分类清晰': 'fa-plus-circle' in tips_content and 'fa-edit' in tips_content and 'fa-trash-alt' in tips_content,
                '颜色编码': 'text-primary' in tips_content and 'text-warning' in tips_content and 'text-danger' in tips_content,
                '响应式设计': 'col-md-4' in tips_content and 'flex-grow-1' in tips_content
            }
            
            # 统计行数（估算）
            lines_count = tips_content.count('\n')
            
            print(f"   📏 估算行数: {lines_count} 行")
            print(f"   📱 响应式设计: {'✅' if analysis['响应式设计'] else '❌'}")
            print(f"   🎨 视觉分类: {'✅' if analysis['图标分类清晰'] and analysis['颜色编码'] else '❌'}")
            print(f"   📋 内容组织: {'✅' if analysis['内容分组合理'] else '❌'}")
            print(f"   📐 三列布局: {'✅' if analysis['使用三列布局'] else '❌'}")
            
            # 与原版本对比（估算）
            original_estimated_lines = 12  # 原来的单列列表大约12行
            current_estimated_lines = lines_count

            if current_estimated_lines <= original_estimated_lines:  # 不超过原来的行数
                print(f"   ✅ 纵向空间优化: 当前约{current_estimated_lines}行，原来约{original_estimated_lines}行")
                return True
            else:
                print(f"   ⚠️  纵向空间: 当前约{current_estimated_lines}行，原来约{original_estimated_lines}行")
                # 如果行数相近，也算通过
                if current_estimated_lines <= original_estimated_lines * 1.3:
                    return True
                else:
                    return False
        else:
            print("   ❌ 无法找到使用提示内容")
            return False
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def test_content_completeness():
    """测试内容完整性"""
    print("\n🧪 测试内容完整性...")
    
    # 定义应该包含的所有功能点
    expected_features = {
        '添加功能': [
            '单个添加',
            '批量添加', 
            'Excel导入'
        ],
        '编辑功能': [
            '单个编辑',
            '批量编辑',
            '监考人数'
        ],
        '删除功能': [
            '单个删除',
            '批量删除',
            '全选'
        ]
    }
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        all_passed = True
        
        for category, features in expected_features.items():
            print(f"   {category}:")
            for feature in features:
                if feature in content:
                    print(f"     ✅ {feature}")
                else:
                    print(f"     ❌ {feature}")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    print("\n📱 测试响应式设计...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查响应式设计元素
        responsive_checks = {
            'Bootstrap网格': 'col-md-4' in content,
            '弹性布局': 'flex-grow-1' in content,
            '响应式间距': 'mb-3' in content or 'mb-2' in content,
            '小屏幕适配': 'col-md-' in content,  # 使用md断点
            '容器布局': 'class="row"' in content
        }
        
        all_passed = True
        for check_name, result in responsive_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 更新后的使用提示测试")
    print("=" * 50)
    print("测试三列布局的使用提示是否正确显示所有功能")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查使用提示结构
    test_results.append(("使用提示结构", check_updated_tips_structure()))
    
    # 2. 检查使用提示内容
    test_results.append(("使用提示内容", check_tips_content()))
    
    # 3. 检查视觉元素
    test_results.append(("视觉元素", check_visual_elements()))
    
    # 4. 分析布局效率
    test_results.append(("布局效率", analyze_layout_efficiency()))
    
    # 5. 测试内容完整性
    test_results.append(("内容完整性", test_content_completeness()))
    
    # 6. 测试响应式设计
    test_results.append(("响应式设计", test_responsive_design()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 更新后的特点:")
        print("✅ 三列布局减少纵向占用")
        print("✅ 功能分类清晰（添加/编辑/删除）")
        print("✅ 图标和颜色编码直观")
        print("✅ 包含所有新增的批量操作功能")
        print("✅ 响应式设计适配不同屏幕")
        print("✅ 内容简洁明了，易于理解")
        
        print("\n🎨 设计特色:")
        print("🟦 添加功能 - 蓝色主题 (fa-plus-circle)")
        print("🟨 编辑功能 - 黄色主题 (fa-edit)")
        print("🟥 删除功能 - 红色主题 (fa-trash-alt)")
        
        print("\n📱 布局优势:")
        print("• 三列布局充分利用横向空间")
        print("• 减少纵向滚动需求")
        print("• 功能分组逻辑清晰")
        print("• 响应式设计适配移动端")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关内容。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
