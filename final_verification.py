#!/usr/bin/env python3
"""
最终验证脚本 - 确保Excel模板下载和导入功能完全正确实现
"""

import os
import sys
import pandas as pd

def verify_template_files():
    """验证模板文件存在且格式正确"""
    print("📁 验证模板文件...")
    
    expected_files = {
        'template-guide/kemu.xlsx': {
            'description': '科目设置模板',
            'expected_sheet': '考试科目设置',
            'expected_columns': ['课程代码', '课程名称', '开始时间', '结束时间']
        },
        'template-guide/kaochang.xlsx': {
            'description': '考场设置模板', 
            'expected_sheet': '考场设置',
            'expected_columns': ['考场', '语文', '数学', '英语']
        },
        'template-guide/jiankaoyuan.xlsx': {
            'description': '监考员设置模板',
            'expected_sheet': '监考员设置', 
            'expected_columns': ['序号', '监考老师', '任教科目', '必监考科目', '不监考科目', '必监考考场', '不监考考场', '场次限制']
        }
    }
    
    all_valid = True
    
    for file_path, config in expected_files.items():
        print(f"\n--- 验证 {config['description']} ---")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            all_valid = False
            continue
        
        try:
            # 检查工作表
            excel_file = pd.ExcelFile(file_path)
            if config['expected_sheet'] not in excel_file.sheet_names:
                print(f"❌ 缺少工作表: {config['expected_sheet']}")
                print(f"   实际工作表: {excel_file.sheet_names}")
                all_valid = False
                continue
            
            # 检查列名
            df = pd.read_excel(file_path, sheet_name=config['expected_sheet'])
            actual_columns = list(df.columns)
            
            missing_columns = [col for col in config['expected_columns'] if col not in actual_columns]
            if missing_columns:
                print(f"❌ 缺少列: {missing_columns}")
                print(f"   实际列: {actual_columns}")
                all_valid = False
                continue
            
            print(f"✅ 文件格式正确")
            print(f"   工作表: {config['expected_sheet']}")
            print(f"   列数: {len(actual_columns)}")
            print(f"   数据行数: {len(df)}")
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            all_valid = False
    
    return all_valid

def verify_import_functions():
    """验证导入函数正确工作"""
    print("\n🔧 验证导入函数...")
    
    try:
        sys.path.append('.')
        from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
        print("✅ 成功导入应用模块")
    except ImportError as e:
        print(f"❌ 无法导入应用模块: {e}")
        return False
    
    test_cases = [
        ('template-guide/kemu.xlsx', import_subjects_from_excel, '科目导入'),
        ('template-guide/kaochang.xlsx', import_rooms_from_excel, '考场导入'),
        ('template-guide/jiankaoyuan.xlsx', import_proctors_from_excel, '监考员导入')
    ]
    
    all_working = True
    
    for file_path, import_func, description in test_cases:
        print(f"\n--- 测试 {description} ---")
        
        if not os.path.exists(file_path):
            print(f"❌ 模板文件不存在: {file_path}")
            all_working = False
            continue
        
        try:
            data = import_func(file_path)
            print(f"✅ {description}成功")
            print(f"   导入数据数量: {len(data)}")
            
            if len(data) > 0:
                print(f"   示例数据: {data[0]}")
            else:
                print(f"   ⚠️  没有导入任何数据")
                
        except Exception as e:
            print(f"❌ {description}失败: {e}")
            all_working = False
    
    return all_working

def verify_route_definitions():
    """验证路由定义是否存在"""
    print("\n🌐 验证路由定义...")
    
    try:
        sys.path.append('.')
        from app import app
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append((rule.rule, rule.methods, rule.endpoint))
        
        # 检查必需的路由
        required_routes = [
            ('/wizard/download-template/<template_type>', 'wizard_download_template'),
            ('/wizard/import-excel/<step_type>', 'wizard_import_excel')
        ]
        
        all_routes_exist = True
        
        for route_pattern, endpoint in required_routes:
            # 查找匹配的路由
            found = False
            for rule, methods, rule_endpoint in routes:
                if endpoint == rule_endpoint:
                    found = True
                    print(f"✅ 路由存在: {rule} -> {endpoint}")
                    print(f"   支持方法: {methods}")
                    break
            
            if not found:
                print(f"❌ 路由缺失: {route_pattern} -> {endpoint}")
                all_routes_exist = False
        
        return all_routes_exist
        
    except Exception as e:
        print(f"❌ 验证路由失败: {e}")
        return False

def verify_page_templates():
    """验证页面模板包含必要的按钮"""
    print("\n📄 验证页面模板...")
    
    template_files = [
        ('templates/wizard/step1_subjects.html', '科目设置页面'),
        ('templates/wizard/step2_rooms.html', '考场设置页面'),
        ('templates/wizard/step3_proctors.html', '监考员设置页面')
    ]
    
    all_templates_valid = True
    
    for template_path, description in template_files:
        print(f"\n--- 验证 {description} ---")
        
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在: {template_path}")
            all_templates_valid = False
            continue
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查必要的元素
            has_import_btn = '从Excel导入' in content or '导入数据' in content
            has_download_btn = '下载导入模板' in content or '下载模板' in content
            has_import_url = 'wizard_import_excel' in content
            has_download_url = 'wizard_download_template' in content
            
            print(f"   导入按钮: {'✅' if has_import_btn else '❌'}")
            print(f"   下载按钮: {'✅' if has_download_btn else '❌'}")
            print(f"   导入URL: {'✅' if has_import_url else '❌'}")
            print(f"   下载URL: {'✅' if has_download_url else '❌'}")
            
            if not (has_import_btn and has_download_btn and has_import_url and has_download_url):
                all_templates_valid = False
            else:
                print(f"✅ {description}模板正确")
                
        except Exception as e:
            print(f"❌ 读取模板失败: {e}")
            all_templates_valid = False
    
    return all_templates_valid

def main():
    """主验证函数"""
    print("🔍 Excel模板下载和导入功能最终验证")
    print("=" * 60)
    
    verification_results = []
    
    # 1. 验证模板文件
    verification_results.append(("模板文件格式", verify_template_files()))
    
    # 2. 验证导入函数
    verification_results.append(("导入函数", verify_import_functions()))
    
    # 3. 验证路由定义
    verification_results.append(("路由定义", verify_route_definitions()))
    
    # 4. 验证页面模板
    verification_results.append(("页面模板", verify_page_templates()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 验证结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in verification_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(verification_results)} 项验证通过")
    
    if passed_count == len(verification_results):
        print("\n🎉 所有验证通过！")
        print("Excel模板下载和导入功能已完全正确实现。")
        print("\n📋 功能清单:")
        print("✅ 三个标准模板文件 (kemu.xlsx, kaochang.xlsx, jiankaoyuan.xlsx)")
        print("✅ 模板下载路由和功能")
        print("✅ Excel导入路由和功能") 
        print("✅ 数据解析和验证函数")
        print("✅ 前端下载和导入按钮")
        print("✅ 跨浏览器兼容的文件上传")
        print("✅ CSRF保护和错误处理")
        return 0
    else:
        print("\n❌ 部分验证失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
