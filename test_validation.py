#!/usr/bin/env python3
"""
测试引导页面生成的Excel文件是否能通过验证
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from excel_validator import ExcelValidator

def test_validation():
    """测试验证功能"""
    test_file = 'test_final.xlsx'
    
    if not os.path.exists(test_file):
        print(f"测试文件 {test_file} 不存在")
        return
    
    print(f"正在验证文件: {test_file}")
    
    try:
        validator = ExcelValidator(test_file)
        is_valid = validator.validate()
        
        print(f"验证结果: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        if validator.errors:
            print("\n错误信息:")
            for error in validator.errors:
                print(f"  - {error}")
        
        if validator.warnings:
            print("\n警告信息:")
            for warning in validator.warnings:
                print(f"  - {warning}")
                
        if is_valid:
            print("\n✅ 引导页面生成的Excel文件完全符合验证要求！")
        else:
            print("\n❌ 还需要进一步调整")
            
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_validation()
