#!/usr/bin/env python3
"""
Excel导入功能的综合测试套件
包括单元测试、集成测试和边界条件测试
"""

import unittest
import sys
import os
import tempfile
import pandas as pd
from unittest.mock import patch, MagicMock

# 添加应用路径
sys.path.append('.')

class TestExcelImportFunctions(unittest.TestCase):
    """测试Excel导入函数的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        # 导入应用模块
        try:
            from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
            self.import_subjects = import_subjects_from_excel
            self.import_rooms = import_rooms_from_excel
            self.import_proctors = import_proctors_from_excel
        except ImportError as e:
            self.skipTest(f"无法导入应用模块: {e}")
    
    def create_test_subjects_excel(self):
        """创建测试用的科目Excel文件"""
        data = {
            '课程代码': ['A', 'B', 'C'],
            '课程名称': ['语文', '数学', '英语'],
            '开始时间': ['2025-02-08 09:00:00', '2025-02-08 14:00:00', '2025-02-08 16:30:00'],
            '结束时间': ['2025-02-08 11:30:00', '2025-02-08 16:00:00', '2025-02-08 18:30:00']
        }
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        df = pd.DataFrame(data)
        df.to_excel(temp_file.name, sheet_name='考试科目设置', index=False)
        return temp_file.name
    
    def create_test_rooms_excel(self):
        """创建测试用的考场Excel文件"""
        data = {
            '考场': ['1考场', '2考场', '3考场'],
            '语文': [2, 2, 2],
            '数学': [2, 2, 2],
            '英语': [2, 2, 2]
        }
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        df = pd.DataFrame(data)
        df.to_excel(temp_file.name, sheet_name='考场设置', index=False)
        return temp_file.name
    
    def create_test_proctors_excel(self):
        """创建测试用的监考员Excel文件"""
        data = {
            '监考老师': ['张老师', '李老师', '王老师'],
            '任教科目': ['语文', '数学', '英语'],
            '监考场次限制': [5, 5, 5]
        }
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        df = pd.DataFrame(data)
        df.to_excel(temp_file.name, sheet_name='监考员设置', index=False)
        return temp_file.name
    
    def test_subjects_import_success(self):
        """测试科目导入成功情况"""
        test_file = self.create_test_subjects_excel()
        try:
            subjects = self.import_subjects(test_file)
            self.assertEqual(len(subjects), 3)
            self.assertEqual(subjects[0]['subject_code'], 'A')
            self.assertEqual(subjects[0]['subject_name'], '语文')
            self.assertIn('start_time', subjects[0])
            self.assertIn('end_time', subjects[0])
        finally:
            os.unlink(test_file)
    
    def test_rooms_import_success(self):
        """测试考场导入成功情况"""
        test_file = self.create_test_rooms_excel()
        try:
            rooms = self.import_rooms(test_file)
            self.assertEqual(len(rooms), 3)
            self.assertEqual(rooms[0]['name'], '1考场')
            self.assertIn('demands', rooms[0])
            self.assertEqual(rooms[0]['demands']['语文'], 2)
        finally:
            os.unlink(test_file)
    
    def test_proctors_import_success(self):
        """测试监考员导入成功情况"""
        test_file = self.create_test_proctors_excel()
        try:
            proctors = self.import_proctors(test_file)
            self.assertEqual(len(proctors), 3)
            self.assertEqual(proctors[0]['name'], '张老师')
            self.assertEqual(proctors[0]['teaching_subject'], '语文')
            self.assertEqual(proctors[0]['session_limit'], 5)
        finally:
            os.unlink(test_file)
    
    def test_subjects_import_invalid_file(self):
        """测试科目导入无效文件"""
        with self.assertRaises(Exception):
            self.import_subjects('nonexistent_file.xlsx')
    
    def test_rooms_import_invalid_file(self):
        """测试考场导入无效文件"""
        with self.assertRaises(Exception):
            self.import_rooms('nonexistent_file.xlsx')
    
    def test_proctors_import_invalid_file(self):
        """测试监考员导入无效文件"""
        with self.assertRaises(Exception):
            self.import_proctors('nonexistent_file.xlsx')

class TestExcelImportIntegration(unittest.TestCase):
    """测试Excel导入的集成测试"""
    
    def test_template_files_exist(self):
        """测试模板文件是否存在"""
        template_files = [
            'template-guide/kemu.xlsx',
            'template-guide/kaochang.xlsx', 
            'template-guide/jiankaoyuan.xlsx'
        ]
        
        for file_path in template_files:
            self.assertTrue(os.path.exists(file_path), f"模板文件不存在: {file_path}")
            self.assertGreater(os.path.getsize(file_path), 0, f"模板文件为空: {file_path}")
    
    def test_template_files_import(self):
        """测试使用实际模板文件导入"""
        try:
            from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
            
            # 测试科目模板
            if os.path.exists('template-guide/kemu.xlsx'):
                subjects = import_subjects_from_excel('template-guide/kemu.xlsx')
                self.assertIsInstance(subjects, list)
                self.assertGreater(len(subjects), 0)
            
            # 测试考场模板
            if os.path.exists('template-guide/kaochang.xlsx'):
                rooms = import_rooms_from_excel('template-guide/kaochang.xlsx')
                self.assertIsInstance(rooms, list)
                self.assertGreater(len(rooms), 0)
            
            # 测试监考员模板
            if os.path.exists('template-guide/jiankaoyuan.xlsx'):
                proctors = import_proctors_from_excel('template-guide/jiankaoyuan.xlsx')
                self.assertIsInstance(proctors, list)
                self.assertGreater(len(proctors), 0)
                
        except ImportError:
            self.skipTest("无法导入应用模块")

class TestExcelImportEdgeCases(unittest.TestCase):
    """测试Excel导入的边界条件"""
    
    def setUp(self):
        try:
            from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
            self.import_subjects = import_subjects_from_excel
            self.import_rooms = import_rooms_from_excel
            self.import_proctors = import_proctors_from_excel
        except ImportError:
            self.skipTest("无法导入应用模块")
    
    def test_empty_excel_file(self):
        """测试空Excel文件"""
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        df = pd.DataFrame()
        df.to_excel(temp_file.name, index=False)
        
        try:
            with self.assertRaises(Exception):
                self.import_subjects(temp_file.name)
        finally:
            os.unlink(temp_file.name)
    
    def test_missing_columns(self):
        """测试缺少必需列的Excel文件"""
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        data = {'错误列': ['数据1', '数据2']}
        df = pd.DataFrame(data)
        df.to_excel(temp_file.name, sheet_name='考试科目设置', index=False)
        
        try:
            with self.assertRaises(Exception):
                self.import_subjects(temp_file.name)
        finally:
            os.unlink(temp_file.name)

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行Excel导入功能综合测试...")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestExcelImportFunctions))
    suite.addTests(loader.loadTestsFromTestCase(TestExcelImportIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestExcelImportEdgeCases))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print(f"\n=== 测试结果总结 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    
    if result.wasSuccessful():
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(run_all_tests())
