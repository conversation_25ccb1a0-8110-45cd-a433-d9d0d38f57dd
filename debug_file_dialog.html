<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件对话框调试工具</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>文件对话框调试工具</h1>
        <p>这个页面用于测试不同的文件对话框触发方法</p>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>方法1: 隐藏的文件输入 + 按钮触发</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" id="btn-hidden" class="btn btn-primary">点击选择文件 (隐藏输入)</button>
                        <input type="file" id="file-hidden" accept=".xlsx,.xls" style="display: none;">
                        <div id="result-hidden" class="mt-2 text-muted">等待测试...</div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>方法2: 可见的文件输入</h5>
                    </div>
                    <div class="card-body">
                        <input type="file" id="file-visible" accept=".xlsx,.xls" class="form-control">
                        <div id="result-visible" class="mt-2 text-muted">等待测试...</div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>方法3: 原生JavaScript触发</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" id="btn-native" class="btn btn-success">点击选择文件 (原生JS)</button>
                        <input type="file" id="file-native" accept=".xlsx,.xls" style="display: none;">
                        <div id="result-native" class="mt-2 text-muted">等待测试...</div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>方法4: 创建动态文件输入</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" id="btn-dynamic" class="btn btn-warning">点击选择文件 (动态创建)</button>
                        <div id="result-dynamic" class="mt-2 text-muted">等待测试...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>调试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-log" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;"></div>
                        <button type="button" id="clear-log" class="btn btn-sm btn-secondary mt-2">清除日志</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            $('#debug-log').append(`<div>${logEntry}</div>`);
            $('#debug-log').scrollTop($('#debug-log')[0].scrollHeight);
            console.log(logEntry);
        }

        function updateResult(method, message, success = false) {
            const resultDiv = $(`#result-${method}`);
            resultDiv.text(message);
            resultDiv.removeClass('text-muted text-success text-danger');
            resultDiv.addClass(success ? 'text-success' : 'text-danger');
        }

        $(document).ready(function() {
            log('页面加载完成');
            
            // 检查浏览器信息
            log(`浏览器: ${navigator.userAgent}`);
            log(`jQuery版本: ${$.fn.jquery}`);
            
            // 方法1: 隐藏的文件输入 + jQuery触发
            $('#btn-hidden').click(function(e) {
                e.preventDefault();
                log('方法1: 按钮被点击');
                
                const fileInput = $('#file-hidden');
                log(`方法1: 文件输入元素数量: ${fileInput.length}`);
                
                if (fileInput.length === 0) {
                    log('方法1: 错误 - 找不到文件输入元素');
                    updateResult('hidden', '错误: 找不到文件输入元素');
                    return;
                }
                
                try {
                    fileInput[0].click();
                    log('方法1: 成功触发文件输入点击');
                    updateResult('hidden', '已触发文件对话框', true);
                } catch (error) {
                    log(`方法1: 错误 - ${error.message}`);
                    updateResult('hidden', `错误: ${error.message}`);
                }
            });
            
            // 方法3: 原生JavaScript触发
            document.getElementById('btn-native').addEventListener('click', function(e) {
                e.preventDefault();
                log('方法3: 按钮被点击 (原生JS)');
                
                const fileInput = document.getElementById('file-native');
                if (!fileInput) {
                    log('方法3: 错误 - 找不到文件输入元素');
                    updateResult('native', '错误: 找不到文件输入元素');
                    return;
                }
                
                try {
                    fileInput.click();
                    log('方法3: 成功触发文件输入点击 (原生JS)');
                    updateResult('native', '已触发文件对话框 (原生JS)', true);
                } catch (error) {
                    log(`方法3: 错误 - ${error.message}`);
                    updateResult('native', `错误: ${error.message}`);
                }
            });
            
            // 方法4: 动态创建文件输入
            $('#btn-dynamic').click(function(e) {
                e.preventDefault();
                log('方法4: 按钮被点击 (动态创建)');
                
                try {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.xlsx,.xls';
                    input.style.display = 'none';
                    
                    input.onchange = function(e) {
                        const file = e.target.files[0];
                        if (file) {
                            log(`方法4: 文件已选择 - ${file.name}`);
                            updateResult('dynamic', `文件已选择: ${file.name}`, true);
                        }
                        document.body.removeChild(input);
                    };
                    
                    document.body.appendChild(input);
                    input.click();
                    log('方法4: 成功创建并触发动态文件输入');
                    updateResult('dynamic', '已触发文件对话框 (动态)', true);
                } catch (error) {
                    log(`方法4: 错误 - ${error.message}`);
                    updateResult('dynamic', `错误: ${error.message}`);
                }
            });
            
            // 监听所有文件输入的变化
            $('#file-hidden, #file-visible, #file-native').change(function(e) {
                const file = e.target.files[0];
                const inputId = e.target.id;
                const method = inputId.replace('file-', '');
                
                if (file) {
                    log(`${method}: 文件已选择 - ${file.name} (${file.size} bytes)`);
                    updateResult(method, `文件已选择: ${file.name}`, true);
                } else {
                    log(`${method}: 没有选择文件`);
                }
            });
            
            // 清除日志
            $('#clear-log').click(function() {
                $('#debug-log').empty();
                log('日志已清除');
            });
            
            log('所有事件绑定完成');
        });
    </script>
</body>
</html>
