#!/usr/bin/env python3
"""
完整的Excel模板下载和导入功能测试
验证所有三个向导页面的模板下载和Excel导入功能
"""

import requests
import os
import sys
import time

# 测试配置
BASE_URL = "http://localhost:5000"
LOGIN_URL = f"{BASE_URL}/login"

# 向导页面URL
WIZARD_URLS = {
    'subjects': f"{BASE_URL}/wizard/step1_subjects",
    'rooms': f"{BASE_URL}/wizard/step2_rooms", 
    'proctors': f"{BASE_URL}/wizard/step3_proctors"
}

# 模板下载URL
TEMPLATE_URLS = {
    'subjects': f"{BASE_URL}/wizard/download-template/subjects",
    'rooms': f"{BASE_URL}/wizard/download-template/rooms",
    'proctors': f"{BASE_URL}/wizard/download-template/proctors"
}

# Excel导入URL
IMPORT_URLS = {
    'subjects': f"{BASE_URL}/wizard/import-excel/subjects",
    'rooms': f"{BASE_URL}/wizard/import-excel/rooms",
    'proctors': f"{BASE_URL}/wizard/import-excel/proctors"
}

# 本地模板文件
TEMPLATE_FILES = {
    'subjects': 'template-guide/kemu.xlsx',
    'rooms': 'template-guide/kaochang.xlsx', 
    'proctors': 'template-guide/jiankaoyuan.xlsx'
}

def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"✅ 服务器连接成功 (状态码: {response.status_code})")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def test_template_files_exist():
    """测试本地模板文件是否存在"""
    print("\n📁 检查本地模板文件...")
    all_exist = True
    
    for step_type, file_path in TEMPLATE_FILES.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {step_type} 模板文件存在: {file_path} ({file_size} bytes)")
        else:
            print(f"❌ {step_type} 模板文件不存在: {file_path}")
            all_exist = False
    
    return all_exist

def test_template_downloads():
    """测试模板下载功能"""
    print("\n⬇️  测试模板下载功能...")
    
    success_count = 0
    total_count = len(TEMPLATE_URLS)
    
    for step_type, url in TEMPLATE_URLS.items():
        print(f"\n--- 测试{step_type}模板下载 ---")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                content_length = len(response.content)
                
                print(f"✅ 下载成功")
                print(f"   Content-Type: {content_type}")
                print(f"   文件大小: {content_length} bytes")
                
                # 检查是否是Excel文件
                if 'excel' in content_type or 'spreadsheet' in content_type:
                    print(f"   ✅ 正确的Excel文件类型")
                    success_count += 1
                else:
                    print(f"   ⚠️  可能不是Excel文件")
                    
            elif response.status_code == 302:
                print(f"⚠️  重定向到登录页面 (需要登录)")
            else:
                print(f"❌ 下载失败 (状态码: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
    
    print(f"\n模板下载测试结果: {success_count}/{total_count}")
    return success_count == total_count

def test_import_functions():
    """测试导入函数"""
    print("\n📊 测试导入函数...")
    
    try:
        sys.path.append('.')
        from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
        print("✅ 成功导入应用模块")
    except ImportError as e:
        print(f"❌ 无法导入应用模块: {e}")
        return False
    
    import_functions = {
        'subjects': (import_subjects_from_excel, '科目'),
        'rooms': (import_rooms_from_excel, '考场'),
        'proctors': (import_proctors_from_excel, '监考员')
    }
    
    success_count = 0
    total_count = len(import_functions)
    
    for step_type, (import_func, description) in import_functions.items():
        file_path = TEMPLATE_FILES[step_type]
        print(f"\n--- 测试{description}导入函数 ---")
        
        if not os.path.exists(file_path):
            print(f"❌ 模板文件不存在: {file_path}")
            continue
        
        try:
            data = import_func(file_path)
            print(f"✅ {description}导入成功")
            print(f"   导入数据数量: {len(data)}")
            
            if len(data) > 0:
                print(f"   示例数据: {data[0]}")
                success_count += 1
            else:
                print(f"   ⚠️  没有导入任何数据")
                
        except Exception as e:
            print(f"❌ {description}导入失败: {e}")
    
    print(f"\n导入函数测试结果: {success_count}/{total_count}")
    return success_count == total_count

def test_wizard_pages_access():
    """测试向导页面访问"""
    print("\n🌐 测试向导页面访问...")
    
    accessible_count = 0
    total_count = len(WIZARD_URLS)
    
    for step_type, url in WIZARD_URLS.items():
        print(f"\n--- 测试{step_type}页面访问 ---")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 页面访问成功")
                
                # 检查页面是否包含预期的元素
                content = response.text
                has_import_btn = '从Excel导入' in content
                has_download_btn = '下载导入模板' in content
                
                print(f"   包含导入按钮: {'✅' if has_import_btn else '❌'}")
                print(f"   包含下载按钮: {'✅' if has_download_btn else '❌'}")
                
                if has_import_btn and has_download_btn:
                    accessible_count += 1
                    
            elif response.status_code == 302:
                print(f"⚠️  重定向到登录页面 (需要登录)")
            else:
                print(f"❌ 页面访问失败 (状态码: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
    
    print(f"\n页面访问测试结果: {accessible_count}/{total_count}")
    return accessible_count == total_count

def main():
    """主测试函数"""
    print("🧪 Excel模板下载和导入功能完整测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试服务器连接
    test_results.append(("服务器连接", test_server_connection()))
    
    # 2. 测试本地模板文件
    test_results.append(("本地模板文件", test_template_files_exist()))
    
    # 3. 测试导入函数
    test_results.append(("导入函数", test_import_functions()))
    
    # 4. 测试模板下载
    test_results.append(("模板下载", test_template_downloads()))
    
    # 5. 测试页面访问
    test_results.append(("页面访问", test_wizard_pages_access()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("🎉 所有功能测试通过！Excel模板下载和导入功能正常工作。")
        return 0
    else:
        print("⚠️  部分功能测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
