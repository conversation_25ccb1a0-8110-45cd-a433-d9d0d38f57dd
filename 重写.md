监考安排系统是一个基于Python开发的自动化监考安排应用程序，旨在自动化监考安排流程，提高排班效率，优化人力资源分配。系统包含会员管理功能，支持管理员、VIP会员和普通注册会员三个角色，并对不同角色实施任务数量限制。用户可管理自建任务，系统支持从Excel文件导入数据、监考任务自动排程、数据验证以及结果导出等功能，能够处理复杂约束条件下的排班问题。

新添加一条验证规则：监考员设置中的场次限制总和要大于等于考场设置的所有科目监考员的总和。

理解这个程序，这个程序（exam_core - local.py）是本地运行的程序。这个程序读取本地excel文件，然后进行考试安排。
现在要进行web程序改造，
要求如下：
写一个数据转化的函数进行数据的转化文件的读取和转换。直接给源程序修改，不要在写单独的python文件。
将读取excel的所有sheet转换为名为sheet名称的json格式传递到下一个流程，
最后导出结果不用做修改。
数据处理的流程如下：
Excel输入 -> JSON（不必导出文件） -> 排班处理 --> Excel输出

Excel文件（输入）
├── 监考员设置     ──┐
├── 考试科目设置   ──┼─> JSON数据 ──┐
└── 考场设置       ──┘             │
                                  ↓
Excel文件（输出）                  排班处理
├── 考场安排      <──┐              │
├── 监考员安排    <──┼── excel数据 <─┘
└── 统计信息      <──┘
 
 "请以网络应用开发专家的身份，提供一个详细的方案，说明如何将一个本地运行的Python程序（用于处理Excel文件）转换为一个网页应用。要求如下：

不修改原有的Python程序源代码。
允许用户通过网页上传Excel文件。
在后台使用原有的Python程序处理上传的文件。
处理完成后，将结果Excel文件返回给用户下载。

数据流转过程
数据输入阶段：
程序从Excel文件（通常是"监考安排.xlsx"）读取数据
数据分为三个工作表：监考员设置、考试科目设置、考场设置
使用pandas库的pd.read_excel函数读取这些工作表
数据转换与处理：
Excel数据被转换为程序内部的数据结构（对象）：
 Teacher类：存储教师信息和监考限制
 Subject类：存储科目信息和考试时间
 Room类：存储考场信息和各科目所需监考人数
这些类都有 from_excel_row方法，用于将Excel行数据转换为对象
数据预处理：
 preprocess_teachers方法：计算教师权重，设置可监考科目
 preprocess_subjects方法：计算科目优先级，分析科目间的时间重叠
 preprocess_rooms方法：确保考场的科目需求字典完整有效
求解过程中的数据转换：
将预处理后的数据传递给 ORToolsScheduler类
创建决策变量：将教师、科目、考场的组合转换为布尔变量
添加约束条件：基于数据关系创建数学约束
求解模型：使用OR-Tools求解器计算最优解
结果处理与输出：
将求解结果转换为 Schedule对象，存储监考安排
使用 export_results方法将结果导出为Excel文件
生成多个工作表：考场安排、监考员安排、统计信息等
缓存与检查点机制：
使用 SolverCache类缓存中间结果
将问题数据序列化为JSON字符串，计算哈希值作为缓存键
使用pickle序列化解决方案，存储到临时文件
通过JSON文件维护缓存索引
数据分析与统计：
收集解决方案的统计数据（如监考时长分布）
将统计数据转换为DataFrame格式
导出到Excel文件的不同工作表
整个过程中，数据从Excel表格格式开始，转换为Python对象，然后转换为OR-Tools的数学模型，求解后再转换回Python对象，最终输出为Excel格式，形成了一个完整的数据流转循环。


exam_core_local.py 的效率优化措施（用中文回复）
exam_core_local.py文件中包含了多种提高程序运行效率的优化措施，主要包括以下几个方面：
处理过程如果需要保存临时文件，临时文件的目录设置在当前任务执行目录，用完清理。尽量在内存中处理，不保存或者减少处理过程中的临时文件生产）
1. 缓存机制
求解器缓存（SolverCache类）：存储和管理求解过程中的中间结果
模型缓存：将已构建的模型序列化保存，避免重复构建
解决方案缓存：缓存已找到的解决方案，相同问题可直接返回结果
内存与磁盘双层缓存：先检查内存缓存，再检查文件缓存，提高访问速度
2. 预计算与数据结构优化
预计算助手（PrecomputationHelper类）：预先计算并存储频繁使用的数据
矩阵存储：使用NumPy数组存储兼容性关系，实现O(1)时间复杂度的查询
教师-科目兼容性矩阵
教师-考场兼容性矩阵
科目时间重叠矩阵
快速查找容器（FastLookupContainer类）：使用哈希表和集合实现O(1)查询
批量处理：将大量数据分批处理，控制内存使用
3. 并行计算
并行求解器（ParallelSolver类）：同时运行多个求解配置
多进程执行：使用ProcessPoolExecutor并行执行求解任务
进程间通信：通过队列收集并行求解结果
心跳监控：使用线程定期执行检查点回调，保证长时间运行的稳定性
4. 增量求解与约束放松
增量求解：按约束级别逐步求解，保留中间结果
约束放松策略：当严格约束无法找到解时，逐步放松约束重新求解
启发式初始值：使用上一次的解作为启发式初始值，加速收敛
5. 内存管理
内存管理器（MemoryManager类）：负责管理和清理内存资源
垃圾回收：主动调用gc.collect()释放不再使用的内存
LRU缓存策略：限制内存中保留的最大缓存条目数，自动清理最旧的条目
临时文件管理：定期清理过期的临时文件
6. 问题规模自适应
大规模问题检测：自动检测问题规模，选择合适的求解策略
变量创建优化：根据问题规模选择不同的变量创建方法
流式处理：对于大规模问题，使用流式处理减少内存占用
7. 中断处理与恢复机制
检查点系统：定期保存求解状态，支持从中断处恢复
信号处理：捕获中断信号，确保在程序终止前保存状态
恢复机制：启动时尝试从最新检查点恢复，继续之前的求解过程
这些优化措施共同作用，显著提高了程序处理大规模监考安排问题的效率，减少了求解时间，降低了内存占用，并增强了程序的稳定性和可靠性。