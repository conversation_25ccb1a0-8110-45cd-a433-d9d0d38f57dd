#!/usr/bin/env python3
"""
测试按钮修复是否成功
"""

import os

def check_button_implementation():
    """检查按钮实现是否正确"""
    print("🔍 检查按钮实现...")
    
    template_file = 'templates/wizard/step1_subjects.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键实现
        checks = {
            '按钮HTML定义': 'id="import-excel-btn-subjects"' in content,
            'initExcelImportComponent函数': 'function initExcelImportComponent()' in content,
            '按钮元素获取': 'getElementById(`import-excel-btn-${componentId}`)' in content,
            'addEventListener绑定': 'addEventListener(\'click\'' in content,
            '函数调用': 'initExcelImportComponent();' in content,
            '文件处理函数': 'handleSubjectFileUpload' in content,
            '动态文件输入': 'document.createElement(\'input\')' in content
        }
        
        all_passed = True
        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def compare_with_working_page():
    """与工作正常的step2_rooms页面对比"""
    print("\n🔍 与step2_rooms页面对比...")
    
    try:
        # 读取两个页面
        with open('templates/wizard/step1_subjects.html', 'r', encoding='utf-8') as f:
            subjects_content = f.read()
        
        with open('templates/wizard/step2_rooms.html', 'r', encoding='utf-8') as f:
            rooms_content = f.read()
        
        # 检查关键模式
        patterns = [
            ('initExcelImportComponent函数定义', 'function initExcelImportComponent()'),
            ('getElementById获取按钮', 'getElementById(`import-excel-btn-'),
            ('addEventListener绑定', 'addEventListener(\'click\''),
            ('动态文件输入创建', 'document.createElement(\'input\')'),
            ('函数初始化调用', 'initExcelImportComponent();')
        ]
        
        all_consistent = True
        
        for description, pattern in patterns:
            subjects_has = pattern in subjects_content
            rooms_has = pattern in rooms_content
            
            if subjects_has and rooms_has:
                print(f"   ✅ {description}: 两页面都有")
            elif not subjects_has and not rooms_has:
                print(f"   ⚠️  {description}: 两页面都没有")
            else:
                print(f"   ❌ {description}: 不一致 (科目:{subjects_has}, 考场:{rooms_has})")
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 按钮修复验证")
    print("=" * 40)
    
    test_results = []
    
    # 1. 检查按钮实现
    test_results.append(("按钮实现检查", check_button_implementation()))
    
    # 2. 与工作页面对比
    test_results.append(("与工作页面对比", compare_with_working_page()))
    
    # 总结
    print("\n" + "=" * 40)
    print("📋 验证结果")
    print("=" * 40)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项验证通过")
    
    if passed_count == len(test_results):
        print("\n🎉 按钮修复验证通过！")
        print("现在使用与step2_rooms相同的工作实现。")
        print("\n📋 修复内容:")
        print("✅ 恢复了initExcelImportComponent函数")
        print("✅ 使用addEventListener而不是jQuery click")
        print("✅ 添加了函数初始化调用")
        print("✅ 与step2_rooms保持完全一致的实现")
        return 0
    else:
        print("\n❌ 按钮修复验证失败。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
