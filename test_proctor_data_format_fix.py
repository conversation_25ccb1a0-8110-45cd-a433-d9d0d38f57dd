#!/usr/bin/env python3
"""
测试监考员数据格式修复
验证required_rooms和unavailable_rooms字段的数据类型处理
"""

import json

def test_ensure_list_format():
    """测试ensure_list_format函数"""
    print("🔍 测试ensure_list_format函数...")
    
    # 模拟ensure_list_format函数
    def ensure_list_format(field_value):
        if isinstance(field_value, list):
            return field_value
        elif isinstance(field_value, str):
            return [item.strip() for item in field_value.split(',') if item.strip()] if field_value.strip() else []
        else:
            return []
    
    # 测试用例
    test_cases = [
        {
            'name': '列表输入',
            'input': ['1考场', '2考场', '3考场'],
            'expected': ['1考场', '2考场', '3考场']
        },
        {
            'name': '字符串输入（逗号分隔）',
            'input': '1考场,2考场,3考场',
            'expected': ['1考场', '2考场', '3考场']
        },
        {
            'name': '字符串输入（带空格）',
            'input': '1考场, 2考场 , 3考场',
            'expected': ['1考场', '2考场', '3考场']
        },
        {
            'name': '空字符串',
            'input': '',
            'expected': []
        },
        {
            'name': '只有空格的字符串',
            'input': '   ',
            'expected': []
        },
        {
            'name': '只有逗号的字符串',
            'input': ',,,',
            'expected': []
        },
        {
            'name': '单个项目字符串',
            'input': '1考场',
            'expected': ['1考场']
        },
        {
            'name': 'None值',
            'input': None,
            'expected': []
        },
        {
            'name': '数字输入',
            'input': 123,
            'expected': []
        },
        {
            'name': '空列表',
            'input': [],
            'expected': []
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        result = ensure_list_format(test_case['input'])
        expected = test_case['expected']
        
        if result == expected:
            print(f"   ✅ {test_case['name']}: {test_case['input']} → {result}")
        else:
            print(f"   ❌ {test_case['name']}: {test_case['input']}")
            print(f"      期望: {expected}")
            print(f"      实际: {result}")
            all_passed = False
    
    return all_passed

def test_proctor_data_processing():
    """测试监考员数据处理"""
    print("\n🔍 测试监考员数据处理...")
    
    # 模拟不同格式的监考员数据
    test_proctors = [
        {
            'name': '张三',
            'teaching_subject': '语文',
            'required_subjects': ['语文', '数学'],
            'unavailable_subjects': ['英语', '物理'],
            'required_rooms': ['1考场', '2考场'],  # 列表格式
            'unavailable_rooms': ['10考场', '11考场'],  # 列表格式
            'session_limit': 5
        },
        {
            'name': '李四',
            'teaching_subject': '数学',
            'required_subjects': ['数学', '物理'],
            'unavailable_subjects': ['语文', '化学'],
            'required_rooms': '3考场,4考场,5考场',  # 字符串格式
            'unavailable_rooms': '12考场,13考场',  # 字符串格式
            'session_limit': 3
        },
        {
            'name': '王五',
            'teaching_subject': '英语',
            'required_subjects': ['英语'],
            'unavailable_subjects': ['物理', '化学'],
            'required_rooms': '',  # 空字符串
            'unavailable_rooms': [],  # 空列表
            'session_limit': 4
        }
    ]
    
    # 模拟处理逻辑
    def ensure_list_format(field_value):
        if isinstance(field_value, list):
            return field_value
        elif isinstance(field_value, str):
            return [item.strip() for item in field_value.split(',') if item.strip()] if field_value.strip() else []
        else:
            return []
    
    validated_proctors = []
    
    for p in test_proctors:
        try:
            session_limit = int(p.get('session_limit', 0)) if p.get('session_limit') else 0
        except (ValueError, TypeError):
            session_limit = 0
        
        validated_proctor = {
            'name': p.get('name', '').strip(),
            'teaching_subject': p.get('teaching_subject', '').strip(),
            'required_subjects': p.get('required_subjects', []),
            'unavailable_subjects': p.get('unavailable_subjects', []),
            'required_rooms': ensure_list_format(p.get('required_rooms', [])),
            'unavailable_rooms': ensure_list_format(p.get('unavailable_rooms', [])),
            'session_limit': session_limit
        }
        validated_proctors.append(validated_proctor)
    
    # 验证处理结果
    expected_results = [
        {
            'name': '张三',
            'required_rooms': ['1考场', '2考场'],
            'unavailable_rooms': ['10考场', '11考场']
        },
        {
            'name': '李四',
            'required_rooms': ['3考场', '4考场', '5考场'],
            'unavailable_rooms': ['12考场', '13考场']
        },
        {
            'name': '王五',
            'required_rooms': [],
            'unavailable_rooms': []
        }
    ]
    
    all_passed = True
    
    for i, (actual, expected) in enumerate(zip(validated_proctors, expected_results)):
        print(f"\n   监考员 {i+1}: {actual['name']}")
        
        for field in ['required_rooms', 'unavailable_rooms']:
            actual_value = actual[field]
            expected_value = expected[field]
            
            if actual_value == expected_value:
                print(f"      ✅ {field}: {actual_value}")
            else:
                print(f"      ❌ {field}:")
                print(f"         期望: {expected_value}")
                print(f"         实际: {actual_value}")
                all_passed = False
    
    return all_passed

def test_error_scenario_simulation():
    """模拟错误场景"""
    print("\n🔍 模拟错误场景...")
    
    # 模拟导致原始错误的数据
    problematic_data = {
        'name': '测试监考员',
        'teaching_subject': '语文',
        'required_subjects': ['语文', '数学'],
        'unavailable_subjects': ['英语'],
        'required_rooms': ['1考场', '2考场'],  # 这是列表，原代码会调用.strip()导致错误
        'unavailable_rooms': ['10考场'],  # 这是列表，原代码会调用.strip()导致错误
        'session_limit': 5
    }
    
    print("   📝 原始错误场景:")
    print(f"      required_rooms类型: {type(problematic_data['required_rooms'])}")
    print(f"      required_rooms值: {problematic_data['required_rooms']}")
    print(f"      unavailable_rooms类型: {type(problematic_data['unavailable_rooms'])}")
    print(f"      unavailable_rooms值: {problematic_data['unavailable_rooms']}")
    
    # 模拟原始错误代码
    try:
        # 这会导致AttributeError: 'list' object has no attribute 'strip'
        old_required_rooms = problematic_data.get('required_rooms', '').strip()
        print(f"   ❌ 原始代码会在这里失败")
    except AttributeError as e:
        print(f"   ✅ 确认原始错误: {e}")
    
    # 模拟修复后的代码
    def ensure_list_format(field_value):
        if isinstance(field_value, list):
            return field_value
        elif isinstance(field_value, str):
            return [item.strip() for item in field_value.split(',') if item.strip()] if field_value.strip() else []
        else:
            return []
    
    try:
        fixed_required_rooms = ensure_list_format(problematic_data.get('required_rooms', []))
        fixed_unavailable_rooms = ensure_list_format(problematic_data.get('unavailable_rooms', []))
        
        print(f"   ✅ 修复后处理成功:")
        print(f"      required_rooms: {fixed_required_rooms}")
        print(f"      unavailable_rooms: {fixed_unavailable_rooms}")
        
        return True
    except Exception as e:
        print(f"   ❌ 修复后仍有错误: {e}")
        return False

def test_json_serialization():
    """测试JSON序列化"""
    print("\n🔍 测试JSON序列化...")
    
    # 模拟处理后的数据
    def ensure_list_format(field_value):
        if isinstance(field_value, list):
            return field_value
        elif isinstance(field_value, str):
            return [item.strip() for item in field_value.split(',') if item.strip()] if field_value.strip() else []
        else:
            return []
    
    test_data = {
        'name': '张三',
        'teaching_subject': '语文',
        'required_subjects': ['语文', '数学'],
        'unavailable_subjects': ['英语', '物理'],
        'required_rooms': ensure_list_format(['1考场', '2考场']),
        'unavailable_rooms': ensure_list_format('10考场,11考场'),
        'session_limit': 5
    }
    
    try:
        # 测试JSON序列化
        json_str = json.dumps(test_data, ensure_ascii=False)
        
        # 测试JSON反序列化
        parsed_data = json.loads(json_str)
        
        print(f"   ✅ JSON序列化成功")
        print(f"   ✅ JSON反序列化成功")
        print(f"   📝 序列化后的数据: {json_str}")
        
        # 验证数据完整性
        if parsed_data == test_data:
            print(f"   ✅ 数据完整性验证通过")
            return True
        else:
            print(f"   ❌ 数据完整性验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ JSON处理失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 监考员数据格式修复测试")
    print("=" * 60)
    print("测试required_rooms和unavailable_rooms字段的数据类型处理")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试ensure_list_format函数
    test_results.append(("ensure_list_format函数", test_ensure_list_format()))
    
    # 2. 测试监考员数据处理
    test_results.append(("监考员数据处理", test_proctor_data_processing()))
    
    # 3. 模拟错误场景
    test_results.append(("错误场景模拟", test_error_scenario_simulation()))
    
    # 4. 测试JSON序列化
    test_results.append(("JSON序列化", test_json_serialization()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 数据格式修复完成:")
        print("✅ 类型检查 - 智能识别列表和字符串格式")
        print("✅ 格式转换 - 统一转换为列表格式")
        print("✅ 错误修复 - 解决AttributeError: 'list' object has no attribute 'strip'")
        print("✅ 兼容性 - 支持导入数据和手动输入数据")
        print("✅ 数据完整性 - 确保数据在处理过程中不丢失")
        
        print("\n🚀 修复效果:")
        print("• 列表格式数据直接使用")
        print("• 字符串格式数据自动分割为列表")
        print("• 空值和异常值安全处理")
        print("• JSON序列化和反序列化正常")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
