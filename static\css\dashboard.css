/* Dashboard页面专用样式 */

/* 现代化设计变量 */
:root {
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --card-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    --card-hover-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    --border-radius-lg: 10px;
    --animation-duration: 0.25s;
}

/* 防止水平滚动 */
html, body {
    overflow-x: hidden;
    max-width: 100vw;
}

* {
    box-sizing: border-box;
    word-wrap: break-word;
}

/* 主体布局 - 减少整体间距 */
.dashboard-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 70px);
    padding: 0;
    width: 100%;
    margin-top: -70px;
    padding-top: 70px;
}

.dashboard-container .container {
    max-width: 100%;
    padding: 0 12px;
    margin-left: auto;
    margin-right: auto;
}

/* 覆盖base.html中的.container.mt-4样式 */
.dashboard-container + .container.mt-4,
.dashboard-container .container.mt-4 {
    margin-top: 0 !important;
}

/* 确保dashboard页面的主容器紧贴导航栏 */
body:has(.dashboard-container) .container.mt-4 {
    margin-top: 0 !important;
}

/* 为不支持:has()的浏览器提供备用方案 */
.dashboard-page .container.mt-4 {
    margin-top: 0 !important;
}

/* 监考安排创建方式 - 紧凑设计 */
.creation-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.method-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all var(--animation-duration) ease;
    overflow: hidden;
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.method-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.method-primary:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
}

.method-primary:hover .method-badge.recommended {
    animation: pulse-glow 1s infinite;
    transform: scale(1.1);
}

.method-secondary:hover {
    border-color: #28a745;
}

.method-header {
    padding: 0.8rem 0.8rem 0.6rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.method-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.method-primary .method-icon {
    background: var(--primary-color);
}

.method-secondary .method-icon {
    background: #28a745;
}

.method-info {
    flex: 1;
    min-width: 0;
}

.method-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #2c3e50;
    line-height: 1.2;
}

.method-subtitle {
    font-size: 0.72rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.3;
}

.method-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.65rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
    animation: pulse-glow 2s infinite;
}

.method-badge.recommended {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    position: relative;
}

.method-badge.recommended::before {
    content: "⭐";
    margin-right: 0.2rem;
    font-size: 0.7rem;
}

.method-badge.recommended::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #ffd700, #ffed4e, #ffd700);
    border-radius: 14px;
    z-index: -1;
    animation: rainbow-border 3s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 16px rgba(40, 167, 69, 0.4);
    }
}

@keyframes rainbow-border {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* 首次访问时的吸引注意力效果 */
.method-badge.recommended.first-visit {
    animation: attention-grabber 4s ease-in-out;
}

@keyframes attention-grabber {
    0%, 90%, 100% {
        transform: scale(1);
    }
    5%, 15% {
        transform: scale(1.2) rotate(-5deg);
    }
    10%, 20% {
        transform: scale(1.2) rotate(5deg);
    }
    25% {
        transform: scale(1.1);
    }
}

.method-badge.advanced {
    background: #f3e5f5;
    color: #7b1fa2;
}

.method-body {
    padding: 0.6rem 0.8rem 0.8rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 方法描述 - 紧凑布局 */
.method-description {
    padding: 0.4rem;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 2px solid var(--primary-color);
    margin-bottom: 0.6rem;
}

.method-description p {
    font-size: 0.7rem;
    line-height: 1.3;
    margin: 0;
    color: #495057;
}

/* 步骤显示 - 紧凑网格布局 */
.method-steps {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.3rem;
    margin-bottom: 0.75rem;
}

.step-item {
    display: flex;
    align-items: center;
    padding: 0.3rem 0.4rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.7rem;
    border-left: 2px solid var(--primary-color);
}

.step-number {
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.6rem;
    font-weight: 600;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.step-text {
    color: #555;
    line-height: 1.2;
    font-size: 0.7rem;
}



/* 功能特性显示 - 2列网格布局 */
.method-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.3rem;
    margin-bottom: 0.75rem;
}

.method-advantages {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
    min-height: 40px;
}

.feature-item,
.advantage-item {
    display: flex;
    align-items: center;
    font-size: 0.7rem;
    color: #555;
    padding: 0.1rem 0;
}

.feature-item i,
.advantage-item i {
    font-size: 0.65rem;
    margin-right: 0.4rem;
    flex-shrink: 0;
}

/* 操作按钮 - 紧凑布局 */
.method-action {
    text-align: center;
    margin-top: auto;
}

.method-action .btn {
    padding: 0.4rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    width: 100%;
}

.method-actions {
    display: flex;
    gap: 0.3rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-top: auto;
}

.method-actions .btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
    font-weight: 600;
    flex: 1;
    min-width: 70px;
}

/* 任务统计数据模块 - 紧凑设计 */
.data-module {
    background: white;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    margin: 0 0 0.8rem 0;
    overflow: hidden;
    border: 1px solid #e9ecef;
    border-top: none;
}

.data-header {
    background: #f8f9fa;
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.data-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
}

.data-total {
    font-size: 0.8rem;
    color: #6c757d;
    background: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.data-stats {
    padding: 0.8rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    flex: 1;
    min-width: 0;
}

.stat-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-pending .stat-indicator { background: #6c757d; }
.stat-processing .stat-indicator { background: #0d6efd; }
.stat-completed .stat-indicator { background: #198754; }
.stat-failed .stat-indicator { background: #dc3545; }

.stat-count {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    min-width: 20px;
    text-align: center;
}

.stat-name {
    font-size: 0.8rem;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 任务列表区域 - 减少内边距 */
.tasks-section {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    padding: 1.2rem;
    margin-bottom: 1rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
    padding-bottom: 0.6rem;
    border-bottom: 2px solid #f8f9fa;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

/* 任务筛选 */
.task-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
    margin-bottom: 0.8rem;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 18px;
    padding: 0.3rem 0.7rem;
    font-size: 0.75rem;
    color: #6c757d;
    transition: all var(--animation-duration) ease;
    cursor: pointer;
    white-space: nowrap;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
    transform: translateY(-1px);
}

/* 任务网格布局 */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.8rem;
}

/* 在大屏幕上最多显示6列 */
@media (min-width: 1200px) {
    .tasks-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* 中等屏幕显示4列 */
@media (min-width: 768px) and (max-width: 1199px) {
    .tasks-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 小屏幕显示2列 */
@media (min-width: 576px) and (max-width: 767px) {
    .tasks-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 最小屏幕显示1列 */
@media (max-width: 575px) {
    .tasks-grid {
        grid-template-columns: 1fr;
    }
}

/* 任务卡片 - 更紧凑 */
.task-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all var(--animation-duration) ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 180px;
}

/* 紧凑卡片样式 */
.compact-card {
    min-height: 120px;
}

.compact-card .task-header {
    padding: 0.6rem 0.8rem 0.3rem;
}

.compact-card .task-title {
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.compact-card .task-meta {
    font-size: 0.7rem;
    gap: 0.4rem;
}

.compact-card .task-body {
    padding: 0.4rem 0.8rem;
}

.compact-card .task-actions {
    padding: 0.4rem 0.8rem;
}

.compact-card .task-actions .action-group {
    gap: 0.2rem;
}

/* 任务创建引导样式 */
.task-creation-guide {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

.guide-header .section-title {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.creation-tip-box {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #ffc107;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tip-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.tip-icon {
    font-size: 1.2rem;
    margin-top: 0.1rem;
}

.tip-text h6 {
    color: #495057;
    font-weight: 600;
}

.tip-text p {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #6c757d;
}

/* 方法选择卡片增强 */
.method-description {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.method-advantages,
.method-process {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.advantage-item,
.process-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #495057;
}



/* 方法卡片的增强样式 */
.method-card .method-title {
    font-size: 1rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    word-break: break-word;
}

.method-card .method-subtitle {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.method-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.task-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #dee2e6;
}

.task-header {
    padding: 0.8rem 1rem 0.4rem;
    border-bottom: 1px solid #f8f9fa;
}

.task-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 0.3rem 0;
    word-break: break-word;
}

.task-meta {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    color: #6c757d;
    font-size: 0.75rem;
    flex-wrap: wrap;
}

.task-body {
    padding: 0.6rem 1rem;
}

.task-progress {
    height: 4px;
    background: #f8f9fa;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.6rem;
}

.task-progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.task-progress-bar.completed {
    background: var(--gradient-success);
}

.task-progress-bar.failed {
    background: var(--gradient-secondary);
}

.task-actions {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    margin-top: auto;
}

.task-actions .action-group {
    display: flex;
    justify-content: center;
    gap: 0.3rem;
    flex-wrap: wrap;
}

/* 空状态 - 更紧凑 */
.empty-state {
    text-align: center;
    padding: 2.5rem 1.2rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
}

.empty-icon {
    width: 56px;
    height: 56px;
    margin: 0 auto 1rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: white;
}

/* 确保按钮不会太小 */
.btn {
    font-size: 0.8rem;
    padding: 0.35rem 0.7rem;
}

.btn-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 0;
        margin-top: -60px;
        padding-top: 60px;
        min-height: calc(100vh - 60px);
    }

    .dashboard-container .container {
        padding: 0 8px;
    }

    .welcome-section {
        padding: 1.2rem 1rem;
        margin-bottom: 0.8rem;
    }

    .welcome-title {
        font-size: 1.4rem;
    }

    /* 在中小屏幕上垂直排列创建方式 */
    .creation-methods {
        grid-template-columns: 1fr;
        gap: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .method-card {
        min-height: auto;
    }

    .method-header {
        padding: 0.7rem 0.8rem 0.5rem;
        gap: 0.6rem;
    }
    
    .method-badge {
        top: 0.4rem;
        right: 0.4rem;
        padding: 0.25rem 0.5rem;
        font-size: 0.6rem;
    }
    
    .method-badge.recommended::before {
        font-size: 0.65rem;
        margin-right: 0.15rem;
    }

    .method-body {
        padding: 0.5rem 0.8rem 0.7rem;
    }

    .method-steps {
        grid-template-columns: 1fr;
        gap: 0.25rem;
        margin-bottom: 0.6rem;
    }

    .step-item {
        padding: 0.25rem 0.3rem;
        font-size: 0.65rem;
    }

    .step-number {
        width: 14px;
        height: 14px;
        font-size: 0.55rem;
        margin-right: 0.4rem;
    }

    .method-title {
        font-size: 0.85rem;
    }

    .method-subtitle {
        font-size: 0.68rem;
    }

    .method-features {
        grid-template-columns: 1fr;
        gap: 0.25rem;
        margin-bottom: 0.6rem;
    }

    .method-advantages {
        margin-bottom: 0.6rem;
    }

    .feature-item,
    .advantage-item {
        font-size: 0.65rem;
    }

    /* 数据模块在小屏幕上的优化 */
    .data-stats {
        padding: 0.6rem 0.8rem;
        gap: 0.5rem;
    }

    .stat-item {
        gap: 0.3rem;
    }

    .stat-name {
        font-size: 0.75rem;
    }

    .tasks-section {
        padding: 1rem;
        margin-bottom: 0.8rem;
    }

    .task-filters {
        justify-content: flex-start;
        margin-bottom: 0.6rem;
    }

    .task-header {
        padding: 0.6rem 0.8rem 0.3rem;
    }

    .task-body {
        padding: 0.5rem 0.8rem;
    }

    .task-actions {
        padding: 0.4rem 0.8rem;
    }

    .task-actions .action-group {
        justify-content: center;
        gap: 0.3rem;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0;
        margin-top: -60px;
        padding-top: 60px;
        min-height: calc(100vh - 60px);
    }

    .dashboard-container .container {
        padding: 0 6px;
    }

    .method-header {
        padding: 0.6rem 0.6rem 0.4rem;
        flex-direction: row;
        text-align: left;
        gap: 0.5rem;
    }
    
    .method-badge {
        top: 0.3rem;
        right: 0.3rem;
        padding: 0.2rem 0.4rem;
        font-size: 0.55rem;
        border-radius: 10px;
    }
    
    .method-badge.recommended::before {
        font-size: 0.6rem;
        margin-right: 0.1rem;
    }

    .method-icon {
        width: 28px;
        height: 28px;
        font-size: 1rem;
    }

    .method-title {
        font-size: 0.8rem;
    }

    .method-subtitle {
        font-size: 0.65rem;
    }

    .method-body {
        padding: 0.4rem 0.6rem 0.6rem;
    }

    .method-description p {
        font-size: 0.65rem;
    }

    .step-item {
        padding: 0.2rem 0.25rem;
        font-size: 0.6rem;
    }

    .step-number {
        width: 12px;
        height: 12px;
        font-size: 0.5rem;
        margin-right: 0.3rem;
    }

    .feature-item,
    .advantage-item {
        font-size: 0.6rem;
    }

    .method-actions {
        flex-direction: row;
        gap: 0.3rem;
    }

    .method-actions .btn {
        font-size: 0.65rem;
        padding: 0.25rem 0.4rem;
    }

    .method-action .btn {
        font-size: 0.7rem;
        padding: 0.3rem 0.8rem;
    }

    /* 最小屏幕数据模块优化 */
    .data-header {
        padding: 0.6rem 0.8rem;
        flex-direction: column;
        gap: 0.3rem;
        text-align: center;
    }

    .data-stats {
        padding: 0.5rem 0.8rem;
        flex-direction: column;
        gap: 0.4rem;
    }

    .stat-item {
        justify-content: space-between;
        padding: 0.3rem 0;
        border-bottom: 1px solid #f5f5f5;
    }

    .stat-item:last-child {
        border-bottom: none;
    }

    .task-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.2rem;
    }
}

/* 防止图片或其他元素超出容器 */
img, video, iframe {
    max-width: 100%;
    height: auto;
}

/* 确保模态框不会超出视口 */
.modal-dialog {
    max-width: calc(100vw - 20px);
    margin: 10px auto;
}

/* 针对任务描述文本的优化 */
.task-description {
    font-size: 0.8rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

/* 进一步减少垂直空间的微调 */
.row {
    margin-bottom: 0;
}

.mb-3 {
    margin-bottom: 0.8rem !important;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

/* 优化badge和按钮的间距 */
.badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

/* 优化卡片间的垂直间距 */
.action-card + .action-card,
.stat-card + .stat-card {
    margin-top: 0;
} 