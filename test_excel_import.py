#!/usr/bin/env python3
"""
测试Excel导入功能的脚本
验证所有三个向导页面的Excel导入功能是否正常工作
"""

import requests
import os
import sys
from pathlib import Path

# 测试配置
BASE_URL = "http://localhost:5000"
LOGIN_URL = f"{BASE_URL}/login"
WIZARD_URLS = {
    'subjects': f"{BASE_URL}/wizard/step1_subjects",
    'rooms': f"{BASE_URL}/wizard/step2_rooms", 
    'proctors': f"{BASE_URL}/wizard/step3_proctors"
}
IMPORT_URLS = {
    'subjects': f"{BASE_URL}/wizard/import-excel/subjects",
    'rooms': f"{BASE_URL}/wizard/import-excel/rooms",
    'proctors': f"{BASE_URL}/wizard/import-excel/proctors"
}
TEMPLATE_FILES = {
    'subjects': 'template-guide/kemu.xlsx',
    'rooms': 'template-guide/kaochang.xlsx', 
    'proctors': 'template-guide/jiankaoyuan.xlsx'
}

def test_server_connection():
    """测试服务器连接"""
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"✅ 服务器连接成功 (状态码: {response.status_code})")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()

    # 获取登录页面和CSRF令牌
    try:
        login_page = session.get(LOGIN_URL)
        if login_page.status_code != 200:
            print(f"❌ 无法访问登录页面 (状态码: {login_page.status_code})")
            return None

        # 从页面中提取CSRF令牌
        import re
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None

        # 尝试登录 (使用默认管理员账号)
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }

        if csrf_token:
            login_data['csrf_token'] = csrf_token

        login_response = session.post(LOGIN_URL, data=login_data)

        # 检查是否重定向到仪表板或其他页面（登录成功的标志）
        if login_response.status_code in [200, 302] and 'login' not in login_response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败 (状态码: {login_response.status_code}, URL: {login_response.url})")
            print(f"响应内容: {login_response.text[:200]}...")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 登录过程出错: {e}")
        return None

def test_template_files():
    """测试模板文件是否存在"""
    print("\n=== 测试模板文件 ===")
    all_exist = True
    
    for step_type, file_path in TEMPLATE_FILES.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {step_type} 模板文件存在: {file_path} ({file_size} bytes)")
        else:
            print(f"❌ {step_type} 模板文件不存在: {file_path}")
            all_exist = False
    
    return all_exist

def test_wizard_pages_access(session):
    """测试向导页面访问"""
    print("\n=== 测试向导页面访问 ===")
    all_accessible = True
    
    for step_type, url in WIZARD_URLS.items():
        try:
            response = session.get(url)
            if response.status_code == 200:
                print(f"✅ {step_type} 页面访问成功")
            else:
                print(f"❌ {step_type} 页面访问失败 (状态码: {response.status_code})")
                all_accessible = False
        except requests.exceptions.RequestException as e:
            print(f"❌ {step_type} 页面访问出错: {e}")
            all_accessible = False
    
    return all_accessible

def test_excel_import(session, step_type):
    """测试特定步骤的Excel导入功能"""
    template_file = TEMPLATE_FILES[step_type]
    import_url = IMPORT_URLS[step_type]
    
    if not os.path.exists(template_file):
        print(f"❌ {step_type}: 模板文件不存在")
        return False
    
    try:
        # 获取CSRF令牌
        wizard_page = session.get(WIZARD_URLS[step_type])
        if wizard_page.status_code != 200:
            print(f"❌ {step_type}: 无法访问向导页面")
            return False
        
        # 上传文件
        with open(template_file, 'rb') as f:
            files = {'file': (os.path.basename(template_file), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = session.post(import_url, files=files)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print(f"✅ {step_type}: Excel导入成功 - {result.get('message', '')}")
                    return True
                else:
                    print(f"❌ {step_type}: Excel导入失败 - {result.get('message', '未知错误')}")
                    return False
            except ValueError:
                print(f"❌ {step_type}: 响应不是有效的JSON")
                return False
        else:
            print(f"❌ {step_type}: HTTP请求失败 (状态码: {response.status_code})")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ {step_type}: 请求出错 - {e}")
        return False
    except Exception as e:
        print(f"❌ {step_type}: 未知错误 - {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试Excel导入功能...")
    
    # 1. 测试服务器连接
    if not test_server_connection():
        sys.exit(1)
    
    # 2. 测试模板文件
    if not test_template_files():
        print("⚠️  部分模板文件缺失，但继续测试...")
    
    # 3. 登录
    session = login_and_get_session()
    if not session:
        sys.exit(1)
    
    # 4. 测试向导页面访问
    if not test_wizard_pages_access(session):
        print("⚠️  部分页面无法访问，但继续测试...")
    
    # 5. 测试Excel导入功能
    print("\n=== 测试Excel导入功能 ===")
    success_count = 0
    total_count = len(TEMPLATE_FILES)
    
    for step_type in TEMPLATE_FILES.keys():
        if test_excel_import(session, step_type):
            success_count += 1
    
    # 6. 总结
    print(f"\n=== 测试总结 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有Excel导入功能测试通过！")
        return 0
    else:
        print("❌ 部分Excel导入功能测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
