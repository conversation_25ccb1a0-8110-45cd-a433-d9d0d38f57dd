# 监考安排系统复现指南

## 项目概述

这是一个基于Flask的智能监考安排系统，用于自动化分配监考员到各个考场。系统采用Google OR-Tools优化引擎进行智能调度，支持复杂的约束条件处理。

## 核心功能

1. **用户管理系统**
   - 多角色支持：管理员、VIP用户、普通用户
   - 用户认证：登录、注册、密码修改
   - 权限控制：基于角色的访问控制

2. **监考任务管理**
   - Excel文件上传和验证
   - 智能监考安排生成
   - 任务状态追踪和进度显示
   - 结果文件下载

3. **后台管理功能**
   - 用户管理：添加、编辑、删除用户
   - 任务管理：查看、重置、删除任务
   - 系统监控：资源使用、任务队列状态

## 技术栈要求

### 后端框架
- Python 3.8+
- Flask 3.1.0
- SQLAlchemy 2.0.40
- Flask-SQLAlchemy 3.1.1
- Flask-Login 0.6.3
- Flask-WTF 1.2.2
- Google OR-Tools 9.8.3296

### 数据库和缓存
- SQLite（开发环境）
- Redis 5.0.1（生产环境）
- fakeredis 2.20.0（Windows环境）

### Excel处理
- openpyxl 3.1.2
- xlrd 2.0.1
- XlsxWriter 3.1.9

### 前端框架
- Bootstrap
- jQuery
- Font Awesome

## 系统架构

1. **Web层**
   ```python
   # 主应用入口
   app = Flask(__name__)
   
   # 用户认证
   login_manager = LoginManager(app)
   
   # 数据库初始化
   db = SQLAlchemy(app)
   ```

2. **数据模型**
   ```python
   # 用户模型
   class User(UserMixin, db.Model):
       id = db.Column(db.Integer, primary_key=True)
       username = db.Column(db.String(80), unique=True)
       role = db.Column(db.String(20))  # admin, vip, user
   
   # 任务模型
   class Task(db.Model):
       id = db.Column(db.Integer, primary_key=True)
       status = db.Column(db.String(20))  # pending, processing, completed, failed
   ```

3. **核心算法**
   ```python
   # 监考调度器
   class ExamScheduler:
       def __init__(self):
           self.solver = cp_model.CpSolver()
   
       def schedule(self):
           # 使用OR-Tools CP-SAT求解器
           pass
   ```

## 关键实现步骤

### 1. 基础设置
```python
# 配置文件 config.py
class Config:
    SECRET_KEY = 'your-secret-key'
    SQLALCHEMY_DATABASE_URI = 'sqlite:///app.db'
    UPLOAD_FOLDER = 'uploads'
    REDIS_HOST = 'localhost'
```

### 2. 用户认证
```python
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            login_user(user)
```

### 3. 文件处理
```python
class ExcelValidator:
    def validate(self):
        # 验证Excel文件格式和内容
        pass

def process_task_in_background(task_id, input_file, output_file):
    # 后台处理任务
    pass
```

### 4. 监考安排算法
```python
def create_scheduling_model():
    # 创建优化模型
    model = cp_model.CpModel()
    
    # 定义决策变量
    x = {}  # 监考员分配变量
    
    # 添加约束条件
    # 1. 时间冲突约束
    # 2. 考场人数约束
    # 3. 教师偏好约束
    
    # 设置优化目标
    model.Minimize(objective)
```

## 部署说明

### 开发环境
1. 创建虚拟环境：
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux
   venv\Scripts\activate     # Windows
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 初始化数据库：
   ```python
   flask db upgrade
   ```

### 生产环境
1. 使用Gunicorn作为WSGI服务器
2. 配置Nginx反向代理
3. 使用Supervisor管理进程
4. 启用SSL证书

## 测试用例

### 1. 用户功能测试
```python
def test_user_registration():
    # 测试用户注册功能
    pass

def test_user_login():
    # 测试用户登录功能
    pass
```

### 2. 任务处理测试
```python
def test_file_validation():
    # 测试文件验证功能
    pass

def test_task_scheduling():
    # 测试监考安排功能
    pass
```

## 注意事项

1. **安全性考虑**
   - 使用CSRF保护
   - 实现请求频率限制
   - 文件上传验证
   - 用户密码加密存储

2. **性能优化**
   - 使用Redis缓存
   - 实现任务队列
   - 文件异步处理
   - 数据库索引优化

3. **错误处理**
   - 全局错误处理
   - 日志记录
   - 用户友好的错误提示
   - 系统状态监控

## 扩展功能

1. **报表功能**
   - 监考统计报表
   - 用户活动报表
   - 系统使用报表

2. **API接口**
   - RESTful API设计
   - API文档生成
   - 接口认证

3. **系统监控**
   - 性能监控
   - 错误报警
   - 资源使用监控

## 维护指南

1. **日常维护**
   - 数据库备份
   - 日志清理
   - 临时文件清理

2. **故障处理**
   - 常见问题解决方案
   - 系统恢复流程
   - 紧急联系方式

3. **升级流程**
   - 版本升级步骤
   - 数据迁移方案
   - 回滚机制

## 参考资源

1. **技术文档**
   - Flask官方文档
   - OR-Tools文档
   - SQLAlchemy文档

2. **示例代码**
   - 完整项目示例
   - 核心功能示例
   - 测试用例示例

3. **学习资源**
   - 在线教程
   - 相关论文
   - 社区讨论 