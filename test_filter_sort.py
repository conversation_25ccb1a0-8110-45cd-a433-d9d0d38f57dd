#!/usr/bin/env python3
"""
测试监考员表格的筛选和排序功能
验证列头的筛选排序功能是否正常工作
"""

import os

def check_table_header_structure():
    """检查表格头部结构"""
    print("🔍 检查表格头部结构...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查表格头部结构
        header_checks = {
            '可排序列头': 'class="sortable"' in content,
            '排序图标': 'sort-icon' in content,
            '排序数据属性': 'data-sort=' in content,
            '筛选行': 'class="filter-row"' in content,
            '筛选输入框': 'filter-input' in content,
            '筛选下拉框': 'filter-select' in content,
            '清除筛选按钮': 'clear-filters' in content,
            '筛选数据属性': 'data-filter=' in content
        }
        
        all_passed = True
        for check_name, result in header_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_sortable_columns():
    """检查可排序的列"""
    print("\n🔍 检查可排序的列...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查可排序的列
        sortable_columns = {
            '序号排序': 'data-sort="index"' in content,
            '监考老师排序': 'data-sort="name"' in content,
            '任教科目排序': 'data-sort="teaching_subject"' in content,
            '场次限制排序': 'data-sort="session_limit"' in content
        }
        
        # 检查不可排序的列（多选字段）
        non_sortable_columns = {
            '必监考科目不可排序': 'data-sort="required_subjects"' not in content,
            '不监考科目不可排序': 'data-sort="unavailable_subjects"' not in content,
            '必监考考场不可排序': 'data-sort="required_rooms"' not in content,
            '不监考考场不可排序': 'data-sort="unavailable_rooms"' not in content
        }
        
        all_passed = True
        
        print("   可排序列:")
        for check_name, result in sortable_columns.items():
            status = "✅" if result else "❌"
            print(f"      {status} {check_name}")
            if not result:
                all_passed = False
        
        print("   不可排序列:")
        for check_name, result in non_sortable_columns.items():
            status = "✅" if result else "❌"
            print(f"      {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_filter_controls():
    """检查筛选控件"""
    print("\n🔍 检查筛选控件...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查筛选控件
        filter_controls = {
            '姓名筛选': 'data-filter="name"' in content,
            '任教科目筛选': 'data-filter="teaching_subject"' in content,
            '必监考科目筛选': 'data-filter="required_subjects"' in content,
            '不监考科目筛选': 'data-filter="unavailable_subjects"' in content,
            '必监考考场筛选': 'data-filter="required_rooms"' in content,
            '不监考考场筛选': 'data-filter="unavailable_rooms"' in content,
            '场次限制筛选': 'data-filter="session_limit"' in content
        }
        
        # 检查筛选控件类型
        filter_types = {
            '文本输入筛选': 'filter-input' in content,
            '下拉选择筛选': 'filter-select' in content,
            '数字输入筛选': 'type="number"' in content and 'filter-input' in content
        }
        
        all_passed = True
        
        print("   筛选字段:")
        for check_name, result in filter_controls.items():
            status = "✅" if result else "❌"
            print(f"      {status} {check_name}")
            if not result:
                all_passed = False
        
        print("   筛选控件类型:")
        for check_name, result in filter_types.items():
            status = "✅" if result else "❌"
            print(f"      {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_css_styles():
    """检查CSS样式"""
    print("\n🔍 检查CSS样式...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查CSS样式
        css_checks = {
            '可排序列样式': '.sortable' in content and 'cursor: pointer' in content,
            '排序图标样式': '.sort-icon' in content,
            '排序状态样式': '.sort-asc' in content and '.sort-desc' in content,
            '筛选行样式': '.filter-row' in content,
            '筛选输入框样式': '.filter-input' in content and 'height: 28px' in content,
            '筛选下拉框样式': '.filter-select' in content,
            '清除按钮样式': '.clear-filters' in content,
            '筛选激活状态': '.filter-active' in content,
            '悬停效果': ':hover' in content
        }
        
        all_passed = True
        for check_name, result in css_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_javascript_functionality():
    """检查JavaScript功能"""
    print("\n🔍 检查JavaScript功能...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查JavaScript功能
        js_checks = {
            '排序状态管理': 'currentSort' in content,
            '筛选状态管理': 'currentFilters' in content,
            '排序点击事件': '.sortable' in content and 'click' in content,
            '筛选输入事件': 'filter-input' in content and 'on(\'input change\'' in content,
            '清除筛选事件': 'clear-filters' in content and 'click' in content,
            '筛选排序应用': 'applyFiltersAndSort' in content,
            '筛选后渲染': 'renderFilteredProctors' in content,
            '排序逻辑': 'filteredProctors.sort' in content,
            '筛选逻辑': 'proctors.filter' in content,
            '状态指示': 'filter-active' in content
        }
        
        all_passed = True
        for check_name, result in js_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_sorting_scenarios():
    """测试排序场景"""
    print("\n🧪 测试排序场景...")
    
    # 模拟排序场景
    scenarios = [
        {
            'name': '按姓名排序',
            'field': 'name',
            'data': [
                {'name': '张三', 'teaching_subject': '语文'},
                {'name': '李四', 'teaching_subject': '数学'},
                {'name': '王五', 'teaching_subject': '英语'}
            ],
            'expected_asc': ['李四', '王五', '张三'],
            'expected_desc': ['张三', '王五', '李四']
        },
        {
            'name': '按场次限制排序',
            'field': 'session_limit',
            'data': [
                {'name': '张三', 'session_limit': '5'},
                {'name': '李四', 'session_limit': '2'},
                {'name': '王五', 'session_limit': '8'}
            ],
            'expected_asc': ['李四', '张三', '王五'],
            'expected_desc': ['王五', '张三', '李四']
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        try:
            # 模拟升序排序
            asc_sorted = sorted(scenario['data'], key=lambda x: x[scenario['field']])
            asc_names = [item['name'] for item in asc_sorted]
            
            # 模拟降序排序
            desc_sorted = sorted(scenario['data'], key=lambda x: x[scenario['field']], reverse=True)
            desc_names = [item['name'] for item in desc_sorted]
            
            # 验证排序结果
            if asc_names == scenario['expected_asc']:
                print(f"   ✅ 升序排序: {asc_names}")
            else:
                print(f"   ❌ 升序排序: 期望{scenario['expected_asc']}, 实际{asc_names}")
                all_passed = False
            
            if desc_names == scenario['expected_desc']:
                print(f"   ✅ 降序排序: {desc_names}")
            else:
                print(f"   ❌ 降序排序: 期望{scenario['expected_desc']}, 实际{desc_names}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 排序测试失败: {e}")
            all_passed = False
    
    return all_passed

def test_filtering_scenarios():
    """测试筛选场景"""
    print("\n🧪 测试筛选场景...")
    
    # 模拟筛选场景
    scenarios = [
        {
            'name': '按姓名筛选',
            'filter_field': 'name',
            'filter_value': '张',
            'data': [
                {'name': '张三', 'teaching_subject': '语文'},
                {'name': '李四', 'teaching_subject': '数学'},
                {'name': '张五', 'teaching_subject': '英语'}
            ],
            'expected': ['张三', '张五']
        },
        {
            'name': '按科目筛选',
            'filter_field': 'required_subjects',
            'filter_value': '语文',
            'data': [
                {'name': '张三', 'required_subjects': ['语文', '数学']},
                {'name': '李四', 'required_subjects': ['数学', '英语']},
                {'name': '王五', 'required_subjects': ['语文', '英语']}
            ],
            'expected': ['张三', '王五']
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        try:
            # 模拟筛选逻辑
            filtered_data = []
            for item in scenario['data']:
                if scenario['filter_field'] == 'name':
                    if scenario['filter_value'].lower() in item['name'].lower():
                        filtered_data.append(item)
                elif scenario['filter_field'] == 'required_subjects':
                    subjects = item.get('required_subjects', [])
                    if scenario['filter_value'] in subjects:
                        filtered_data.append(item)
            
            filtered_names = [item['name'] for item in filtered_data]
            
            # 验证筛选结果
            if filtered_names == scenario['expected']:
                print(f"   ✅ 筛选结果: {filtered_names}")
            else:
                print(f"   ❌ 筛选结果: 期望{scenario['expected']}, 实际{filtered_names}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 筛选测试失败: {e}")
            all_passed = False
    
    return all_passed

def analyze_feature_completeness():
    """分析功能完整性"""
    print("\n📊 分析功能完整性...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析功能完整性
        features = {
            '列头排序': 'sortable' in content and 'data-sort' in content,
            '排序图标': 'sort-icon' in content,
            '排序状态': 'sort-asc' in content and 'sort-desc' in content,
            '文本筛选': 'filter-input' in content,
            '下拉筛选': 'filter-select' in content,
            '筛选清除': 'clear-filters' in content,
            '筛选状态': 'filter-active' in content,
            '排序逻辑': 'applyFiltersAndSort' in content,
            '筛选逻辑': 'proctors.filter' in content,
            '状态管理': 'currentSort' in content and 'currentFilters' in content
        }
        
        implemented_features = sum(features.values())
        total_features = len(features)
        completeness_score = implemented_features / total_features * 100
        
        print(f"   📊 功能完整性分析:")
        for feature_name, implemented in features.items():
            status = "✅ 已实现" if implemented else "❌ 未实现"
            print(f"      {feature_name}: {status}")
        
        print(f"   📊 完整性评分: {completeness_score:.1f}%")
        
        if completeness_score >= 90:
            print(f"   ✅ 功能完整性: 优秀")
            return True
        elif completeness_score >= 70:
            print(f"   ⚠️  功能完整性: 良好")
            return True
        else:
            print(f"   ❌ 功能完整性: 需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 监考员表格筛选排序功能测试")
    print("=" * 60)
    print("测试列头的筛选排序功能是否正常工作")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查表格头部结构
    test_results.append(("表格头部结构", check_table_header_structure()))
    
    # 2. 检查可排序的列
    test_results.append(("可排序列", check_sortable_columns()))
    
    # 3. 检查筛选控件
    test_results.append(("筛选控件", check_filter_controls()))
    
    # 4. 检查CSS样式
    test_results.append(("CSS样式", check_css_styles()))
    
    # 5. 检查JavaScript功能
    test_results.append(("JavaScript功能", check_javascript_functionality()))
    
    # 6. 测试排序场景
    test_results.append(("排序场景测试", test_sorting_scenarios()))
    
    # 7. 测试筛选场景
    test_results.append(("筛选场景测试", test_filtering_scenarios()))
    
    # 8. 分析功能完整性
    test_results.append(("功能完整性", analyze_feature_completeness()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 筛选排序功能特点:")
        print("✅ 列头排序 - 点击列头进行升序/降序排序")
        print("✅ 多字段筛选 - 支持文本、下拉、数字筛选")
        print("✅ 实时筛选 - 输入即时生效，无需点击按钮")
        print("✅ 状态指示 - 排序和筛选状态清晰显示")
        print("✅ 清除功能 - 一键清除所有筛选条件")
        print("✅ 数据保持 - 筛选排序不影响原始数据")
        
        print("\n🚀 支持的操作:")
        print("• 按序号、姓名、任教科目、场次限制排序")
        print("• 按姓名、任教科目、场次限制文本筛选")
        print("• 按必监考科目、不监考科目下拉筛选")
        print("• 按必监考考场、不监考考场下拉筛选")
        print("• 组合筛选和排序操作")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
