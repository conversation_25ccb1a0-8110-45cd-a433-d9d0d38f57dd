#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复时间格式兼容性问题的脚本
解决向导->File_Processing->core之间的时间格式不匹配问题
"""

import json
import os
from datetime import datetime
import pandas as pd

def fix_json_time_format(json_file_path):
    """
    修复JSON文件中的时间格式问题
    将分离的日期和时间重新组合为完整的datetime格式
    """
    try:
        print(f"🔧 开始修复JSON文件: {json_file_path}")
        
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查是否有科目设置数据
        if 'data' not in data or 'subject_settings' not in data['data']:
            print("❌ JSON文件中没有找到科目设置数据")
            return False
        
        subject_records = data['data']['subject_settings']['records']
        
        print(f"📊 处理 {len(subject_records)} 个科目记录")
        
        # 修复每个科目记录的时间格式
        for i, record in enumerate(subject_records):
            try:
                # 获取日期、开始时间、结束时间
                exam_date = record.get('考试日期', '2025/02/08')  # 默认日期
                start_time = record.get('开始时间', '09:00')
                end_time = record.get('结束时间', '11:00')
                
                # 标准化日期格式
                if '/' in exam_date:
                    date_parts = exam_date.split('/')
                    if len(date_parts) == 3:
                        year, month, day = date_parts
                        normalized_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    else:
                        normalized_date = "2025-02-08"
                else:
                    normalized_date = exam_date
                
                # 组合完整的日期时间
                start_datetime = f"{normalized_date} {start_time}:00"
                end_datetime = f"{normalized_date} {end_time}:00"
                
                # 验证时间格式
                try:
                    datetime.strptime(start_datetime, "%Y-%m-%d %H:%M:%S")
                    datetime.strptime(end_datetime, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    print(f"⚠️  科目 {record.get('课程名称', '未知')} 的时间格式无效，使用默认时间")
                    start_datetime = f"{normalized_date} 09:00:00"
                    end_datetime = f"{normalized_date} 11:00:00"
                
                # 更新记录
                record['开始时间'] = start_datetime
                record['结束时间'] = end_datetime
                
                print(f"✅ 科目 {record.get('课程名称', '未知')}: {start_datetime} -> {end_datetime}")
                
            except Exception as e:
                print(f"❌ 处理科目 {i+1} 时发生错误: {e}")
                continue
        
        # 写回JSON文件
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON文件修复完成: {json_file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复JSON文件时发生错误: {e}")
        return False

def fix_excel_time_format(excel_file_path):
    """
    修复Excel文件中的时间格式，确保core模块能正确读取
    """
    try:
        print(f"🔧 开始修复Excel文件: {excel_file_path}")
        
        # 读取Excel文件
        subject_df = pd.read_excel(excel_file_path, sheet_name='考试科目设置')
        
        print(f"📊 原始科目数据:")
        for col in ['考试日期', '开始时间', '结束时间']:
            if col in subject_df.columns:
                print(f"  {col}: {subject_df[col].iloc[0]} (类型: {type(subject_df[col].iloc[0])})")
        
        # 修复时间格式
        for index, row in subject_df.iterrows():
            try:
                exam_date = str(row.get('考试日期', '2025/02/08')).strip()
                start_time = str(row.get('开始时间', '09:00')).strip()
                end_time = str(row.get('结束时间', '11:00')).strip()
                
                # 处理日期格式
                if '/' in exam_date:
                    date_obj = datetime.strptime(exam_date, '%Y/%m/%d')
                else:
                    try:
                        date_obj = datetime.strptime(exam_date, '%Y-%m-%d')
                    except:
                        date_obj = datetime(2025, 2, 8)  # 默认日期
                
                # 处理时间格式并组合
                try:
                    start_time_obj = datetime.strptime(start_time, '%H:%M').time()
                    start_datetime = datetime.combine(date_obj.date(), start_time_obj)
                except:
                    start_datetime = datetime.combine(date_obj.date(), datetime.strptime('09:00', '%H:%M').time())
                
                try:
                    end_time_obj = datetime.strptime(end_time, '%H:%M').time()
                    end_datetime = datetime.combine(date_obj.date(), end_time_obj)
                except:
                    end_datetime = datetime.combine(date_obj.date(), datetime.strptime('11:00', '%H:%M').time())
                
                # 更新DataFrame - 使用core模块期望的完整datetime格式
                subject_df.at[index, '开始时间'] = start_datetime.strftime('%Y-%m-%d %H:%M:%S')
                subject_df.at[index, '结束时间'] = end_datetime.strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"✅ 科目 {row.get('课程名称', '未知')}: {start_datetime} -> {end_datetime}")
                
            except Exception as e:
                print(f"❌ 处理科目 {index+1} 时发生错误: {e}")
                continue
        
        # 写回Excel文件
        with pd.ExcelWriter(excel_file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            subject_df.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        print(f"✅ Excel文件修复完成: {excel_file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复Excel文件时发生错误: {e}")
        return False

def fix_task_files(task_id):
    """
    修复指定任务的所有相关文件
    """
    task_dir = f"uploads/{task_id}"
    
    if not os.path.exists(task_dir):
        print(f"❌ 任务目录不存在: {task_dir}")
        return False
    
    print(f"🔧 开始修复任务 {task_id} 的文件...")
    
    success = True
    
    # 修复JSON文件
    json_file = os.path.join(task_dir, "考试配置处理结果.json")
    if os.path.exists(json_file):
        success &= fix_json_time_format(json_file)
    else:
        print(f"⚠️  JSON文件不存在: {json_file}")
    
    # 修复Excel文件
    excel_files = [
        os.path.join(task_dir, f"{task_id}_设置文件.xlsx"),
        os.path.join(task_dir, f"input_{task_id}.xlsx")
    ]
    
    for excel_file in excel_files:
        if os.path.exists(excel_file):
            success &= fix_excel_time_format(excel_file)
        else:
            print(f"⚠️  Excel文件不存在: {excel_file}")
    
    return success

if __name__ == "__main__":
    print("🔧 时间格式修复工具")
    print("=" * 50)
    
    # 修复任务90
    task_id = 90
    if fix_task_files(task_id):
        print(f"\n✅ 任务 {task_id} 的文件修复完成！")
        print("现在可以重新尝试处理任务了。")
    else:
        print(f"\n❌ 任务 {task_id} 的文件修复失败！") 