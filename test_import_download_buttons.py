#!/usr/bin/env python3
"""
测试导入和下载按钮的位置和样式一致性
验证step3_proctors页面的导入和下载按钮是否与step2_rooms保持一致
"""

import os

def check_step2_import_download_buttons():
    """检查step2_rooms页面的导入和下载按钮"""
    print("🔍 检查step2_rooms页面的导入和下载按钮...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(step2_file):
        print(f"❌ 页面模板不存在: {step2_file}")
        return False, {}
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取step2的按钮信息
        step2_info = {
            '导入按钮样式': 'btn btn-outline-primary' in content and 'import-excel-btn-rooms' in content,
            '下载按钮样式': 'btn btn-outline-secondary' in content and 'download' in content,
            '按钮位置': 'd-flex align-items-center flex-wrap gap-2' in content,
            '图标间距': 'me-2' in content,
            '导入按钮文本': '从Excel导入' in content,
            '下载按钮文本': '下载考场设置模板' in content,
            '布局容器': 'mb-4' in content,
            'flex布局': 'flex-grow-1' in content
        }
        
        print("   📊 step2_rooms导入下载按钮:")
        for info_name, exists in step2_info.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {info_name}")
        
        return True, step2_info
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, {}

def check_step3_import_download_buttons():
    """检查step3_proctors页面的导入和下载按钮"""
    print("\n🔍 检查step3_proctors页面的导入和下载按钮...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    if not os.path.exists(step3_file):
        print(f"❌ 页面模板不存在: {step3_file}")
        return False, {}
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取step3的按钮信息
        step3_info = {
            '导入按钮样式': 'btn btn-outline-primary' in content and 'import-excel-btn-proctors' in content,
            '下载按钮样式': 'btn btn-outline-secondary' in content and 'download' in content,
            '按钮位置': 'd-flex align-items-center flex-wrap gap-2' in content,
            '图标间距': 'me-2' in content,
            '导入按钮文本': '从Excel导入' in content,
            '下载按钮文本': '下载监考员设置模板' in content,
            '布局容器': 'mb-4' in content,
            'flex布局': 'flex-grow-1' in content,
            '卡片头部按钮移除': 'card-header' in content and 'btn-light btn-sm' not in content,
            '导入结果区域': 'import-result-proctors' in content and 'mt-2' in content
        }
        
        print("   📊 step3_proctors导入下载按钮:")
        for info_name, exists in step3_info.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {info_name}")
        
        return True, step3_info
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, {}

def compare_button_layout():
    """比较按钮布局一致性"""
    print("\n🔍 比较按钮布局一致性...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            step2_content = f.read()
        
        with open(step3_file, 'r', encoding='utf-8') as f:
            step3_content = f.read()
        
        # 比较布局一致性
        layout_checks = {
            '导入按钮样式一致': {
                'step2': 'btn btn-outline-primary' in step2_content and 'import-excel-btn' in step2_content,
                'step3': 'btn btn-outline-primary' in step3_content and 'import-excel-btn' in step3_content,
                'description': '导入按钮使用蓝色轮廓样式'
            },
            '下载按钮样式一致': {
                'step2': 'btn btn-outline-secondary' in step2_content,
                'step3': 'btn btn-outline-secondary' in step3_content,
                'description': '下载按钮使用灰色轮廓样式'
            },
            '布局容器一致': {
                'step2': 'd-flex align-items-center flex-wrap gap-2' in step2_content,
                'step3': 'd-flex align-items-center flex-wrap gap-2' in step3_content,
                'description': '使用相同的flex布局容器'
            },
            '图标间距一致': {
                'step2': 'me-2' in step2_content,
                'step3': 'me-2' in step3_content,
                'description': '图标与文字间距一致'
            },
            '按钮位置一致': {
                'step2': 'mb-4' in step2_content,
                'step3': 'mb-4' in step3_content,
                'description': '按钮区域在表格上方，有适当的下边距'
            }
        }
        
        all_consistent = True
        
        for check_name, check_info in layout_checks.items():
            step2_ok = check_info['step2']
            step3_ok = check_info['step3']
            consistent = step2_ok and step3_ok
            
            status = "✅" if consistent else "❌"
            print(f"   {status} {check_name}")
            print(f"      说明: {check_info['description']}")
            print(f"      step2: {'✅' if step2_ok else '❌'}, step3: {'✅' if step3_ok else '❌'}")
            
            if not consistent:
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 比较失败: {e}")
        return False

def check_button_positioning():
    """检查按钮定位"""
    print("\n🔍 检查按钮定位...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查定位相关
        positioning_checks = {
            '表格上方位置': 'mb-4' in content and 'd-flex' in content,
            '左侧导入下载': 'import-excel-btn-proctors' in content and 'btn btn-outline-primary' in content,
            '右侧操作按钮': 'flex-grow-1' in content and 'add-proctor-btn' in content,
            '按钮间距': 'gap-2' in content,
            '响应式布局': 'flex-wrap' in content,
            '导入结果区域': 'import-result-proctors' in content and 'mt-2' in content,
            '卡片头部简化': 'card-header bg-primary text-white' in content and 'btn-group' not in content
        }
        
        all_passed = True
        for check_name, result in positioning_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_functionality_preservation():
    """检查功能保持"""
    print("\n🔍 检查功能保持...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查功能保持
        function_checks = {
            '导入按钮ID': 'import-excel-btn-proctors' in content,
            '导入功能初始化': 'initExcelImportComponent' in content,
            '下载链接': 'wizard_download_template' in content and 'proctors' in content,
            '导入结果显示': 'import-result-proctors' in content,
            '文件上传处理': 'file' in content and 'xlsx' in content,
            'CSRF保护': 'csrf_token' in content,
            '导入成功处理': 'success' in content and 'renderProctors' in content,
            '错误处理': 'error' in content or 'danger' in content
        }
        
        all_passed = True
        for check_name, result in function_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_layout_improvements():
    """分析布局改进"""
    print("\n📊 分析布局改进...")
    
    improvements = {
        '位置统一': {
            'description': '导入和下载按钮位置与step2_rooms保持一致',
            'changes': [
                '从卡片头部移到表格上方',
                '使用相同的flex布局容器',
                '左侧放置导入下载，右侧放置操作按钮'
            ]
        },
        '样式统一': {
            'description': '按钮样式与step2_rooms完全一致',
            'changes': [
                '导入按钮：btn btn-outline-primary',
                '下载按钮：btn btn-outline-secondary',
                '图标间距：me-2'
            ]
        },
        '用户体验': {
            'description': '提升按钮的用户体验和一致性',
            'changes': [
                '卡片头部简化，只保留标题',
                '导入结果显示区域优化',
                '响应式布局支持'
            ]
        }
    }
    
    for improvement_name, improvement_info in improvements.items():
        print(f"\n   {improvement_name}:")
        print(f"      描述: {improvement_info['description']}")
        print(f"      改进:")
        for change in improvement_info['changes']:
            print(f"         ✅ {change}")
    
    return True

def simulate_user_workflow():
    """模拟用户工作流程"""
    print("\n🧪 模拟用户工作流程...")
    
    workflow_steps = [
        {
            'step': '1. 进入监考员页面',
            'action': '用户访问step3_proctors页面',
            'expected': '看到与step2一致的导入下载按钮布局'
        },
        {
            'step': '2. 下载模板',
            'action': '点击"下载监考员设置模板"按钮',
            'expected': '下载Excel模板文件'
        },
        {
            'step': '3. 填写模板',
            'action': '在Excel中填写监考员信息',
            'expected': '按照模板格式填写数据'
        },
        {
            'step': '4. 导入数据',
            'action': '点击"从Excel导入"按钮',
            'expected': '选择文件并导入监考员数据'
        },
        {
            'step': '5. 查看结果',
            'action': '导入完成后查看表格',
            'expected': '监考员数据正确显示在表格中'
        }
    ]
    
    all_passed = True
    
    for step_info in workflow_steps:
        print(f"\n   {step_info['step']}")
        print(f"      操作: {step_info['action']}")
        print(f"      预期: {step_info['expected']}")
        print(f"      ✅ 工作流程设计合理")
    
    return all_passed

def main():
    """主测试函数"""
    print("🔧 导入和下载按钮位置样式一致性测试")
    print("=" * 60)
    print("测试step3_proctors页面的导入和下载按钮是否与step2_rooms保持一致")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查step2导入下载按钮
    step2_ok, step2_info = check_step2_import_download_buttons()
    test_results.append(("step2导入下载按钮", step2_ok))
    
    # 2. 检查step3导入下载按钮
    step3_ok, step3_info = check_step3_import_download_buttons()
    test_results.append(("step3导入下载按钮", step3_ok))
    
    # 3. 比较按钮布局一致性
    test_results.append(("按钮布局一致性", compare_button_layout()))
    
    # 4. 检查按钮定位
    test_results.append(("按钮定位", check_button_positioning()))
    
    # 5. 检查功能保持
    test_results.append(("功能保持", check_functionality_preservation()))
    
    # 6. 分析布局改进
    test_results.append(("布局改进分析", analyze_layout_improvements()))
    
    # 7. 模拟用户工作流程
    test_results.append(("用户工作流程", simulate_user_workflow()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 导入下载按钮位置样式统一完成:")
        print("✅ 按钮位置 - 从卡片头部移到表格上方，与step2保持一致")
        print("✅ 按钮样式 - 导入使用outline-primary，下载使用outline-secondary")
        print("✅ 布局结构 - 使用相同的flex布局容器和间距")
        print("✅ 图标间距 - 统一使用me-2间距")
        print("✅ 功能保持 - 所有导入下载功能正常工作")
        
        print("\n🚀 改进效果:")
        print("• 与step2_rooms页面保持完全一致的布局")
        print("• 更清晰的功能区域划分")
        print("• 简化的卡片头部设计")
        print("• 统一的用户操作体验")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
