<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文件输入功能</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>测试文件输入功能</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>方法1: 使用 d-none 类</h3>
                <button type="button" id="btn1" class="btn btn-primary">从Excel导入 (d-none)</button>
                <input type="file" id="file1" accept=".xlsx,.xls" class="d-none">
            </div>
            
            <div class="col-md-6">
                <h3>方法2: 使用 style="display: none;"</h3>
                <button type="button" id="btn2" class="btn btn-success">从Excel导入 (style)</button>
                <input type="file" id="file2" accept=".xlsx,.xls" style="display: none;">
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>方法3: 可见的文件输入</h3>
                <input type="file" id="file3" accept=".xlsx,.xls" class="form-control">
            </div>
        </div>
        
        <div class="mt-4">
            <h3>测试结果:</h3>
            <div id="results" class="alert alert-info"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#results').append(`<div>[${timestamp}] ${message}</div>`);
            console.log(message);
        }

        $(document).ready(function() {
            log('页面加载完成');
            
            // 方法1: d-none
            $('#btn1').click(function() {
                log('按钮1被点击 (d-none)');
                try {
                    $('#file1').click();
                    log('成功触发文件1点击');
                } catch (error) {
                    log('错误: ' + error.message);
                }
            });
            
            // 方法2: style display none
            $('#btn2').click(function() {
                log('按钮2被点击 (style)');
                try {
                    $('#file2').click();
                    log('成功触发文件2点击');
                } catch (error) {
                    log('错误: ' + error.message);
                }
            });
            
            // 监听文件选择
            $('#file1, #file2, #file3').change(function(e) {
                const file = e.target.files[0];
                const inputId = e.target.id;
                if (file) {
                    log(`${inputId}: 文件已选择 - ${file.name}`);
                } else {
                    log(`${inputId}: 没有选择文件`);
                }
            });
            
            log('事件绑定完成');
        });
    </script>
</body>
</html>
