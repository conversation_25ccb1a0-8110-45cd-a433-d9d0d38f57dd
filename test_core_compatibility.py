#!/usr/bin/env python3
"""
测试引导页面生成的文件是否与core程序兼容
"""

import pandas as pd
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def generate_core_compatible_excel(wizard_data, file_path):
    """生成与core程序兼容的Excel文件"""
    
    # 1. 考试科目设置表 - core程序兼容格式
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')
        
        start_datetime = None
        end_datetime = None
        
        try:
            if start_time_str:
                start_datetime = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
                        
            if end_time_str:
                end_datetime = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
        except Exception as e:
            print(f"解析时间时出错: {e}")
            
        if not start_datetime:
            start_datetime = datetime(2025, 1, 1, 9, 0)
        if not end_datetime:
            end_datetime = datetime(2025, 1, 1, 11, 0)
            
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': start_datetime.strftime('%Y/%m/%d'),
            '开始时间': start_datetime,  # core程序需要的完整datetime
            '结束时间': end_datetime     # core程序需要的完整datetime
        })
    
    df_subjects = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 2. 监考员设置表 - core程序兼容格式
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            session_limit = proctor.get('session_limit', 99)
            try:
                session_limit = int(session_limit)
                if session_limit <= 0:
                    session_limit = 99
            except (ValueError, TypeError):
                session_limit = 99
            
            proctors_data.append({
                '序号': i,
                '监考老师': proctor.get('name', '').strip(),
                '任教科目': proctor.get('teaching_subject', ''),
                '场次限制': session_limit,
                '必监考科目': proctor.get('required_subjects', ''),
                '不监考科目': proctor.get('forbidden_subjects', ''),
                '必监考考场': proctor.get('required_rooms', ''),
                '不监考考场': proctor.get('forbidden_rooms', '')
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 3. 考场设置表 - core程序兼容格式
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

def test_core_data_loading(file_path):
    """测试core程序的数据加载逻辑"""
    try:
        # 模拟core程序的数据加载过程
        print("测试core程序数据加载...")
        
        # 读取Excel文件的三个工作表
        teacher_df = pd.read_excel(file_path, sheet_name='监考员设置')
        subject_df = pd.read_excel(file_path, sheet_name='考试科目设置')
        room_df = pd.read_excel(file_path, sheet_name='考场设置')
        
        print("✅ 成功读取所有工作表")
        
        # 测试Teacher.from_excel_row所需的列
        print("\n=== 监考员设置表检查 ===")
        print("列名:", list(teacher_df.columns))
        required_teacher_cols = ['序号', '监考老师', '任教科目', '场次限制']
        missing_teacher_cols = [col for col in required_teacher_cols if col not in teacher_df.columns]
        
        if missing_teacher_cols:
            print(f"❌ 缺少必需列: {missing_teacher_cols}")
            return False
        else:
            print("✅ 包含所有必需列")
        
        # 测试Subject.from_excel_row所需的列
        print("\n=== 考试科目设置表检查 ===")
        print("列名:", list(subject_df.columns))
        required_subject_cols = ['课程代码', '课程名称', '开始时间', '结束时间']
        missing_subject_cols = [col for col in required_subject_cols if col not in subject_df.columns]
        
        if missing_subject_cols:
            print(f"❌ 缺少必需列: {missing_subject_cols}")
            return False
        else:
            print("✅ 包含所有必需列")
        
        # 检查时间格式
        print("\n时间格式检查:")
        for i, row in subject_df.iterrows():
            start_time = row['开始时间']
            end_time = row['结束时间']
            print(f"  {row['课程名称']}: 开始={start_time} (类型: {type(start_time).__name__}), 结束={end_time} (类型: {type(end_time).__name__})")
            
            # 检查是否可以转换为datetime
            try:
                start_dt = pd.to_datetime(start_time)
                end_dt = pd.to_datetime(end_time)
                print(f"    ✅ 时间格式正确: {start_dt} - {end_dt}")
            except Exception as e:
                print(f"    ❌ 时间格式错误: {e}")
                return False
        
        # 测试Room.from_excel_row所需的列
        print("\n=== 考场设置表检查 ===")
        print("列名:", list(room_df.columns))
        if '考场' not in room_df.columns:
            print("❌ 缺少考场列")
            return False
        else:
            print("✅ 包含考场列")
        
        # 检查科目列
        subject_columns = [col for col in room_df.columns if col != '考场']
        print(f"科目列: {subject_columns}")
        
        print("\n🎉 所有检查通过！文件与core程序兼容！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    # 测试数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'},
            {'code': 'C', 'name': '英语', 'start_time': '2025/02/09 09:00', 'end_time': '2025/02/09 11:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3, 'teaching_subject': '语文'},
            {'name': '李老师', 'session_limit': 2, 'teaching_subject': '数学'},
            {'name': '王老师', 'session_limit': 2, 'teaching_subject': '英语'}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1, '英语': 1}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2, '英语': 1}},
            {'name': 'B201', 'demands': {'语文': 1, '数学': 1, '英语': 2}}
        ]
    }

    test_file = 'test_core_compatible.xlsx'
    print("生成与core程序兼容的Excel文件...")
    generate_core_compatible_excel(wizard_data, test_file)
    print(f"✅ 已生成文件: {test_file}")

    # 测试兼容性
    success = test_core_data_loading(test_file)
    
    if success:
        print(f"\n✅ 测试成功！文件 {test_file} 与core程序完全兼容")
    else:
        print(f"\n❌ 测试失败！需要进一步调整")

if __name__ == '__main__':
    main()
