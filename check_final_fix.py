#!/usr/bin/env python3
"""
检查最终修复结果
"""

import pandas as pd
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_file():
    """检查文件"""
    test_file = 'test_final_fix.xlsx'
    
    if not os.path.exists(test_file):
        print(f"文件 {test_file} 不存在")
        return False
    
    print(f"检查文件: {test_file}")
    
    # 检查考试科目设置表
    df = pd.read_excel(test_file, sheet_name='考试科目设置')
    print("\n=== 考试科目设置表 ===")
    print("列名:", list(df.columns))
    print("数据:")
    print(df.to_string())
    
    print("\n时间格式检查:")
    for i, row in df.iterrows():
        start_time = row['开始时间']
        end_time = row['结束时间']
        exam_date = row['考试日期']
        print(f"  {row['课程名称']}: 日期={exam_date}, 开始={start_time}, 结束={end_time}")
        print(f"    类型: 日期={type(exam_date).__name__}, 开始={type(start_time).__name__}, 结束={type(end_time).__name__}")
    
    # 检查监考员设置表
    df_proctors = pd.read_excel(test_file, sheet_name='监考员设置')
    print("\n=== 监考员设置表 ===")
    print("列名:", list(df_proctors.columns))
    print("数据:")
    print(df_proctors.to_string())
    
    # 检查考场设置表
    df_rooms = pd.read_excel(test_file, sheet_name='考场设置')
    print("\n=== 考场设置表 ===")
    print("列名:", list(df_rooms.columns))
    print("数据:")
    print(df_rooms.to_string())
    
    # 计算需求和供给
    total_demand = 0
    for _, room_row in df_rooms.iterrows():
        for col in df_rooms.columns:
            if col != '考场':
                total_demand += room_row[col]
    
    total_supply = df_proctors['场次限制'].sum()
    
    print(f"\n=== 需求分析 ===")
    print(f"总需求: {total_demand}")
    print(f"总供给: {total_supply}")
    print(f"是否足够: {'✅' if total_supply >= total_demand else '❌'}")
    
    return True

def test_validator():
    """测试验证器"""
    try:
        from excel_validator import ExcelValidator
        
        test_file = 'test_final_fix.xlsx'
        print(f"\n=== 验证器测试 ===")
        print(f"测试文件: {test_file}")
        
        validator = ExcelValidator(test_file)
        is_valid = validator.validate()
        
        if is_valid:
            print("✅ 验证器测试通过")
        else:
            print("❌ 验证器测试失败")
        
        if validator.errors:
            print("\n错误信息:")
            for error in validator.errors:
                print(f"  - {error}")
        
        if validator.warnings:
            print("\n警告信息:")
            for warning in validator.warnings:
                print(f"  - {warning}")
        
        return is_valid
        
    except Exception as e:
        print(f"验证器测试时出错: {e}")
        return False

def main():
    print("=== 检查最终修复结果 ===")
    
    # 1. 检查文件内容
    file_ok = check_file()
    
    # 2. 测试验证器
    validator_ok = test_validator()
    
    print("\n" + "="*50)
    
    if file_ok and validator_ok:
        print("🎉 最终修复成功！")
        print("\n修复要点:")
        print("  ✅ 时间格式使用HH:MM格式，满足验证器要求")
        print("  ✅ 保留考试日期列，提供日期信息")
        print("  ✅ 监考员设置表包含所有必需列")
        print("  ✅ 文件格式与core程序兼容")
        print("  ✅ 通过验证器检查")
        
        print("\n现在引导页面生成的设置文件应该能够:")
        print("  1. 通过验证器检查")
        print("  2. 被core监考安排程序正确处理")
        
    else:
        print("❌ 还有问题需要解决")
        if not file_ok:
            print("  - 文件检查失败")
        if not validator_ok:
            print("  - 验证器测试失败")

if __name__ == '__main__':
    main()
