{% extends "base.html" %}

{% block title %}{{ task.title }} - 任务详情{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: var(--card-shadow);
    }

    .task-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
        margin-bottom: 24px;
    }

    .task-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        position: relative;
    }

    .task-title-container {
        flex-grow: 1;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .task-title {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .edit-title-btn {
        background: none;
        border: 1px solid #dee2e6;
        color: #6c757d;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .edit-title-btn:hover {
        color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
        border-color: #0d6efd;
    }

    .edit-title-form {
        display: none;
        width: 100%;
    }

    .edit-title-form.active {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .edit-title-form input {
        flex-grow: 1;
        height: 38px;
        padding: 0.375rem 0.75rem;
        font-size: 1.75rem;
        font-weight: 600;
        border: 2px solid #dee2e6;
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }

    .edit-title-form input:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        outline: none;
    }

    .edit-title-form button {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .edit-title-form button:hover {
        transform: translateY(-1px);
    }

    .task-status {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 0.875rem;
        white-space: nowrap;
    }

    .task-body {
        padding: 25px;
    }

    .task-section {
        margin-bottom: 30px;
    }

    .task-section:last-child {
        margin-bottom: 0;
    }

    .task-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        color: var(--primary-color);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .info-item {
        margin-bottom: 10px;
    }

    .info-label {
        font-weight: 500;
        color: var(--text-secondary);
        margin-bottom: 5px;
        font-size: 0.85rem;
    }

    .info-value {
        font-weight: 500;
    }

    .progress-container {
        background-color: rgba(var(--bs-primary-rgb), 0.05);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .progress {
        height: 10px;
        border-radius: 5px;
        margin: 15px 0;
        background-color: rgba(0, 0, 0, 0.05);
    }

    .progress-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin: 0;
        line-height: 1;
    }

    .progress-label {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .action-btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s;
        margin-right: 10px;
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .action-btn i {
        margin-right: 8px;
    }

    .log-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
        height: 100%;
    }

    .log-header {
        background-color: rgba(var(--bs-dark-rgb), 0.03);
        padding: 15px 20px;
        border-bottom: none;
    }

    .log-container {
        max-height: 500px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 15px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 0.85rem;
        line-height: 1.5;
    }

    .log-line {
        margin: 0;
        padding: 3px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    }

    .log-line:last-child {
        border-bottom: none;
    }

    .log-timestamp {
        color: var(--text-secondary);
        margin-right: 10px;
        font-size: 0.8rem;
    }

    .log-empty {
        color: var(--text-secondary);
        font-style: italic;
        text-align: center;
        padding: 20px;
    }

    .error-container {
        background-color: rgba(var(--bs-danger-rgb), 0.05);
        border-left: 4px solid var(--bs-danger);
        padding: 15px;
        border-radius: 4px;
    }

    .error-title {
        color: var(--bs-danger);
        font-weight: 600;
        margin-bottom: 10px;
    }

    .error-message {
        font-family: 'Consolas', 'Monaco', monospace;
        white-space: pre-wrap;
        font-size: 0.9rem;
    }

    .file-info {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: rgba(var(--bs-primary-rgb), 0.05);
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .file-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .file-details {
        flex-grow: 1;
    }

    .file-name {
        font-weight: 500;
        margin: 0;
    }

    .file-meta {
        font-size: 0.8rem;
        color: var(--text-secondary);
        margin: 0;
    }

    .breadcrumb-custom {
        background-color: transparent;
        padding: 0;
        margin-bottom: 20px;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s;
    }

    .breadcrumb-item a:hover {
        color: var(--primary-light);
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--text-secondary);
    }

    .status-badge {
        font-size: 0.85rem;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 500;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .badge-processing {
        animation: pulse 2s infinite;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-custom">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt me-1"></i> 控制台
            </a></li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-clipboard-list me-1"></i> 任务详情
            </li>
        </ol>
    </nav>
</div>

<div class="container">
    <div class="task-header">
        <div class="task-title-container">
            <h2 class="task-title" id="taskTitle">{{ task.title }}</h2>
            {% if task.user_id == current_user.id or current_user.role == 'admin' %}
            <button type="button" class="edit-title-btn" onclick="toggleEditTitle()">
                <i class="fas fa-edit"></i> 编辑任务名称
            </button>
            {% endif %}

            <form id="editTitleForm" class="edit-title-form" onsubmit="updateTaskTitle(event)">
                <input type="text" class="form-control" id="newTitle" value="{{ task.title }}" required>
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="toggleEditTitle()">取消</button>
            </form>
        </div>
        <span class="badge bg-{{ task.status_color }} task-status">{{ task.status_display }}</span>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Task Details Card -->
            <div class="task-card">
                <div class="task-body">
                    <!-- Progress Section -->
                    <div class="task-section">
                        <div class="progress-container text-center">
                            <h4 class="progress-value">{{ task.progress }}%</h4>
                            <p class="progress-label">处理进度</p>
                            <div class="progress">
                                <div id="progress-bar"
                                     class="progress-bar progress-bar-striped {% if task.status == 'processing' %}progress-bar-animated{% endif %}"
                                     role="progressbar"
                                     style="width: {{ task.progress }}%; background-color: {% if task.status == 'completed' %}#198754{% elif task.status == 'failed' %}#dc3545{% else %}#0d6efd{% endif %};"
                                     aria-valuenow="{{ task.progress }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Task Information Section -->
                    <div class="task-section">
                        <h5 class="task-section-title">
                            <i class="fas fa-info-circle me-2"></i> 任务信息
                        </h5>

                        {% if task.description %}
                        <div class="mb-4">
                            <p class="mb-0">{{ task.description }}</p>
                        </div>
                        {% endif %}

                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">状态</div>
                                <div class="info-value">{{ task.status_display }}</div>
                            </div>

                            {% if task.updated_at %}
                            <div class="info-item">
                                <div class="info-label">更新时间</div>
                                <div class="info-value">{{ task.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- File Information Section -->
                    <div class="task-section">
                        <h5 class="task-section-title">
                            <i class="fas fa-file me-2"></i> 文件信息
                        </h5>

                        {% if task.template_file %}
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div class="file-details">
                                <h6 class="file-name">设置文件.xlsx</h6>
                                <p class="file-meta">提交的设置文件</p>
                            </div>
                            <a href="{{ url_for('download_template', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i> 下载
                            </a>
                        </div>
                        {% endif %}

                        <!-- 输入文件显示已移除 -->

                        {% if task.status == 'completed' and task.output_file %}
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div class="file-details">
                                <h6 class="file-name">结果文件.xlsx</h6>
                                <!-- 实际文件名为: {{ task.output_file.split('\\')[-1] if '\\' in task.output_file else task.output_file.split('/')[-1] }} -->
                                <p class="file-meta">处理结果文件</p>
                            </div>
                            <a href="{{ url_for('download_result', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i> 下载
                            </a>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Error Message Section (if any) -->
                    {% if task.error_message %}
                    <div class="task-section">
                        <h5 class="task-section-title">
                            <i class="fas fa-exclamation-triangle me-2"></i> 错误信息
                        </h5>
                        <div class="error-container">
                            <div class="error-title">处理过程中发生错误</div>
                            <div class="error-message">{{ task.error_message }}</div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Actions Section -->
                    <div class="task-section">
                        <h5 class="task-section-title">
                            <i class="fas fa-cogs me-2"></i> 操作
                        </h5>
                        <div class="d-flex flex-wrap">
                            {% if task.status == 'pending' %}
                            <form method="POST" action="{{ url_for('schedule_task', task_id=task.id) }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-primary action-btn">
                                    <i class="fas fa-play"></i> 开始处理
                                </button>
                            </form>
                            {% endif %}

                            {% if task.status == 'completed' and task.output_file %}
                            <a href="{{ url_for('download_result', task_id=task.id) }}" class="btn btn-success action-btn">
                                <i class="fas fa-download"></i> 下载结果
                            </a>
                            {% endif %}

                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary action-btn">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Log Card -->
            <div class="log-card">
                <div class="log-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-terminal me-2"></i> 处理日志
                    </h5>
                    <span class="badge bg-secondary" id="log-count">0 条</span>
                </div>
                <div id="log-container" class="log-container">
                    <div id="log-content">
                        <p class="log-empty">等待日志输出...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if task.status == 'processing' %}
<script>
    $(document).ready(function() {
        let logCount = 0;
        let lastProgressValue = 0;

        // 初始化日志
        function initLogs() {
            const timestamp = new Date().toLocaleTimeString();
            const logHtml = '<p class="log-line">' +
                           '<span class="log-timestamp">[' + timestamp + ']</span>' +
                           '<span style="color: #198754;"><i class="fas fa-info-circle"></i> 正在连接服务器，准备获取任务进度...</span></p>';
            $("#log-content").html(logHtml);
        }

        // 初始化
        initLogs();

        // 定时获取任务进度
        function updateProgress() {
            $.ajax({
                url: "{{ url_for('get_task_progress', task_id=task.id) }}",
                type: "GET",
                dataType: "json",
                success: function(data) {
                    // 更新进度条
                    $("#progress-bar").css("width", data.progress + "%");
                    $("#progress-bar").attr("aria-valuenow", data.progress);
                    $(".progress-value").text(data.progress + "%");

                    // 创建日志内容
                    var logHtml = '';

                    // 添加处理日志
                    if (data.logs && data.logs.length > 0) {
                        logCount = data.logs.length;
                        $("#log-count").text(logCount + " 条");

                        for (var i = 0; i < data.logs.length; i++) {
                            const timestamp = new Date().toLocaleTimeString();
                            logHtml += '<p class="log-line">' +
                                       '<span class="log-timestamp">[' + timestamp + ']</span>' +
                                       data.logs[i] + '</p>';
                        }
                    }

                    // 只有当进度变化时才添加进度信息
                    if (data.progress !== lastProgressValue) {
                        lastProgressValue = data.progress;

                        // 添加进度信息
                        const progressTimestamp = new Date().toLocaleTimeString();
                        logHtml += '<p class="log-line" style="background-color: rgba(13, 110, 253, 0.1); padding: 8px; border-radius: 4px; margin-top: 10px;">' +
                                   '<span class="log-timestamp">[' + progressTimestamp + ']</span>' +
                                   '<span style="color: #0d6efd; font-weight: bold;">当前进度: ' + data.progress + '%</span>' +
                                   ' <span class="badge rounded-pill bg-' + data.status_color + '">' + data.status_display + '</span></p>';
                    }

                    // 如果没有任何日志内容，确保至少显示最基本的进度
                    if (!logHtml) {
                        const timestamp = new Date().toLocaleTimeString();
                        logHtml = '<p class="log-line">' +
                                  '<span class="log-timestamp">[' + timestamp + ']</span>' +
                                  '<span style="color: #198754;"><i class="fas fa-sync-alt fa-spin"></i> 任务正在处理中...</span></p>';
                    }

                    // 更新日志容器
                    $("#log-content").html(logHtml);
                    $("#log-container").scrollTop($("#log-container")[0].scrollHeight);

                    // 如果任务状态改变，刷新页面
                    if (data.status !== 'processing') {
                        location.reload();
                    } else {
                        // 继续轮询
                        setTimeout(updateProgress, 2000);
                    }
                },
                error: function() {
                    // 出错时也继续轮询
                    setTimeout(updateProgress, 5000);
                }
            });
        }

        // 开始轮询
        updateProgress();
    });
</script>
{% endif %}

<script>
function toggleEditTitle() {
    const titleContainer = document.querySelector('.task-title-container');
    const titleElement = document.getElementById('taskTitle');
    const editButton = document.querySelector('.edit-title-btn');
    const editForm = document.getElementById('editTitleForm');
    const newTitleInput = document.getElementById('newTitle');

    if (editForm.classList.contains('active')) {
        // 取消编辑
        editForm.classList.remove('active');
        titleElement.style.display = '';
        editButton.style.display = '';
    } else {
        // 开始编辑
        editForm.classList.add('active');
        titleElement.style.display = 'none';
        editButton.style.display = 'none';
        newTitleInput.focus();
        newTitleInput.select();
    }
}

function updateTaskTitle(event) {
    event.preventDefault();

    const newTitle = document.getElementById('newTitle').value.trim();
    if (!newTitle) {
        return;
    }

    const taskId = '{{ task.id }}';
    const submitButton = event.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    const titleElement = document.getElementById('taskTitle');
    const editButton = document.querySelector('.edit-title-btn');
    const editForm = document.getElementById('editTitleForm');

    // 显示加载状态
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    submitButton.disabled = true;

    fetch(`/task/${taskId}/update`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            title: newTitle
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 更新页面标题
            titleElement.textContent = data.title;
            document.title = `${data.title} - 任务详情`;

            // 退出编辑模式
            editForm.classList.remove('active');
            titleElement.style.display = '';
            editButton.style.display = '';

            // 显示成功消息
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.task-header'));
        } else {
            // 显示错误消息
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.task-header'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // 显示错误消息
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>更新失败，请稍后重试
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.container').insertBefore(alert, document.querySelector('.task-header'));
    })
    .finally(() => {
        // 恢复按钮状态
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

// 如果按下ESC键，取消编辑
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const editForm = document.getElementById('editTitleForm');
        if (editForm.classList.contains('active')) {
            toggleEditTitle();
        }
    }
});
</script>
{% endblock %}
