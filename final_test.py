#!/usr/bin/env python3
"""
最终测试修复后的Excel生成功能
"""

import pandas as pd
from datetime import datetime

def generate_test_excel(wizard_data, file_path):
    """生成测试Excel文件"""
    
    # 1. 考试科目设置表
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')
        
        exam_date = '2025/01/01'
        start_time = '09:00'
        end_time = '11:00'
        
        try:
            if start_time_str:
                start_datetime = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
                exam_date = start_datetime.strftime('%Y/%m/%d')
                start_time = start_datetime.strftime('%H:%M')
                        
            if end_time_str:
                end_datetime = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
                end_time = end_datetime.strftime('%H:%M')
        except Exception as e:
            print(f"解析时间时出错: {e}")
            
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': exam_date,
            '开始时间': start_time,
            '结束时间': end_time
        })
    
    df_subjects = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 2. 监考员设置表 - 最终修复版本
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            session_limit = proctor.get('session_limit')
            try:
                session_limit = int(session_limit) if session_limit is not None else 99
                if session_limit <= 0:
                    session_limit = 99
            except (ValueError, TypeError):
                session_limit = 99
            
            proctors_data.append({
                '序号': i,
                '监考老师': proctor.get('name', '').strip(),
                '任教科目': proctor.get('teaching_subject', ''),  # 如果没有则为空字符串
                '场次限制': session_limit
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 3. 考场设置表
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

def main():
    # 测试数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3, 'teaching_subject': '语文'},
            {'name': '李老师', 'session_limit': 2, 'teaching_subject': '数学'},
            {'name': '王老师', 'session_limit': 2, 'teaching_subject': ''}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2}}
        ]
    }

    test_file = 'final_test.xlsx'
    print("生成最终测试Excel文件...")
    generate_test_excel(wizard_data, test_file)

    # 验证结果
    print("\n=== 验证结果 ===")
    
    # 检查监考员设置表
    df_proctors = pd.read_excel(test_file, sheet_name='监考员设置')
    print("监考员设置表:")
    print("列名:", list(df_proctors.columns))
    print("数据:")
    print(df_proctors.to_string())
    
    # 检查必需列
    required_columns = ['序号', '监考老师', '任教科目', '场次限制']
    missing = [col for col in required_columns if col not in df_proctors.columns]
    
    if missing:
        print(f"\n❌ 缺少列: {missing}")
    else:
        print("\n✅ 包含所有必需列")
    
    # 检查数据完整性
    print("\n数据完整性检查:")
    all_valid = True
    for i, row in df_proctors.iterrows():
        issues = []
        if pd.isna(row['序号']):
            issues.append("序号为空")
        if pd.isna(row['监考老师']) or str(row['监考老师']).strip() == '':
            issues.append("监考老师为空")
        if pd.isna(row['场次限制']):
            issues.append("场次限制为空")
        elif not isinstance(row['场次限制'], (int, float)) or row['场次限制'] <= 0:
            issues.append("场次限制无效")
            
        if issues:
            print(f"  第{i+1}行问题: {', '.join(issues)}")
            all_valid = False
        else:
            print(f"  第{i+1}行: ✅ 数据完整")
    
    # 检查考试科目设置表
    df_subjects = pd.read_excel(test_file, sheet_name='考试科目设置')
    print(f"\n考试科目设置表列名: {list(df_subjects.columns)}")
    
    required_subject_columns = ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
    missing_subject = [col for col in required_subject_columns if col not in df_subjects.columns]
    
    if missing_subject:
        print(f"❌ 考试科目设置表缺少列: {missing_subject}")
        all_valid = False
    else:
        print("✅ 考试科目设置表包含所有必需列")

    print(f"\n{'='*50}")
    if all_valid:
        print("🎉 所有检查通过！修复成功！")
        print(f"生成的文件: {test_file}")
        print("\n现在引导页面生成的Excel文件应该能够通过验证器检查。")
    else:
        print("❌ 还有问题需要解决")

if __name__ == '__main__':
    main()
