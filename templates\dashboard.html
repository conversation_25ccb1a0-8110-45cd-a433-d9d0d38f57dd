{% extends "base.html" %}

{% block title %}{{ title }} - 均程通用监考安排{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container">
        <!-- 用户信息与任务统计合并模块 -->
        <div class="data-module">
            <div class="data-header">
                <h3 class="data-title">
                    <i class="fas fa-tachometer-alt me-2"></i>控制台概览
                </h3>
                <span class="data-total">{{ now.strftime('%Y/%m/%d %A') }}</span>
            </div>
            <div class="data-stats">
                <!-- 用户信息部分 -->
                <div class="stat-item">
                    <div class="stat-indicator" style="background-color: #0d6efd;"></div>
                    <span class="stat-count">{{ current_user.username }}</span>
                    <span class="stat-name">当前用户</span>
                </div>
                <div class="stat-item">
                    <div class="stat-indicator" style="background-color: #198754;"></div>
                    <span class="stat-count">
                        {% if current_user.role == 'admin' %}
                            管理员
                        {% elif current_user.role == 'vip' %}
                            VIP
                        {% else %}
                            普通
                        {% endif %}
                    </span>
                    <span class="stat-name">用户类型</span>
                </div>
                {% if current_user.role != 'admin' %}
                <div class="stat-item">
                    <div class="stat-indicator" style="background-color: #fd7e14;"></div>
                    <span class="stat-count">{{ remaining_tasks }}</span>
                    <span class="stat-name">剩余配额</span>
                </div>
                {% endif %}
                <!-- 任务统计部分 -->
                <div class="stat-item stat-pending">
                    <div class="stat-indicator"></div>
                    <span class="stat-count">{{ task_counts.pending }}</span>
                    <span class="stat-name">等待处理</span>
                </div>
                <div class="stat-item stat-processing">
                    <div class="stat-indicator"></div>
                    <span class="stat-count">{{ task_counts.processing }}</span>
                    <span class="stat-name">处理中</span>
                </div>
                <div class="stat-item stat-completed">
                    <div class="stat-indicator"></div>
                    <span class="stat-count">{{ task_counts.completed }}</span>
                    <span class="stat-name">已完成</span>
                </div>
                <div class="stat-item stat-failed">
                    <div class="stat-indicator"></div>
                    <span class="stat-count">{{ task_counts.failed }}</span>
                    <span class="stat-name">失败</span>
                </div>
                <div class="stat-item">
                    <div class="stat-indicator" style="background-color: #6c757d;"></div>
                    <span class="stat-count">{{ task_counts.total }}</span>
                    <span class="stat-name">总任务</span>
                </div>
            </div>
        </div>


        <!-- 监考安排创建方式 -->
        <div class="creation-methods">
            <!-- 方式一：页面引导生成 -->
            <div class="method-card method-primary">
                <div class="method-header">
                    <div class="method-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="method-info">
                        <h3 class="method-title">新建监考-智能向导配置-新手模式</h3>
                        <p class="method-subtitle">
                            <i class="fas fa-user-circle me-1 text-success"></i>新手友好 · 无需Excel文件 · 分步引导
                        </p>
                    </div>
                    <div class="method-badge recommended" 
                         data-bs-toggle="tooltip" 
                         data-bs-placement="left" 
                         data-bs-title="🎉 新手首选！无需Excel技能，智能向导帮您轻松完成监考安排">
                        推荐
                    </div>
                </div>
                <div class="method-body">
                    <div class="method-description mb-3">
                        <p class="text-muted small">
                            通过页面表单逐步配置监考信息，系统自动生成Excel设置文件并创建任务。
                            适合首次使用或不熟悉Excel模板格式的用户。
                        </p>
                    </div>
                    <div class="method-steps">
                        <div class="step-item">
                            <span class="step-number">1</span>
                            <span class="step-text">配置考试科目、时间安排</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">2</span>
                            <span class="step-text">设置考场及监考需求数量</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">3</span>
                            <span class="step-text">录入监考员信息和约束</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">4</span>
                            <span class="step-text">预览验证并创建任务</span>
                        </div>
                    </div>
                    <div class="method-advantages mb-3">
                        <div class="advantage-item">
                            <i class="fas fa-check text-success me-2"></i>
                            <span>无需学习Excel模板格式</span>
                        </div>
                        <div class="advantage-item">
                            <i class="fas fa-check text-success me-2"></i>
                            <span>实时数据验证和提示</span>
                        </div>
                    </div>
                    <div class="method-action">
                        <a href="{{ url_for('wizard_start') }}" class="btn btn-primary btn-lg"
                           {% if current_user.role != 'admin' and remaining_tasks == 0 %}
                           onclick="event.preventDefault(); showTaskLimitAlert();"
                           style="opacity: 0.6; cursor: not-allowed;"
                           {% endif %}>
                            <i class="fas fa-magic me-2"></i>开始向导配置
                        </a>
                    </div>
                </div>
            </div>

            <!-- 方式二：上传Excel设置文件 -->
            <div class="method-card method-secondary">
                <div class="method-header">
                    <div class="method-icon">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <div class="method-info">
                        <h3 class="method-title">新建监考-Excel文件上传-专家模式</h3>
                        <p class="method-subtitle">
                            <i class="fas fa-user-graduate me-1 text-info"></i>专业用户 · 需要Excel文件 · 功能强大
                        </p>
                    </div>
                    <div class="method-badge advanced">专业</div>
                </div>
                <div class="method-body">
                    <div class="method-description mb-3">
                        <p class="text-muted small">
                            使用标准Excel模板预先配置监考信息，支持复杂规则和批量处理。
                            适合熟悉Excel操作或有复杂配置需求的用户。
                        </p>
                    </div>
                    <div class="method-steps mb-3">
                        <div class="step-item">
                            <span class="step-number">1</span>
                            <span class="step-text">下载Excel模板文件</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">2</span>
                            <span class="step-text">按格式填写配置信息</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">3</span>
                            <span class="step-text">验证文件格式正确性</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">4</span>
                            <span class="step-text">上传文件创建任务</span>
                        </div>
                    </div>
                    <div class="method-features">
                        <div class="feature-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <span>支持复杂监考约束规则</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <span>配置文件可保存重复使用</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <span>批量处理大量数据</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <span>提供详细的格式验证</span>
                        </div>
                    </div>
                    <div class="method-actions">
                        <a href="{{ url_for('template_guide') }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-download me-1"></i>下载模板
                        </a>
                        <a href="{{ url_for('validate_test') }}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-check-double me-1"></i>验证文件
                        </a>
                        <a href="{{ url_for('new_task') }}" class="btn btn-success"
                           {% if current_user.role != 'admin' and remaining_tasks == 0 %}
                           onclick="event.preventDefault(); showTaskLimitAlert();"
                           style="opacity: 0.6; cursor: not-allowed;"
                           {% endif %}>
                            <i class="fas fa-upload me-1"></i>上传文件
                        </a>
                    </div>
                </div>
            </div>
        </div>



        <!-- 任务列表 -->
        <div class="tasks-section">
            <div class="section-header">
                <h2 class="section-title">我的任务</h2>
                {% if task_counts.total > 0 %}
                <div class="d-flex align-items-center gap-2">
                    <small class="text-muted">显示重要任务</small>
                    {% if task_counts.total > 8 %}
                    <a href="{{ url_for('tasks_list') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>查看全部 ({{ task_counts.total }})
                    </a>
                    {% elif other_tasks|length > 0 %}
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleAllTasks()">
                        <span id="toggleText">展开历史 ({{ other_tasks|length }})</span>
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>

            {% if priority_tasks or other_tasks %}
            <!-- 任务筛选 -->
            <div class="task-filters">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="pending">等待中 ({{ task_counts.pending }})</button>
                <button class="filter-btn" data-filter="processing">处理中 ({{ task_counts.processing }})</button>
                <button class="filter-btn" data-filter="completed">已完成 ({{ task_counts.completed }})</button>
                <button class="filter-btn" data-filter="failed">失败 ({{ task_counts.failed }})</button>
            </div>

            <!-- 优先显示的任务 -->
            <div id="priorityTasksList" class="tasks-grid">
                {% for task in priority_tasks %}
                <div class="task-card task-item compact-card" data-status="{{ task.status }}">
                    <div class="task-header">
                        <h4 class="task-title">{{ task.title }}</h4>
                        <div class="task-meta">
                            <span><i class="fas fa-calendar-alt me-1"></i>{{ task.created_at.strftime('%m-%d %H:%M') }}</span>
                            <span class="badge bg-{{ task.status_color }} text-white">{{ task.status_display }}</span>
                        </div>
                    </div>

                    <div class="task-body">
                        <!-- 进度条 -->
                        <div class="task-progress">
                            <div class="task-progress-bar {% if task.status == 'completed' %}completed{% elif task.status == 'failed' %}failed{% endif %}" 
                                 style="width: {% if task.status == 'processing' %}{{ task.progress }}%{% elif task.status == 'completed' %}100%{% elif task.status == 'failed' %}100%{% else %}0%{% endif %}">
                            </div>
                        </div>

                        {% if task.status == 'processing' %}
                        <small class="text-muted">进度: {{ task.progress }}%</small>
                        {% elif task.status == 'failed' and task.error_message %}
                        <small class="text-danger">{{ task.error_message|truncate(25) }}</small>
                        {% endif %}
                    </div>

                    <div class="task-actions">
                        <div class="action-group">
                            {% if task.status == 'pending' and (current_user.role == 'admin' or task.user_id == current_user.id) %}
                            <a href="{{ url_for('schedule_task', task_id=task.id) }}" 
                               class="btn btn-success btn-sm"
                               onclick="return confirm('确定要开始处理这个任务吗？');">
                                <i class="fas fa-play"></i>
                            </a>
                            {% endif %}
                            <a href="{{ url_for('task_detail', task_id=task.id) }}" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if task.status in ['completed', 'failed'] or current_user.role == 'admin' %}
                            <button onclick="confirmDeleteTask({{ task.id }})" 
                                    class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- 其他任务（默认隐藏） -->
            {% if other_tasks|length > 0 %}
            <div id="otherTasksSection" class="mt-4" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="text-muted mb-0">
                        <i class="fas fa-archive me-2"></i>历史任务 ({{ other_tasks|length }})
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleAllTasks()">
                        收起
                    </button>
                </div>
                <div id="otherTasksList" class="tasks-grid">
                    {% for task in other_tasks %}
                    <div class="task-card task-item compact-card" data-status="{{ task.status }}">
                        <div class="task-header">
                            <h4 class="task-title">{{ task.title }}</h4>
                            <div class="task-meta">
                                <span><i class="fas fa-calendar-alt me-1"></i>{{ task.created_at.strftime('%m-%d %H:%M') }}</span>
                                <span class="badge bg-{{ task.status_color }} text-white">{{ task.status_display }}</span>
                            </div>
                        </div>

                        <div class="task-body">
                            <!-- 进度条 -->
                            <div class="task-progress">
                                <div class="task-progress-bar {% if task.status == 'completed' %}completed{% elif task.status == 'failed' %}failed{% endif %}" 
                                     style="width: {% if task.status == 'processing' %}{{ task.progress }}%{% elif task.status == 'completed' %}100%{% elif task.status == 'failed' %}100%{% else %}0%{% endif %}">
                                </div>
                            </div>

                            {% if task.status == 'processing' %}
                            <small class="text-muted">进度: {{ task.progress }}%</small>
                            {% elif task.status == 'failed' and task.error_message %}
                            <small class="text-danger">{{ task.error_message|truncate(25) }}</small>
                            {% endif %}
                        </div>

                        <div class="task-actions">
                            <div class="action-group">
                                <a href="{{ url_for('task_detail', task_id=task.id) }}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if task.status in ['completed', 'failed'] or current_user.role == 'admin' %}
                                <button onclick="confirmDeleteTask({{ task.id }})" 
                                        class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h4 class="text-muted mb-3">开始您的第一个监考安排</h4>
                <p class="text-muted mb-4">使用智能向导，几分钟内即可完成监考安排配置</p>
        <a href="{{ url_for('wizard_start') }}" class="btn btn-primary">
                    <i class="fas fa-magic me-2"></i>开始使用向导
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>删除任务后将释放任务配额，您可以创建新的任务。</p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle me-2"></i>任务相关的所有数据都将被永久删除，此操作无法恢复！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 为dashboard页面添加特殊class
document.body.classList.add('dashboard-page');

// 显示任务限制提醒
function showTaskLimitAlert() {
    alert('您已达到任务数量限制！\n\n当前任务数：{{ task_counts.total }}\n配额上限：{{ current_user.task_limit }}\n\n请删除已完成或失败的任务后再创建新任务。');
}

// 切换显示所有任务
function toggleAllTasks() {
    const otherTasksSection = document.getElementById('otherTasksSection');
    const toggleText = document.getElementById('toggleText');
    
    if (otherTasksSection.style.display === 'none') {
        otherTasksSection.style.display = 'block';
        toggleText.textContent = '收起历史任务';
        // 平滑滚动到历史任务区域
        setTimeout(() => {
            otherTasksSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 100);
    } else {
        otherTasksSection.style.display = 'none';
        toggleText.textContent = '展开历史 ({{ other_tasks|length }})';
    }
}

    document.addEventListener('DOMContentLoaded', function() {
    // 推荐标签首次访问特效
    const recommendedBadge = document.querySelector('.method-badge.recommended');
    if (recommendedBadge && !localStorage.getItem('dashboard_visited')) {
        recommendedBadge.classList.add('first-visit');
        localStorage.setItem('dashboard_visited', 'true');
        
        // 4秒后移除特效类
        setTimeout(() => {
            recommendedBadge.classList.remove('first-visit');
        }, 4000);
    }
    
    // 任务筛选功能
        const filterButtons = document.querySelectorAll('.filter-btn');
        const taskItems = document.querySelectorAll('.task-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                taskItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-status') === filter) {
                        item.style.display = 'block';
                    item.style.opacity = '0';
                    setTimeout(() => {
                        item.style.opacity = '1';
                    }, 30);
                    } else {
                    item.style.opacity = '0';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 200);
                    }
            });
        });
    });

    // 删除任务功能
    let deleteTaskModal;
    let taskIdToDelete;

    deleteTaskModal = new bootstrap.Modal(document.getElementById('deleteTaskModal'));

    window.confirmDeleteTask = function(taskId) {
        taskIdToDelete = taskId;
        deleteTaskModal.show();
    };

    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!taskIdToDelete) return;
        
        fetch(`/task/${taskIdToDelete}/delete`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '删除失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        })
        .finally(() => {
            deleteTaskModal.hide();
            taskIdToDelete = null;
        });
    });

    // 动画效果
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -20px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // 为各种元素添加进入动画
    document.querySelectorAll('.method-card, .data-module, .task-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(10px)';
        el.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
        observer.observe(el);
    });

    // 防止水平滚动的检查
    function checkHorizontalOverflow() {
        const body = document.body;
        const html = document.documentElement;
        
        if (body.scrollWidth > body.clientWidth || html.scrollWidth > html.clientWidth) {
            console.warn('检测到水平滚动');
        }
    }

    // 页面加载完成后检查
    setTimeout(checkHorizontalOverflow, 800);
    
    // 窗口大小改变时检查
    window.addEventListener('resize', checkHorizontalOverflow);
    });
</script>
{% endblock %}
