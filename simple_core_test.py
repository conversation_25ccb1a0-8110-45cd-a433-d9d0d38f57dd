#!/usr/bin/env python3
"""
简单的core程序兼容性测试
"""

import sys
import os
import pandas as pd

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_classes():
    """测试数据类的兼容性"""
    try:
        # 导入core模块的数据类
        from core.data_structures import Teacher, Subject, Room
        
        test_file = 'test_core_compatible.xlsx'
        if not os.path.exists(test_file):
            print(f"测试文件 {test_file} 不存在")
            return False
        
        print(f"测试数据类兼容性: {test_file}")
        
        # 读取Excel文件
        teacher_df = pd.read_excel(test_file, sheet_name='监考员设置')
        subject_df = pd.read_excel(test_file, sheet_name='考试科目设置')
        room_df = pd.read_excel(test_file, sheet_name='考场设置')
        
        print("✅ 成功读取Excel文件")
        
        # 测试Teacher.from_excel_row
        print("\n测试Teacher.from_excel_row:")
        teachers = []
        for _, row in teacher_df.iterrows():
            try:
                teacher = Teacher.from_excel_row(row)
                teachers.append(teacher)
                print(f"  ✅ {teacher.name}: 场次限制={teacher.max_sessions}")
            except Exception as e:
                print(f"  ❌ 创建教师失败: {e}")
                return False
        
        # 测试Subject.from_excel_row
        print("\n测试Subject.from_excel_row:")
        subjects = []
        for _, row in subject_df.iterrows():
            try:
                subject = Subject.from_excel_row(row)
                subjects.append(subject)
                print(f"  ✅ {subject.name} ({subject.code}): {subject.start_time} - {subject.end_time}")
            except Exception as e:
                print(f"  ❌ 创建科目失败: {e}")
                return False
        
        # 测试Room.from_excel_row
        print("\n测试Room.from_excel_row:")
        rooms = []
        for _, row in room_df.iterrows():
            try:
                room = Room.from_excel_row(row, [s.name for s in subjects])
                rooms.append(room)
                print(f"  ✅ {room.name}: {room.subject_requirements}")
            except Exception as e:
                print(f"  ❌ 创建考场失败: {e}")
                return False
        
        print(f"\n🎉 所有数据类测试通过！")
        print(f"  - 成功创建 {len(teachers)} 个教师")
        print(f"  - 成功创建 {len(subjects)} 个科目")
        print(f"  - 成功创建 {len(rooms)} 个考场")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入core模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== 简单core程序兼容性测试 ===\n")
    
    success = test_data_classes()
    
    print("\n" + "="*50)
    
    if success:
        print("🎉 测试成功！引导页面生成的文件与core程序数据结构完全兼容！")
        print("\n这意味着:")
        print("  ✅ 文件格式正确")
        print("  ✅ 列名匹配")
        print("  ✅ 数据类型正确")
        print("  ✅ 可以被core程序正常处理")
    else:
        print("❌ 测试失败，需要进一步调整")

if __name__ == '__main__':
    main()
