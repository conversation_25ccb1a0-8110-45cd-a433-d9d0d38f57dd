{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/flatpickr.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dataTables.bootstrap5.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.bootstrap5.min.css') }}">
<style>
    /* 进度条样式优化 */
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, #e9ecef 50%, #0d6efd 50%);
        background-size: 200% 100%;
        background-position: right bottom;
        transform: translateY(-50%);
        z-index: 1;
        transition: all 0.3s ease;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
        transform: scale(1.05);
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #fff;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 1rem 1rem 0 0;
        padding: 1.25rem;
    }
    .modal-body {
        padding: 1.5rem;
    }
    .modal-footer {
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 1rem 1rem;
        padding: 1.25rem;
    }
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    .form-control {
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13,110,253,0.25);
    }

    /* 数据表格工具栏样式 */
    .dataTables_wrapper .row:first-child {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-bottom: 0.75rem;
        align-items: center;
    }
    .dataTables_filter input {
        padding: 0.25rem 0.5rem !important;
        height: auto !important;
        font-size: 0.875rem !important;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }
    
    .dataTables_length select {
        padding: 0.25rem 2rem 0.25rem 0.5rem !important;
        height: auto !important;
        font-size: 0.875rem !important;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        width: 80px !important;
        min-width: 80px !important;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .wizard-step-label {
            font-size: 0.8rem;
        }
        .table thead th {
            font-size: 0.75rem;
            padding: 0.75rem;
        }
        .table tbody td {
            padding: 0.75rem;
        }
        .action-buttons {
            display: flex;
            flex-direction: column;
        }
        .action-buttons .btn + .btn {
            margin-left: 0;
            margin-top: 0.5rem;
        }
    }

    /* 响应式表格优化 */
    .table-responsive {
        margin: 0;
        padding: 0;
        border: none;
        overflow-x: auto;
    }
    
    /* 表格样式优化 */
    .table {
        width: 100% !important;
        margin-bottom: 0;
    }
    
    /* 优化表格行高和内边距 */
    .table > tbody > tr > td {
        padding: 0.25rem 0.5rem !important;  /* 进一步减小单元格内边距 */
        vertical-align: middle !important;    /* 垂直居中对齐 */
        line-height: 1.1 !important;         /* 进一步减小行高 */
        height: 32px !important;             /* 进一步减小固定行高 */
        white-space: nowrap;                 /* 防止文本换行 */
        font-size: 0.9rem !important;       /* 略微减小字体大小 */
    }

    /* 表格头部样式优化 */
    .table > thead > tr > th {
        padding: 0.5rem !important;
        background-color: #f8f9fa !important;
        font-weight: 600 !important;
        border-bottom: 2px solid #dee2e6 !important;
        white-space: nowrap;
        font-size: 0.9rem !important;
        height: 36px !important;
    }

    /* 表格行样式优化 */
    .table > tbody > tr {
        transition: background-color 0.2s ease;
    }

    .table > tbody > tr:hover {
        background-color: rgba(13, 110, 253, 0.05) !important;
    }

    /* 操作按钮列样式优化 */
    .table .action-column {
        width: 120px !important;
        text-align: center !important;
    }

    .table .btn-group {
        display: inline-flex !important;
        gap: 0.25rem !important;
    }

    /* 紧凑型按钮样式 */
    .table .btn-sm {
        padding: 0.15rem 0.4rem !important;
        font-size: 0.8rem !important;
        line-height: 1.2 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-book-open me-2"></i>
                        向导第一步：考试科目设置
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <i class="fas fa-info-circle fa-2x me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-2">使用提示</h5>
                                <ul class="mb-0 ps-3">
                                    <li>课程代码输入时会自动提示已有科目</li>
                                    <li>可以通过Excel批量导入科目信息</li>
                                    <li>时间选择支持日历和时间选择器，格式为：YYYY/MM/DD HH:MM</li>
                                    <li>可以点击表格标题排序</li>
                                    <li>使用搜索框快速查找科目</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step1_subjects') }}" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="subjects_data" name="subjects_data">
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <button type="button" id="import-excel-btn" class="btn btn-outline-primary">
                                    <i class="fas fa-file-excel me-2"></i>从Excel导入
                                </button>
                                <input type="file" id="excel-file" accept=".xlsx,.xls" style="display: none;">
                                <a href="{{ url_for('wizard_download_template', template_type='subjects') }}" id="download-template-btn" class="btn btn-outline-secondary">
                                    <i class="fas fa-download me-2"></i>下载导入模板
                                </a>
                                <div class="flex-grow-1"></div>
                                <button type="button" id="add-subject-btn" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>添加科目
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table id="subjects-table" class="table table-striped table-hover" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>课程代码</th>
                                        <th>课程名称</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 数据将通过JavaScript动态添加 -->
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between py-3">
                    <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-2"></i>返回
                    </button>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步<i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑科目的模态框 -->
<div class="modal fade" id="subjectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    <span id="modal-title-text">添加科目</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="subject-form">
                    <div class="mb-3">
                        <label class="form-label">课程代码</label>
                        <input type="text" class="form-control" name="subject_code" required>
                        <div class="subject-code-suggestions"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">课程名称</label>
                        <input type="text" class="form-control" name="subject_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">开始时间 <small class="text-muted">(格式: YYYY/MM/DD HH:MM)</small></label>
                        <input type="text" class="form-control datetimepicker" name="start_time" required placeholder="2025/02/08 09:00">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">结束时间 <small class="text-muted">(格式: YYYY/MM/DD HH:MM)</small></label>
                        <input type="text" class="form-control datetimepicker" name="end_time" required placeholder="2025/02/08 11:00">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="save-subject-btn">
                    <i class="fas fa-save me-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/flatpickr.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/zh.js') }}"></script>
<script src="{{ url_for('static', filename='js/xlsx.full.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/jquery.dataTables.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/dataTables.bootstrap5.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/dataTables.responsive.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/responsive.bootstrap5.min.js') }}"></script>
<script>
$(document).ready(function() {
    // 安全地获取wizard_data
    const wizardData = {{ wizard_data | default({}) | tojson | safe }};
    console.log('Wizard data from server:', wizardData);
    
    let subjects = Array.isArray(wizardData.subjects) ? wizardData.subjects : [];
    console.log('Initial subjects array:', subjects);
    
    // 如果没有科目数据，添加初始科目
    if (subjects.length === 0) {
        console.log('No subjects found, adding default subjects');
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        
        subjects = [
            {
                subject_code: 'A',
                subject_name: '语文',
                start_time: `${year}/${month}/${day} 08:30`,
                end_time: `${year}/${month}/${day} 10:30`
            },
            {
                subject_code: 'B',
                subject_name: '数学',
                start_time: `${year}/${month}/${day} 14:00`,
                end_time: `${year}/${month}/${day} 16:00`
            },
            {
                subject_code: 'C',
                subject_name: '英语',
                start_time: `${year}/${month}/${day} 16:30`,
                end_time: `${year}/${month}/${day} 18:30`
            }
        ];
        console.log('Added default subjects:', subjects);
    }

    // 转换旧格式数据到新格式
    subjects = subjects.map(function(subject) {
        // 如果已经是新格式，直接返回
        if (subject.start_time && subject.start_time.includes('/') && subject.start_time.includes(':')) {
            return subject;
        }
        
        // 转换旧格式到新格式
        const examDate = subject.exam_date || new Date().toISOString().split('T')[0];
        const startTime = subject.start_time || '09:00';
        const endTime = subject.end_time || '11:00';
        
        // 转换日期格式从 YYYY-MM-DD 到 YYYY/MM/DD
        const formattedDate = examDate.replace(/-/g, '/');
        
        return {
            subject_code: subject.subject_code,
            subject_name: subject.subject_name,
            start_time: `${formattedDate} ${startTime}`,
            end_time: `${formattedDate} ${endTime}`
        };
    });

    // 更新隐藏字段中的数据
    function updateSubjectsData() {
        $('#subjects_data').val(JSON.stringify(subjects));
        console.log('Updated subjects data in hidden field:', subjects);
    }

    // 在页面加载时就更新隐藏字段中的数据
    updateSubjectsData();

    // 格式化显示时间（用于表格显示）
    function formatDisplayTime(timeStr) {
        if (!timeStr) return '';
        try {
            const date = new Date(timeStr.replace(/\//g, '-'));
            if (isNaN(date.getTime())) return timeStr;
            
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            
            return `${year}/${month}/${day} ${hours}:${minutes}`;
        } catch (e) {
            return timeStr;
        }
    }

    // 初始化DataTable
    console.log('Initializing DataTable with subjects:', subjects);
    const table = $('#subjects-table').DataTable({
        data: subjects,
        columns: [
            { data: 'subject_code', title: '课程代码' },
            { data: 'subject_name', title: '课程名称' },
            { 
                data: 'start_time',
                title: '开始时间',
                render: function(data, type, row) {
                    return formatDisplayTime(data);
                }
            },
            { 
                data: 'end_time',
                title: '结束时间',
                render: function(data, type, row) {
                    return formatDisplayTime(data);
                }
            },
            {
                data: null,
                title: '操作',
                orderable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-primary edit-btn" data-id="${row.subject_code}">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger delete-btn" data-id="${row.subject_code}">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    `;
                }
            }
        ],
        language: {
            processing: "处理中...",
            lengthMenu: "显示 _MENU_ 条",
            zeroRecords: "没有找到匹配的记录",
            info: "显示第 _START_ 至 _END_ 项记录，共 _TOTAL_ 项",
            infoEmpty: "显示第 0 至 0 项记录，共 0 项",
            infoFiltered: "(由 _MAX_ 项记录过滤)",
            infoPostFix: "",
            search: "搜索:",
            url: "",
            emptyTable: "表中数据为空",
            loadingRecords: "载入中...",
            infoThousands: ",",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            },
            aria: {
                sortAscending: ": 以升序排列此列",
                sortDescending: ": 以降序排列此列"
            }
        },
        responsive: true,
        order: [[0, 'asc']],
        drawCallback: function() {
            subjects = table.data().toArray();
            updateSubjectsData();
            console.log('表格重绘完成，当前科目数据：', subjects);
        }
    });

    // 初始化日期时间选择器
    function initDateTimePickers() {
        flatpickr('.datetimepicker', {
            locale: 'zh',
            enableTime: true,
            dateFormat: 'Y/m/d H:i',
            time_24hr: true,
            minuteIncrement: 5,
            minDate: 'today',
            disableMobile: true,
            defaultDate: new Date()
        });
    }

    // 在模态框显示时初始化日期时间选择器
    $('#subjectModal').on('shown.bs.modal', function() {
        initDateTimePickers();
    });

    // 添加科目按钮点击事件
    $('#add-subject-btn').click(function() {
        $('#subject-form')[0].reset();
        $('#modal-title-text').text('添加科目');
        $('#subjectModal').modal('show');
    });

    // 编辑科目
    $('#subjects-table').on('click', '.edit-btn', function(e) {
        e.preventDefault();
        const data = table.row($(this).closest('tr')).data();
        if (!data) return;
        
        const form = $('#subject-form');
        form.find('[name=subject_code]').val(data.subject_code || '');
        form.find('[name=subject_name]').val(data.subject_name || '');
        form.find('[name=start_time]').val(data.start_time || '');
        form.find('[name=end_time]').val(data.end_time || '');
        
        $('#modal-title-text').text('编辑科目');
        $('#subjectModal').modal('show');
    });

    // 删除科目
    $('#subjects-table').on('click', '.delete-btn', function() {
        const row = $(this).closest('tr');
        const data = table.row(row).data();
        
        if (confirm(`确定要删除科目"${data.subject_name}"吗？`)) {
            table.row(row).remove().draw();
            
            // 显示提示消息
            const alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                .html(`
                    科目已删除
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `);
            $('.card-body').prepend(alert);
            
            // 3秒后自动关闭提示
            setTimeout(() => {
                alert.alert('close');
            }, 3000);
        }
    });

    // 验证日期时间格式
    function validateDateTime(dateTimeStr) {
        if (!dateTimeStr) return false;
        
        // 支持的格式: YYYY/MM/DD HH:MM 或 YYYY-MM-DD HH:MM
        const regex = /^(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2}) (\d{1,2}):(\d{2})$/;
        const match = dateTimeStr.match(regex);
        
        if (!match) return false;
        
        const [, year, month, day, hour, minute] = match;
        const date = new Date(year, month - 1, day, hour, minute);
        
        return date.getFullYear() == year &&
               date.getMonth() == month - 1 &&
               date.getDate() == day &&
               date.getHours() == hour &&
               date.getMinutes() == minute;
    }

    // 保存科目
    $('#save-subject-btn').click(function() {
        const form = $('#subject-form');
        
        // 表单验证
        if (!form[0].checkValidity()) {
            form[0].reportValidity();
            return;
        }
        
        const data = {
            subject_code: form.find('[name=subject_code]').val().trim(),
            subject_name: form.find('[name=subject_name]').val().trim(),
            start_time: form.find('[name=start_time]').val().trim(),
            end_time: form.find('[name=end_time]').val().trim()
        };

        // 验证日期时间格式
        if (!validateDateTime(data.start_time)) {
            alert('开始时间格式错误，请使用格式：YYYY/MM/DD HH:MM');
            return;
        }
        
        if (!validateDateTime(data.end_time)) {
            alert('结束时间格式错误，请使用格式：YYYY/MM/DD HH:MM');
            return;
        }

        // 验证时间逻辑
        const startDateTime = new Date(data.start_time.replace(/\//g, '-'));
        const endDateTime = new Date(data.end_time.replace(/\//g, '-'));
        
        if (endDateTime <= startDateTime) {
            alert('结束时间必须晚于开始时间');
            return;
        }

        // 检查是否是编辑模式
        const isEdit = $('#modal-title-text').text() === '编辑科目';
        if (isEdit) {
            // 编辑现有记录
            const rows = table.rows().data();
            for (let i = 0; i < rows.length; i++) {
                if (rows[i].subject_code === data.subject_code) {
                    table.row(i).data(data).draw();
                    break;
                }
            }
        } else {
            // 添加新记录
            table.row.add(data).draw();
        }
        
        $('#subjectModal').modal('hide');
        
        // 显示成功提示
        const message = isEdit ? '科目更新成功' : '科目添加成功';
        const alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
            .html(`
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `);
        $('.card-body').prepend(alert);
        
        setTimeout(() => {
            alert.alert('close');
        }, 3000);
    });

    // 下载模板
    $('#download-template-btn').click(function(e) {
        e.preventDefault();
        window.location.href = '{{ url_for("wizard_download_template", template_type="subjects") }}';
    });

    // Excel导入 - 使用动态创建文件输入的方法
    $('#import-excel-btn').click(function(e) {
        e.preventDefault();
        console.log('导入按钮被点击');

        try {
            // 创建动态文件输入元素
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.style.display = 'none';

            // 设置文件选择回调
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) {
                    console.log('没有选择文件');
                    return;
                }

                console.log('文件已选择:', file.name, file.size, 'bytes');

                // 执行原来的文件处理逻辑
                handleFileUpload(file);

                // 清理动态创建的元素
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };

            // 添加到DOM并触发点击
            document.body.appendChild(input);
            input.click();
            console.log('成功创建并触发动态文件输入');

        } catch (error) {
            console.error('创建文件输入时出错:', error);
            alert('创建文件输入时出错: ' + error.message);
        }
    });

    // 设置CSRF令牌
    const csrfToken = $('input[name=csrf_token]').val();
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrfToken);
            }
        }
    });

    // 文件处理函数
    function handleFileUpload(file) {
        console.log('开始处理文件:', file.name);

        // 检查文件类型
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        if (!allowedTypes.includes(file.type)) {
            alert('请上传Excel文件（.xls或.xlsx格式）');
            return;
        }

        // 检查文件大小
        if (file.size > 5 * 1024 * 1024) {  // 5MB
            alert('文件大小不能超过5MB');
            return;
        }

        // 显示加载提示
        const loadingAlert = $('<div class="alert alert-info alert-dismissible fade show" role="alert">')
            .html(`
                <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `);
        $('.card-body').prepend(loadingAlert);

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('csrf_token', csrfToken);  // 添加CSRF令牌

        // 发送到后端处理
        $.ajax({
            url: '{{ url_for("wizard_import_excel", step_type="subjects") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                loadingAlert.remove();

                if (response.success) {
                    // 清空现有数据
                    table.clear();

                    // 添加导入的数据
                    table.rows.add(response.data).draw();

                    // 更新隐藏字段
                    subjects = response.data;
                    updateSubjectsData();

                    // 显示成功提示
                    const alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                        .html(`
                            <i class="fas fa-check-circle me-2"></i>${response.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `);
                    $('.card-body').prepend(alert);

                    setTimeout(() => {
                        alert.alert('close');
                    }, 5000);
                } else {
                    // 显示错误提示
                    const alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">')
                        .html(`
                            <i class="fas fa-exclamation-circle me-2"></i>${response.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `);
                    $('.card-body').prepend(alert);

                    setTimeout(() => {
                        alert.alert('close');
                    }, 5000);
                }
            },
            error: function(xhr, status, error) {
                loadingAlert.remove();

                let errorMessage = '导入失败，请稍后重试';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                }

                const alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">')
                    .html(`
                        <i class="fas fa-exclamation-circle me-2"></i>${errorMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `);
                $('.card-body').prepend(alert);

                setTimeout(() => {
                    alert.alert('close');
                }, 5000);
            }
        });
    }

    // 表单提交前验证
    $('#wizard-form').submit(function(e) {
        const data = table.data().toArray();
        if (data.length === 0) {
            e.preventDefault();
            alert('请至少添加一个考试科目');
            return false;
        }
        subjects = data;
        updateSubjectsData();
    });

    // 初始化提示框
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %} 