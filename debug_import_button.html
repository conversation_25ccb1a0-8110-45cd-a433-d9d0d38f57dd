<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试Excel导入按钮</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>调试Excel导入按钮功能</h1>
    
    <div>
        <button type="button" id="import-excel-btn" class="btn btn-outline-primary">
            <i class="fas fa-file-excel me-2"></i>从Excel导入
        </button>
        <input type="file" id="excel-file" accept=".xlsx,.xls" class="d-none" style="display: none;">
    </div>
    
    <div id="debug-info" style="margin-top: 20px; padding: 10px; background-color: #f0f0f0;">
        <h3>调试信息:</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#log').append(`<div>[${timestamp}] ${message}</div>`);
            console.log(message);
        }

        $(document).ready(function() {
            log('页面加载完成');
            
            // 检查元素是否存在
            const importBtn = $('#import-excel-btn');
            const fileInput = $('#excel-file');
            
            log(`导入按钮存在: ${importBtn.length > 0}`);
            log(`文件输入存在: ${fileInput.length > 0}`);
            
            // 绑定点击事件
            $('#import-excel-btn').click(function() {
                log('导入按钮被点击');
                
                try {
                    $('#excel-file').click();
                    log('触发文件输入点击');
                } catch (error) {
                    log(`错误: ${error.message}`);
                }
            });
            
            // 监听文件选择
            $('#excel-file').change(function(e) {
                const file = e.target.files[0];
                if (file) {
                    log(`文件已选择: ${file.name} (${file.size} bytes)`);
                } else {
                    log('没有选择文件');
                }
            });
            
            // 测试直接点击文件输入
            $('#excel-file').click(function() {
                log('文件输入被直接点击');
            });
            
            log('事件绑定完成');
        });
    </script>
</body>
</html>
