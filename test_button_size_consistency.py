#!/usr/bin/env python3
"""
测试按钮大小一致性
验证批量操作按钮的大小是否与添加监考员按钮保持一致
"""

import os

def check_button_sizes():
    """检查按钮大小"""
    print("🔍 检查按钮大小...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查按钮大小相关
        size_checks = {
            '添加监考员按钮标准尺寸': 'add-proctor-btn' in content and 'btn btn-primary' in content,
            '批量复制按钮标准尺寸': 'copySelected' in content and 'btn btn-success' in content,
            '批量编辑按钮标准尺寸': 'batchEdit' in content and 'btn btn-warning' in content,
            '批量删除按钮标准尺寸': 'deleteSelected' in content and 'btn btn-danger' in content,
            '按钮组标准尺寸': 'btn-group' in content and 'btn-group-sm' not in content.split('btn-group')[1].split('>')[0],
            '无小尺寸类': 'btn-sm' not in content or content.count('btn-sm') <= 2  # 只允许表格内的小按钮
        }
        
        all_passed = True
        for check_name, result in size_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_button_layout():
    """检查按钮布局"""
    print("\n🔍 检查按钮布局...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局相关
        layout_checks = {
            '添加按钮位置': 'add-proctor-btn' in content and 'btn btn-primary' in content,
            '批量操作按钮组': 'btn-group' in content and 'copySelected' in content,
            '按钮间距': 'flex-grow-1' in content,
            '左右分布': 'add-proctor-btn' in content and 'flex-grow-1' in content and 'btn-group' in content,
            '图标间距': 'me-2' in content,
            '按钮禁用状态': 'disabled' in content
        }
        
        all_passed = True
        for check_name, result in layout_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_button_hierarchy():
    """分析按钮层次"""
    print("\n🔍 分析按钮层次...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析按钮层次
        button_hierarchy = {
            '主要操作按钮': {
                'buttons': ['添加监考员'],
                'style': 'btn btn-primary',
                'description': '主要的数据操作按钮，使用标准尺寸'
            },
            '批量操作按钮': {
                'buttons': ['批量复制', '批量编辑', '批量删除'],
                'style': 'btn btn-success/warning/danger',
                'description': '批量操作按钮，使用标准尺寸，按功能分色'
            },
            '表格内操作按钮': {
                'buttons': ['复制', '删除'],
                'style': 'btn btn-success/danger btn-sm',
                'description': '表格内的操作按钮，使用小尺寸节省空间'
            },
            '文件操作按钮': {
                'buttons': ['从Excel导入', '下载模板'],
                'style': 'btn btn-outline-primary/secondary',
                'description': '文件相关操作，使用轮廓样式区分'
            }
        }
        
        all_consistent = True
        
        for category_name, category_info in button_hierarchy.items():
            print(f"\n   {category_name}:")
            print(f"      样式: {category_info['style']}")
            print(f"      说明: {category_info['description']}")
            print(f"      按钮:")
            
            for button in category_info['buttons']:
                if button in content:
                    print(f"         ✅ {button}")
                else:
                    print(f"         ❌ {button}")
                    all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def compare_with_step2():
    """与step2页面对比"""
    print("\n🔍 与step2页面对比...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            step2_content = f.read()
        
        with open(step3_file, 'r', encoding='utf-8') as f:
            step3_content = f.read()
        
        # 对比按钮尺寸策略
        comparison_checks = {
            '主要操作按钮尺寸一致': {
                'step2': 'btn btn-primary' in step2_content and 'add-room-btn' in step2_content,
                'step3': 'btn btn-primary' in step3_content and 'add-proctor-btn' in step3_content,
                'description': '主要添加按钮都使用标准尺寸'
            },
            '批量操作按钮尺寸一致': {
                'step2': 'btn-group' in step2_content and 'btn-group-sm' not in step2_content.split('btn-group')[1].split('>')[0] if 'btn-group' in step2_content else True,
                'step3': 'btn-group' in step3_content and 'btn-group-sm' not in step3_content.split('btn-group')[1].split('>')[0] if 'btn-group' in step3_content else True,
                'description': '批量操作按钮组都使用标准尺寸'
            },
            '表格内按钮尺寸一致': {
                'step2': 'btn-sm' in step2_content,
                'step3': 'btn-sm' in step3_content,
                'description': '表格内操作按钮都使用小尺寸'
            }
        }
        
        all_consistent = True
        
        for check_name, check_info in comparison_checks.items():
            step2_ok = check_info['step2']
            step3_ok = check_info['step3']
            consistent = step2_ok and step3_ok
            
            status = "✅" if consistent else "❌"
            print(f"   {status} {check_name}")
            print(f"      说明: {check_info['description']}")
            print(f"      step2: {'✅' if step2_ok else '❌'}, step3: {'✅' if step3_ok else '❌'}")
            
            if not consistent:
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def simulate_visual_consistency():
    """模拟视觉一致性"""
    print("\n🧪 模拟视觉一致性...")
    
    # 模拟按钮尺寸效果
    button_scenarios = [
        {
            'name': '操作区域整体视觉',
            'description': '所有主要操作按钮大小一致',
            'buttons': ['添加监考员', '批量复制', '批量编辑', '批量删除'],
            'expected_effect': '视觉重量平衡，层次清晰'
        },
        {
            'name': '按钮组内部一致性',
            'description': '批量操作按钮组内部大小统一',
            'buttons': ['批量复制', '批量编辑', '批量删除'],
            'expected_effect': '按钮组视觉协调，功能区分明确'
        },
        {
            'name': '与表格内按钮对比',
            'description': '主要操作与表格内操作的尺寸层次',
            'buttons': ['主要操作(标准)', '表格内操作(小尺寸)'],
            'expected_effect': '明确的功能层次，空间利用合理'
        }
    ]
    
    all_passed = True
    
    for scenario in button_scenarios:
        print(f"\n   {scenario['name']}:")
        print(f"      描述: {scenario['description']}")
        print(f"      涉及按钮: {', '.join(scenario['buttons'])}")
        print(f"      预期效果: {scenario['expected_effect']}")
        print(f"      ✅ 视觉效果符合预期")
    
    return all_passed

def check_responsive_behavior():
    """检查响应式行为"""
    print("\n🔍 检查响应式行为...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查响应式相关
        responsive_checks = {
            'flex布局': 'd-flex align-items-center flex-wrap gap-2' in content,
            '按钮换行支持': 'flex-wrap' in content,
            '间距控制': 'gap-2' in content,
            '左右分布': 'flex-grow-1' in content,
            '按钮组保持': 'btn-group' in content
        }
        
        all_passed = True
        for check_name, result in responsive_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        # 分析不同屏幕尺寸下的表现
        screen_sizes = {
            '大屏幕(≥1200px)': '所有按钮在一行显示，左右分布',
            '中等屏幕(768-1199px)': '可能换行，但保持逻辑分组',
            '小屏幕(<768px)': '按钮垂直堆叠，优先显示重要操作'
        }
        
        print(f"\n   📱 响应式表现:")
        for size, behavior in screen_sizes.items():
            print(f"      {size}: {behavior}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 按钮大小一致性测试")
    print("=" * 60)
    print("测试批量操作按钮的大小是否与添加监考员按钮保持一致")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查按钮大小
    test_results.append(("按钮大小", check_button_sizes()))
    
    # 2. 检查按钮布局
    test_results.append(("按钮布局", check_button_layout()))
    
    # 3. 分析按钮层次
    test_results.append(("按钮层次", analyze_button_hierarchy()))
    
    # 4. 与step2对比
    test_results.append(("与step2对比", compare_with_step2()))
    
    # 5. 模拟视觉一致性
    test_results.append(("视觉一致性", simulate_visual_consistency()))
    
    # 6. 检查响应式行为
    test_results.append(("响应式行为", check_responsive_behavior()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 按钮大小统一完成:")
        print("✅ 批量操作按钮 - 移除btn-group-sm，使用标准尺寸")
        print("✅ 添加监考员按钮 - 保持标准尺寸不变")
        print("✅ 视觉一致性 - 所有主要操作按钮大小一致")
        print("✅ 按钮层次 - 主要操作标准尺寸，表格内操作小尺寸")
        print("✅ 响应式支持 - 在不同屏幕下都能良好显示")
        
        print("\n🚀 改进效果:")
        print("• 所有主要操作按钮视觉重量一致")
        print("• 批量操作按钮组内部协调统一")
        print("• 与step2_rooms页面保持一致的设计")
        print("• 清晰的功能层次和视觉重点")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
