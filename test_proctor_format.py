#!/usr/bin/env python3
"""
测试修复后的监考员设置表格式
"""

import pandas as pd
from datetime import datetime

def test_generate_excel_fixed(wizard_data, file_path):
    """测试修复后的Excel生成功能"""
    
    # 1. 考试科目设置表
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')
        
        exam_date = '2025/01/01'
        start_time = '09:00'
        end_time = '11:00'
        
        try:
            if start_time_str:
                start_datetime = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
                exam_date = start_datetime.strftime('%Y/%m/%d')
                start_time = start_datetime.strftime('%H:%M')
                        
            if end_time_str:
                end_datetime = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
                end_time = end_datetime.strftime('%H:%M')
        except Exception as e:
            print(f"解析时间时出错: {e}")
            
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': exam_date,
            '开始时间': start_time,
            '结束时间': end_time
        })
    
    df_subjects = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 2. 监考员设置表 - 修复后的格式
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            session_limit = proctor.get('session_limit')
            try:
                session_limit = int(session_limit) if session_limit is not None else 99
                if session_limit <= 0:
                    session_limit = 99
            except (ValueError, TypeError):
                session_limit = 99
            
            proctors_data.append({
                '序号': i,                                    # 验证器要求的序号列
                '监考老师': proctor.get('name', '').strip(),
                '任教科目': '',                               # 验证器要求的任教科目列
                '场次限制': session_limit
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 3. 考场设置表
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

def main():
    # 测试数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3},
            {'name': '李老师', 'session_limit': 2},
            {'name': '王老师', 'session_limit': 2}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2}}
        ]
    }

    test_file = 'test_fixed_format.xlsx'
    print("生成修复后的Excel文件...")
    test_generate_excel_fixed(wizard_data, test_file)

    # 验证监考员设置表
    df_proctors = pd.read_excel(test_file, sheet_name='监考员设置')
    print("\n=== 监考员设置表 ===")
    print("列名:", list(df_proctors.columns))
    print("数据:")
    print(df_proctors.to_string())

    # 检查必需的列
    required_columns = ['序号', '监考老师', '任教科目', '场次限制']
    missing = [col for col in required_columns if col not in df_proctors.columns]

    if missing:
        print(f"\n❌ 监考员设置表缺少列: {missing}")
    else:
        print("\n✅ 监考员设置表包含所有必需的列")

    # 检查数据完整性
    print("\n数据完整性检查:")
    for i, row in df_proctors.iterrows():
        序号 = row['序号']
        监考老师 = row['监考老师']
        场次限制 = row['场次限制']
        
        issues = []
        if pd.isna(序号):
            issues.append("序号为空")
        if pd.isna(监考老师) or str(监考老师).strip() == '':
            issues.append("监考老师为空")
        if pd.isna(场次限制):
            issues.append("场次限制为空")
        elif not isinstance(场次限制, (int, float)) or 场次限制 <= 0:
            issues.append("场次限制无效")
            
        if issues:
            print(f"  第{i+1}行问题: {', '.join(issues)}")
        else:
            print(f"  第{i+1}行: ✅ 数据完整")

    # 验证考试科目设置表
    df_subjects = pd.read_excel(test_file, sheet_name='考试科目设置')
    print("\n=== 考试科目设置表 ===")
    print("列名:", list(df_subjects.columns))
    
    required_subject_columns = ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
    missing_subject = [col for col in required_subject_columns if col not in df_subjects.columns]
    
    if missing_subject:
        print(f"❌ 考试科目设置表缺少列: {missing_subject}")
    else:
        print("✅ 考试科目设置表包含所有必需的列")

    print(f"\n✅ 测试完成，生成文件: {test_file}")

if __name__ == '__main__':
    main()
