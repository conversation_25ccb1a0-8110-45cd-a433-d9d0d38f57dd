# 批量编辑和批量删除考场功能

## 🎯 功能概述

为第二步考场页面添加了批量编辑监考人数和批量删除考场功能，用户可以通过复选框选择多个考场，然后进行批量操作，大大提高了考场管理的效率。

## ✨ 功能特点

### 1. 智能选择系统
- **全选功能**：表头复选框可一键选择/取消所有考场
- **单选功能**：每行复选框可单独选择考场
- **状态同步**：全选复选框根据选择情况显示不同状态
- **按钮联动**：批量操作按钮根据选择状态自动启用/禁用

### 2. 批量编辑监考人数
- **选择性编辑**：只编辑需要修改的科目，其他保持不变
- **批量应用**：一次设置应用到所有选中的考场
- **实时预览**：显示选中的考场数量
- **智能验证**：确保至少修改一个科目才能提交

### 3. 批量删除考场
- **安全确认**：删除前显示确认对话框
- **批量处理**：一次删除多个选中的考场
- **索引安全**：从后往前删除，避免索引错乱
- **即时反馈**：显示删除的考场数量

## 🔧 界面设计

### 表格头部复选框
```html
<th style="width: 5%;">
    <input type="checkbox" id="select-all-rooms" class="form-check-input">
</th>
```

### 表格行复选框
```html
<td style="vertical-align: middle;">
    <input type="checkbox" class="form-check-input room-checkbox">
</td>
```

### 批量操作按钮
```html
<button type="button" id="batch-edit-room-btn" class="btn btn-warning" disabled>
    <i class="fas fa-edit me-2"></i>批量编辑
</button>
<button type="button" id="batch-delete-room-btn" class="btn btn-danger" disabled>
    <i class="fas fa-trash-alt me-2"></i>批量删除
</button>
```

### 批量编辑模态框
- **选择数量显示**：实时显示已选择的考场数量
- **科目编辑区域**：为每个科目提供监考人数输入框
- **留空保持**：未填写的科目保持原有设置不变
- **确认操作**：提供确认和取消按钮

## 📋 使用流程

### 批量编辑监考人数

#### 第一步：选择考场
1. 使用表头复选框全选所有考场，或
2. 使用行复选框单独选择需要编辑的考场
3. 批量编辑按钮自动启用

#### 第二步：设置监考人数
1. 点击"批量编辑"按钮
2. 在弹出的模态框中查看选中的考场数量
3. 为需要修改的科目设置新的监考人数
4. 留空的科目将保持原有设置不变

#### 第三步：确认编辑
1. 检查设置是否正确
2. 点击"确认编辑"按钮
3. 系统批量更新所有选中考场的监考人数
4. 显示成功编辑的考场数量和科目

### 批量删除考场

#### 第一步：选择考场
1. 使用复选框选择要删除的考场
2. 可以选择一个或多个考场
3. 批量删除按钮自动启用

#### 第二步：确认删除
1. 点击"批量删除"按钮
2. 系统显示确认对话框，包含删除数量
3. 确认后执行批量删除操作

#### 第三步：完成删除
1. 系统从后往前删除选中的考场
2. 重新渲染考场表格
3. 显示成功删除的考场数量

## 🎯 使用示例

### 示例1：批量编辑监考人数
**场景**：需要将所有考场的语文监考人数改为3人

**操作步骤**：
1. 点击表头复选框，全选所有考场
2. 点击"批量编辑"按钮
3. 在语文科目输入框中输入"3"
4. 其他科目留空（保持不变）
5. 点击"确认编辑"

**结果**：所有考场的语文监考人数更新为3人，其他科目保持原设置

### 示例2：批量删除考场
**场景**：需要删除1考场、3考场、5考场

**操作步骤**：
1. 分别勾选1考场、3考场、5考场的复选框
2. 点击"批量删除"按钮
3. 在确认对话框中点击"确定"

**结果**：选中的3个考场被删除，其他考场保持不变

### 示例3：部分选择编辑
**场景**：只需要修改A101、A102考场的数学和英语监考人数

**操作步骤**：
1. 勾选A101、A102考场的复选框
2. 点击"批量编辑"按钮
3. 在数学科目输入框中输入"1"
4. 在英语科目输入框中输入"2"
5. 语文科目留空
6. 点击"确认编辑"

**结果**：A101、A102考场的数学改为1人，英语改为2人，语文保持不变

## 🔧 技术实现

### 复选框状态管理
```javascript
// 全选/取消全选
$('#select-all-rooms').change(function() {
    var isChecked = $(this).is(':checked');
    $('.room-checkbox').prop('checked', isChecked);
    updateBatchButtonsState();
});

// 单个复选框变化
$(document).on('change', '.room-checkbox', function() {
    updateBatchButtonsState();
    
    // 更新全选复选框状态
    var totalCheckboxes = $('.room-checkbox').length;
    var checkedCheckboxes = $('.room-checkbox:checked').length;
    
    if (checkedCheckboxes === 0) {
        $('#select-all-rooms').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all-rooms').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all-rooms').prop('indeterminate', true);
    }
});
```

### 批量编辑逻辑
```javascript
$('#confirm-batch-edit').click(function() {
    // 收集编辑的监考人数
    var editDemands = {};
    $('.batch-edit-demand').each(function() {
        var subject = $(this).data('subject');
        var value = $(this).val().trim();
        if (value !== '') {
            editDemands[subject] = parseInt(value) || 0;
        }
    });
    
    // 获取选中的行索引
    var selectedIndices = [];
    $('.room-checkbox:checked').each(function() {
        var rowIndex = $(this).closest('tr').index();
        selectedIndices.push(rowIndex);
    });
    
    // 批量更新选中考场的监考人数
    selectedIndices.forEach(function(index) {
        Object.keys(editDemands).forEach(function(subject) {
            rooms[index].demands[subject] = editDemands[subject];
        });
    });
});
```

### 批量删除逻辑
```javascript
$('#batch-delete-room-btn').click(function() {
    var selectedCount = $('.room-checkbox:checked').length;
    
    if (confirm(`确定要删除选中的 ${selectedCount} 个考场吗？`)) {
        // 获取选中的行索引
        var selectedIndices = [];
        $('.room-checkbox:checked').each(function() {
            var rowIndex = $(this).closest('tr').index();
            selectedIndices.push(rowIndex);
        });
        
        // 从后往前删除（避免索引变化）
        selectedIndices.sort(function(a, b) { return b - a; });
        selectedIndices.forEach(function(index) {
            rooms.splice(index, 1);
        });
        
        renderRooms();
    }
});
```

## ✅ 功能优势

### 1. 效率提升
- **批量操作**：一次处理多个考场，避免重复操作
- **智能选择**：全选功能快速选择所有考场
- **选择性编辑**：只修改需要的科目，其他保持不变

### 2. 用户体验
- **直观操作**：复选框选择方式简单明了
- **状态反馈**：按钮状态和复选框状态实时同步
- **安全确认**：删除操作有确认对话框防止误操作

### 3. 数据安全
- **索引安全**：删除时从后往前处理，避免索引错乱
- **操作确认**：重要操作都有确认步骤
- **即时反馈**：操作完成后立即显示结果

## 🔍 错误处理

### 1. 选择验证
- **无选择提醒**：未选择考场时提示用户先选择
- **空编辑检查**：批量编辑时检查是否至少修改一个科目
- **数量显示**：实时显示选中的考场数量

### 2. 操作安全
- **删除确认**：删除前显示确认对话框
- **数据验证**：监考人数输入验证（非负整数）
- **异常处理**：操作失败时的错误提示

### 3. 界面反馈
- **成功提示**：操作成功后显示详细信息
- **自动关闭**：提示信息5秒后自动关闭
- **状态重置**：操作完成后重置选择状态

## 🚀 使用建议

### 1. 批量编辑策略
- **分类编辑**：按科目特点分批编辑监考人数
- **保持一致**：同类型考场使用相同的监考人数设置
- **及时保存**：编辑完成后及时进入下一步保存

### 2. 批量删除注意事项
- **谨慎删除**：删除操作不可撤销，请仔细确认
- **分批删除**：大量删除时可分批进行
- **备份数据**：重要数据删除前建议先备份

### 3. 选择技巧
- **全选快捷**：需要编辑大部分考场时使用全选
- **排除选择**：全选后取消不需要的考场
- **分组操作**：按楼层或区域分组进行批量操作

## 📝 总结

批量编辑和批量删除考场功能显著提升了考场管理的效率和便利性。通过智能的选择系统、安全的操作确认和友好的用户界面，用户可以快速完成大量考场的编辑和删除工作，为监考安排的后续步骤提供了强有力的支持。

**核心价值**:
- ✅ **高效批量** - 一次操作处理多个考场
- ✅ **智能选择** - 灵活的复选框选择系统
- ✅ **安全操作** - 完善的确认和验证机制
- ✅ **用户友好** - 直观的界面和即时反馈
