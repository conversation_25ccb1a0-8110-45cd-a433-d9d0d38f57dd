#!/usr/bin/env python3
"""
创建测试用的监考员Excel文件，包含多种分隔符
"""

import pandas as pd

def create_test_excel_with_separators():
    """创建包含多种分隔符的测试Excel文件"""
    
    # 创建测试数据，使用不同的分隔符
    test_data = [
        {
            '监考老师': '张三',
            '任教科目': '语文',
            '必监考科目': '语文,数学',  # 逗号分隔
            '不监考科目': '英语。物理',  # 中文句号分隔
            '必监考考场': '1考场;2考场',  # 分号分隔
            '不监考考场': '10考场|11考场',  # 竖线分隔
            '场次限制': 5
        },
        {
            '监考老师': '李四',
            '任教科目': '数学',
            '必监考科目': '数学 物理 化学',  # 空格分隔
            '不监考科目': '语文/化学',  # 斜杠分隔
            '必监考考场': '3考场-4考场-5考场',  # 连字符分隔
            '不监考考场': '12考场_13考场',  # 下划线分隔
            '场次限制': 3
        },
        {
            '监考老师': '王五',
            '任教科目': '英语',
            '必监考科目': '英语(历史)地理',  # 括号分隔
            '不监考科目': '数学*生物&历史',  # 特殊符号分隔
            '必监考考场': '6考场[7考场]8考场',  # 方括号分隔
            '不监考考场': '14考场{15考场}16考场',  # 花括号分隔
            '场次限制': 4
        },
        {
            '监考老师': '赵六',
            '任教科目': '物理',
            '必监考科目': '物理，化学；生物',  # 混合中文标点
            '不监考科目': '语文+数学=英语',  # 数学符号分隔
            '必监考考场': '9考场<10考场>11考场',  # 尖括号分隔
            '不监考考场': '17考场\\18考场',  # 反斜杠分隔
            '场次限制': 6
        },
        {
            '监考老师': '钱七',
            '任教科目': '化学',
            '必监考科目': '化学^生物%地理$政治#历史@信息!技术~体育`美术?音乐',  # 多种特殊符号
            '不监考科目': '语文:数学:英语',  # 冒号分隔
            '必监考考场': '12考场\t13考场\n14考场',  # 制表符和换行符分隔
            '不监考考场': '19考场.20考场',  # 点号分隔
            '场次限制': 2
        },
        {
            '监考老师': '孙八',
            '任教科目': '生物',
            '必监考科目': '生物,,,,地理;;;;政治||||历史',  # 连续分隔符
            '不监考科目': ',语文,数学,英语,',  # 首尾分隔符
            '必监考考场': '高中 考场, 初中 考场 , 小学 考场',  # 包含空格的内容
            '不监考考场': '',  # 空字符串
            '场次限制': 7
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    output_file = 'test_proctors_separators.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ 测试Excel文件已创建: {output_file}")
    print(f"📊 包含 {len(test_data)} 条监考员记录")
    
    # 显示数据预览
    print("\n📋 数据预览:")
    for i, row in df.iterrows():
        print(f"  {i+1}. {row['监考老师']} - {row['任教科目']}")
        print(f"     必监考科目: {row['必监考科目']}")
        print(f"     不监考科目: {row['不监考科目']}")
        print(f"     必监考考场: {row['必监考考场']}")
        print(f"     不监考考场: {row['不监考考场']}")
        print()
    
    # 分析使用的分隔符
    print("📝 使用的分隔符类型:")
    separator_examples = {
        '逗号': '语文,数学',
        '中文句号': '英语。物理',
        '分号': '1考场;2考场',
        '竖线': '10考场|11考场',
        '空格': '数学 物理 化学',
        '斜杠': '语文/化学',
        '连字符': '3考场-4考场-5考场',
        '下划线': '12考场_13考场',
        '括号': '英语(历史)地理',
        '特殊符号': '数学*生物&历史',
        '方括号': '6考场[7考场]8考场',
        '花括号': '14考场{15考场}16考场',
        '混合标点': '物理，化学；生物',
        '数学符号': '语文+数学=英语',
        '尖括号': '9考场<10考场>11考场',
        '反斜杠': '17考场\\18考场',
        '多种特殊符号': '化学^生物%地理$政治#历史@信息!技术~体育`美术?音乐',
        '冒号': '语文:数学:英语',
        '制表符换行符': '12考场\t13考场\n14考场',
        '点号': '19考场.20考场',
        '连续分隔符': '生物,,,,地理;;;;政治||||历史',
        '首尾分隔符': ',语文,数学,英语,',
        '包含空格': '高中 考场, 初中 考场 , 小学 考场'
    }
    
    for sep_type, example in separator_examples.items():
        print(f"  • {sep_type}: {example}")
    
    return output_file

if __name__ == "__main__":
    create_test_excel_with_separators()
