<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试按钮问题</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>调试Excel导入按钮问题</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>测试按钮</h5>
            </div>
            <div class="card-body">
                <button type="button" id="import-excel-btn-subjects" class="btn btn-outline-primary">
                    <i class="fas fa-file-excel me-2"></i>从Excel导入
                </button>
                
                <div id="debug-info" class="mt-3">
                    <h6>调试信息:</h6>
                    <div id="log"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            $('#log').append(`<div>${logEntry}</div>`);
            console.log(logEntry);
        }

        $(document).ready(function() {
            log('页面加载完成');
            log(`jQuery版本: ${$.fn.jquery}`);
            
            // 检查按钮是否存在
            const btn = $('#import-excel-btn-subjects');
            log(`按钮元素数量: ${btn.length}`);
            
            if (btn.length > 0) {
                log('按钮元素存在');
                
                // 绑定点击事件
                btn.click(function(e) {
                    e.preventDefault();
                    log('按钮被点击！');
                    
                    try {
                        // 创建动态文件输入元素
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = '.xlsx,.xls';
                        input.style.display = 'none';
                        
                        log('文件输入元素已创建');
                        
                        // 设置文件选择回调
                        input.onchange = function(e) {
                            const file = e.target.files[0];
                            if (!file) {
                                log('没有选择文件');
                                return;
                            }
                            
                            log(`文件已选择: ${file.name} (${file.size} bytes)`);
                            
                            // 清理动态创建的元素
                            if (input.parentNode) {
                                input.parentNode.removeChild(input);
                                log('文件输入元素已清理');
                            }
                        };
                        
                        // 添加到DOM并触发点击
                        document.body.appendChild(input);
                        input.click();
                        log('文件输入点击已触发');
                        
                    } catch (error) {
                        log(`错误: ${error.message}`);
                    }
                });
                
                log('点击事件已绑定');
            } else {
                log('❌ 按钮元素不存在');
            }
        });
    </script>
</body>
</html>
