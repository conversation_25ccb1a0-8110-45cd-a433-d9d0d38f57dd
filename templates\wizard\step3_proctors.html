{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>


/* 表格行高度控制 */
#proctors-table tbody tr {
    height: 70px !important;
}

#proctors-table tbody td {
    vertical-align: middle !important;
    padding: 4px 6px !important;
}

/* 控制输入框高度 */
#proctors-table .form-control-sm,
#proctors-table .form-select-sm {
    height: 32px !important;
    font-size: 0.875em !important;
}

/* 序号徽章样式 */
.proctor-index {
    font-size: 0.75em !important;
    padding: 4px 6px !important;
}

/* 操作按钮组紧凑样式 */
.btn-group-sm .btn {
    padding: 2px 6px !important;
    font-size: 0.75em !important;
}

/* 科目选择器样式 */
.subject-selector {
    width: 100%;
}

.subject-edit-btn {
    height: 32px !important;
    font-size: 0.75em !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.subject-edit-btn:hover {
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.subject-count {
    font-weight: bold;
}

/* 科目编辑模态框样式 */
.subject-checkbox-group {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
}

.subject-checkbox-item {
    padding: 4px 0;
}

.subject-checkbox-item .form-check-input {
    margin-right: 8px;
}

.subject-checkbox-item .form-check-label {
    font-size: 0.9em;
    cursor: pointer;
}

/* 考场选择器样式 */
.room-selector {
    width: 100%;
}

.room-edit-btn {
    height: 32px !important;
    font-size: 0.75em !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.room-edit-btn:hover {
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.room-count {
    font-weight: bold;
}

/* 考场编辑模态框样式 */
.room-checkbox-group {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
}

.room-checkbox-item {
    padding: 4px 0;
}

.room-checkbox-item .form-check-input {
    margin-right: 8px;
}

.room-checkbox-item .form-check-label {
    font-size: 0.9em;
    cursor: pointer;
}

/* 表格排序和筛选样式 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable:hover {
    background-color: rgba(0,0,0,0.05);
}

.sort-icon {
    font-size: 0.8em;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.sortable.sort-asc .sort-icon {
    opacity: 1;
    color: #0d6efd;
}

.sortable.sort-desc .sort-icon {
    opacity: 1;
    color: #0d6efd;
    transform: rotate(180deg);
}

.sortable.sort-asc .sort-icon:before {
    content: "\f0de"; /* fa-sort-up */
}

.sortable.sort-desc .sort-icon:before {
    content: "\f0dd"; /* fa-sort-down */
}

/* 筛选行样式 */
.filter-row {
    background-color: #f8f9fa !important;
}

.filter-row th {
    padding: 8px 6px !important;
    border-top: 1px solid #dee2e6;
}

.filter-input, .filter-select {
    height: 28px !important;
    font-size: 0.75em !important;
    padding: 2px 6px !important;
}

.clear-filters {
    height: 28px !important;
    width: 28px !important;
    padding: 0 !important;
    font-size: 0.7em !important;
}

/* 筛选状态指示 */
.filter-active {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第三步：监考员设置</h4>
                </div>
                <div class="card-body p-0">
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step3_proctors') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="proctors_data" name="proctors_data">
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <button type="button" id="import-excel-btn-proctors" class="btn btn-outline-primary">
                                    <i class="fas fa-file-excel me-2"></i>从Excel导入
                                </button>
                                <a href="{{ url_for('wizard_download_template', template_type='proctors') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-download me-2"></i>下载监考员设置模板
                                </a>
                                <div class="flex-grow-1"></div>
                                <button type="button" id="add-proctor-btn" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>添加监考员
                                </button>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-success" id="copySelected" disabled>
                                        <i class="fas fa-copy me-2"></i>批量复制
                                    </button>
                                    <button type="button" class="btn btn-warning" id="batchEdit" disabled>
                                        <i class="fas fa-edit me-2"></i>批量编辑
                                    </button>
                                    <button type="button" class="btn btn-danger" id="deleteSelected" disabled>
                                        <i class="fas fa-trash me-2"></i>批量删除
                                    </button>
                                </div>
                            </div>
                            <div id="import-result-proctors" class="mt-2"></div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="proctors-table">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 40px;">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th style="width: 60px;" class="sortable" data-sort="index">
                                            序号
                                            <i class="fas fa-sort sort-icon ms-1"></i>
                                        </th>
                                        <th style="width: 120px;" class="sortable" data-sort="name">
                                            监考老师
                                            <i class="fas fa-sort sort-icon ms-1"></i>
                                        </th>
                                        <th style="width: 100px;" class="sortable" data-sort="teaching_subject">
                                            任教科目
                                            <i class="fas fa-sort sort-icon ms-1"></i>
                                        </th>
                                        <th style="width: 150px;">必监考科目</th>
                                        <th style="width: 150px;">不监考科目</th>
                                        <th style="width: 120px;">必监考考场</th>
                                        <th style="width: 120px;">不监考考场</th>
                                        <th style="width: 100px;" class="sortable" data-sort="session_limit">
                                            场次限制
                                            <i class="fas fa-sort sort-icon ms-1"></i>
                                        </th>
                                        <th style="width: 100px;">操作</th>
                                    </tr>
                                    <tr class="filter-row">
                                        <th></th>
                                        <th></th>
                                        <th>
                                            <input type="text" class="form-control form-control-sm filter-input"
                                                   data-filter="name" placeholder="筛选姓名">
                                        </th>
                                        <th>
                                            <input type="text" class="form-control form-control-sm filter-input"
                                                   data-filter="teaching_subject" placeholder="筛选科目">
                                        </th>
                                        <th>
                                            <select class="form-select form-select-sm filter-select" data-filter="required_subjects">
                                                <option value="">全部科目</option>
                                                {% for subject in wizard_data.get('subjects', []) %}
                                                <option value="{{ subject.subject_name }}">{{ subject.subject_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </th>
                                        <th>
                                            <select class="form-select form-select-sm filter-select" data-filter="unavailable_subjects">
                                                <option value="">全部科目</option>
                                                {% for subject in wizard_data.get('subjects', []) %}
                                                <option value="{{ subject.subject_name }}">{{ subject.subject_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </th>
                                        <th>
                                            <select class="form-select form-select-sm filter-select" data-filter="required_rooms">
                                                <option value="">全部考场</option>
                                                {% for room in wizard_data.get('rooms', []) %}
                                                <option value="{{ room.name }}">{{ room.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </th>
                                        <th>
                                            <select class="form-select form-select-sm filter-select" data-filter="unavailable_rooms">
                                                <option value="">全部考场</option>
                                                {% for room in wizard_data.get('rooms', []) %}
                                                <option value="{{ room.name }}">{{ room.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </th>
                                        <th>
                                            <input type="number" class="form-control form-control-sm filter-input"
                                                   data-filter="session_limit" placeholder="场次" min="0">
                                        </th>
                                        <th>
                                            <button type="button" class="btn btn-outline-secondary btn-sm clear-filters" title="清除筛选">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="proctors-list">
                                    <!-- 监考员行将动态添加到这里 -->
                                </tbody>
                            </table>
                        </div>


                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step2_rooms') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量导入监考员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#excelImport">Excel导入</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#textImport">文本导入</a>
                    </li>
                </ul>
                
                <div class="tab-content mt-3">
                    <div class="tab-pane fade show active" id="excelImport">
                        <div class="mb-3">
                            <label class="form-label">选择Excel文件</label>
                            <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls">
                        </div>
                    </div>
                    <div class="tab-pane fade" id="textImport">
                        <div class="mb-3">
                            <label class="form-label">粘贴表格数据（从Excel复制）</label>
                            <textarea class="form-control" id="pasteArea" rows="10" placeholder="从Excel复制数据后粘贴到这里&#10;数据格式：姓名 任教科目 必监考科目 不监考科目 必监考考场 不监考考场 场次限制"></textarea>
                </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="importData">导入数据</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="batchEditModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量修改</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择要修改的字段</label>
                    <select class="form-select" id="batchEditField">
                        <option value="teaching_subject">任教科目</option>
                        <option value="required_subjects">必监考科目</option>
                        <option value="unavailable_subjects">不监考科目</option>
                        <option value="required_rooms">必监考考场</option>
                        <option value="unavailable_rooms">不监考考场</option>
                        <option value="session_limit">场次限制</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">新值</label>
                    <input type="text" class="form-control" id="batchEditValue">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="applyBatchEdit">应用修改</button>
            </div>
        </div>
    </div>
</div>

<!-- 科目编辑模态框 -->
<div class="modal fade" id="subjectEditModal" tabindex="-1" aria-labelledby="subjectEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subjectEditModalLabel">
                    <i class="fas fa-edit me-2"></i>编辑科目设置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">
                        <span id="subject-field-label">科目设置</span>
                    </label>
                    <div class="subject-checkbox-group">
                        {% for subject in wizard_data.get('subjects', []) %}
                        <div class="subject-checkbox-item">
                            <div class="form-check">
                                <input class="form-check-input subject-checkbox" type="checkbox"
                                       value="{{ subject.subject_name }}" id="subject_{{ loop.index }}">
                                <label class="form-check-label" for="subject_{{ loop.index }}">
                                    {{ subject.subject_name }}
                                </label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>选择适用的科目，可以多选。选择后点击"确认"保存设置。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-subject-edit">
                    <i class="fas fa-check me-2"></i>确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 考场编辑模态框 -->
<div class="modal fade" id="roomEditModal" tabindex="-1" aria-labelledby="roomEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roomEditModalLabel">
                    <i class="fas fa-edit me-2"></i>编辑考场设置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">
                        <span id="room-field-label">考场设置</span>
                    </label>
                    <div class="room-checkbox-group">
                        {% for room in wizard_data.get('rooms', []) %}
                        <div class="room-checkbox-item">
                            <div class="form-check">
                                <input class="form-check-input room-checkbox" type="checkbox"
                                       value="{{ room.name }}" id="room_{{ loop.index }}">
                                <label class="form-check-label" for="room_{{ loop.index }}">
                                    {{ room.name }}
                                </label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>选择适用的考场，可以多选。选择后点击"确认"保存设置。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-room-edit">
                    <i class="fas fa-check me-2"></i>确认
                </button>
            </div>
        </div>
    </div>
</div>

<template id="proctor-template">
    <tr>
        <td class="text-center">
            <input type="checkbox" class="form-check-input proctor-select">
        </td>
        <td class="text-center">
            <span class="proctor-index badge bg-secondary"></span>
        </td>
        <td>
            <input type="text" name="proctor_name" class="form-control form-control-sm" placeholder="姓名" required>
        </td>
        <td>
            <input type="text" name="teaching_subject" class="form-control form-control-sm" placeholder="任教科目">
        </td>
        <td>
            <div class="subject-selector" data-field="required_subjects">
                <button type="button" class="btn btn-outline-primary btn-sm w-100 subject-edit-btn"
                        data-field="required_subjects" title="点击编辑必监考科目">
                    <span class="subject-count">0</span> 个科目
                    <i class="fas fa-edit ms-1"></i>
                </button>
                <input type="hidden" name="required_subjects" value="">
            </div>
        </td>
        <td>
            <div class="subject-selector" data-field="unavailable_subjects">
                <button type="button" class="btn btn-outline-danger btn-sm w-100 subject-edit-btn"
                        data-field="unavailable_subjects" title="点击编辑不监考科目">
                    <span class="subject-count">0</span> 个科目
                    <i class="fas fa-edit ms-1"></i>
                </button>
                <input type="hidden" name="unavailable_subjects" value="">
            </div>
        </td>
        <td>
            <div class="room-selector" data-field="required_rooms">
                <button type="button" class="btn btn-outline-success btn-sm w-100 room-edit-btn"
                        data-field="required_rooms" title="点击编辑必监考考场">
                    <span class="room-count">0</span> 个考场
                    <i class="fas fa-edit ms-1"></i>
                </button>
                <input type="hidden" name="required_rooms" value="">
            </div>
        </td>
        <td>
            <div class="room-selector" data-field="unavailable_rooms">
                <button type="button" class="btn btn-outline-warning btn-sm w-100 room-edit-btn"
                        data-field="unavailable_rooms" title="点击编辑不监考考场">
                    <span class="room-count">0</span> 个考场
                    <i class="fas fa-edit ms-1"></i>
                </button>
                <input type="hidden" name="unavailable_rooms" value="">
            </div>
        </td>
        <td>
            <input type="number" name="session_limit" class="form-control form-control-sm" min="0" placeholder="场次">
        </td>
        <td style="vertical-align: middle;">
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-success btn-sm copy-proctor-btn" title="复制">
                    <i class="fas fa-copy"></i>
                </button>
                <button type="button" class="btn btn-danger btn-sm remove-proctor-btn" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/xlsx.full.min.js') }}"></script>
<script>
$(document).ready(function() {
    const wizardData = {{ wizard_data | tojson | safe }};
    let proctors = wizardData.proctors || [];
    const tableBody = $('#proctors-list');
    const template = $('#proctor-template').html();
    
    $(document).keydown(function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'c':
                    if ($('.proctor-select:checked').length > 0) {
                        e.preventDefault();
                        $('#copySelected').click();
                    }
                    break;
                case 'v':
                    if ($(document.activeElement).is('#pasteArea')) {
                        return true;
                    }
                    break;
                case 'd':
                    if ($('.proctor-select:checked').length > 0) {
                        e.preventDefault();
                        $('#deleteSelected').click();
                    }
                    break;
            }
        }
    });

    $('#selectAll').change(function() {
        const isChecked = $(this).prop('checked');
        $('.proctor-select').prop('checked', isChecked);

        // 更新全选状态提示
        updateSelectAllStatus();
    });

    // 监听单个复选框变化
    $(document).on('change', '.proctor-select', function() {
        updateSelectAllStatus();
    });

    // 更新全选状态
    function updateSelectAllStatus() {
        const totalCheckboxes = $('.proctor-select').length;
        const checkedCheckboxes = $('.proctor-select:checked').length;
        const selectAllCheckbox = $('#selectAll');

        if (checkedCheckboxes === 0) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }

        // 更新批量操作按钮状态
        updateBatchButtonsState();
    }

    // 更新批量操作按钮状态
    function updateBatchButtonsState() {
        const selectedCount = $('.proctor-select:checked').length;
        const hasSelection = selectedCount > 0;

        $('#copySelected, #batchEdit, #deleteSelected').prop('disabled', !hasSelection);

        // 显示选择数量
        if (hasSelection) {
            const filterInfo = Object.keys(currentFilters).length > 0 ? ' (筛选中)' : '';
            $('#selectAll').attr('title', `已选择 ${selectedCount} 个监考员${filterInfo}`);
        } else {
            $('#selectAll').attr('title', '全选/取消全选');
        }
    }

    $('#copySelected').click(function() {
        const selectedCheckboxes = $('.proctor-select:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请先选择要复制的监考员');
            return;
        }

        // 收集当前显示的数据（可能是筛选后的）
        const currentDisplayData = [];
        selectedCheckboxes.each(function() {
            const row = $(this).closest('tr');
            const rowIndex = row.index();

            // 从当前显示的数据中获取（filteredProctors或proctors）
            const sourceData = Object.keys(currentFilters).length > 0 ? filteredProctors : proctors;
            if (sourceData[rowIndex]) {
                const proctorData = {...sourceData[rowIndex]};
                proctorData.name = proctorData.name + '(副本)';
                currentDisplayData.push(proctorData);
            }
        });

        // 添加到原始数据中
        proctors = proctors.concat(currentDisplayData);

        // 重新应用筛选和排序
        applyFiltersAndSort();

        alert(`成功复制 ${currentDisplayData.length} 个监考员`);
    });

    $('#deleteSelected').click(function() {
        const selectedCheckboxes = $('.proctor-select:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请先选择要删除的监考员');
            return;
        }

        if (!confirm(`确定要删除选中的 ${selectedCheckboxes.length} 名监考员吗？`)) {
            return;
        }

        // 收集要删除的监考员数据
        const toDelete = [];
        selectedCheckboxes.each(function() {
            const row = $(this).closest('tr');
            const rowIndex = row.index();

            // 从当前显示的数据中获取要删除的项
            const sourceData = Object.keys(currentFilters).length > 0 ? filteredProctors : proctors;
            if (sourceData[rowIndex]) {
                toDelete.push(sourceData[rowIndex]);
            }
        });

        // 从原始数据中删除这些项
        toDelete.forEach(itemToDelete => {
            const originalIndex = proctors.findIndex(p =>
                p.name === itemToDelete.name &&
                p.teaching_subject === itemToDelete.teaching_subject
            );
            if (originalIndex !== -1) {
                proctors.splice(originalIndex, 1);
            }
        });

        // 重新应用筛选和排序
        applyFiltersAndSort();

        alert(`成功删除 ${toDelete.length} 个监考员`);
    });

    $('#batchEdit').click(function() {
        const selectedCheckboxes = $('.proctor-select:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请先选择要修改的监考员');
            return;
        }
        $('#batchEditModal').modal('show');
    });

    $('#applyBatchEdit').click(function() {
        const field = $('#batchEditField').val();
        const value = $('#batchEditValue').val();
        const selectedCheckboxes = $('.proctor-select:checked');

        if (!field || !value) {
            alert('请选择字段并输入值');
            return;
        }

        // 收集要修改的监考员
        const toModify = [];
        selectedCheckboxes.each(function() {
            const row = $(this).closest('tr');
            const rowIndex = row.index();

            // 从当前显示的数据中获取要修改的项
            const sourceData = Object.keys(currentFilters).length > 0 ? filteredProctors : proctors;
            if (sourceData[rowIndex]) {
                toModify.push(sourceData[rowIndex]);
            }
        });

        // 在原始数据中找到并修改这些项
        toModify.forEach(itemToModify => {
            const originalIndex = proctors.findIndex(p =>
                p.name === itemToModify.name &&
                p.teaching_subject === itemToModify.teaching_subject
            );
            if (originalIndex !== -1) {
                if (field === 'required_subjects' || field === 'unavailable_subjects' ||
                    field === 'required_rooms' || field === 'unavailable_rooms') {
                    proctors[originalIndex][field] = value.split(',').map(v => v.trim()).filter(v => v);
                } else {
                    proctors[originalIndex][field] = value;
                }
            }
        });

        // 重新应用筛选和排序
        applyFiltersAndSort();
        $('#batchEditModal').modal('hide');

        alert(`成功修改 ${toModify.length} 个监考员的 ${field} 字段`);
    });

    // 下载模板
    $('#downloadTemplate').click(function(e) {
        e.preventDefault();
        window.location.href = '{{ url_for("wizard_download_template", template_type="proctors") }}';
    });

    // 设置CSRF令牌
    const csrfToken = $('input[name=csrf_token]').val();
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrfToken);
            }
        }
    });

    $('#importData').click(function() {
        const activeTab = $('.nav-tabs .active').attr('href');
        if (activeTab === '#excelImport') {
            const file = $('#excelFile')[0].files[0];
            if (!file) {
                alert('请选择Excel文件');
                return;
            }

            // 检查文件类型
            const allowedTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            if (!allowedTypes.includes(file.type)) {
                alert('请上传Excel文件（.xls或.xlsx格式）');
                return;
            }

            // 检查文件大小
            if (file.size > 5 * 1024 * 1024) {  // 5MB
                alert('文件大小不能超过5MB');
                return;
            }

            // 显示加载提示
            const loadingAlert = $('<div class="alert alert-info alert-dismissible fade show" role="alert">')
                .html(`
                    <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `);
            $('.modal-body').prepend(loadingAlert);

            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', file);
            formData.append('csrf_token', csrfToken);  // 添加CSRF令牌

            // 发送到后端处理
            $.ajax({
                url: '{{ url_for("wizard_import_excel", step_type="proctors") }}',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    loadingAlert.remove();
                    
                    if (response.success) {
                        // 更新proctors数据
                        proctors = response.data;
                        renderProctors();
                        $('#importModal').modal('hide');
                        
                        // 显示成功提示
                        const alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                            .html(`
                                <i class="fas fa-check-circle me-2"></i>${response.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            `);
                        $('.card-body').prepend(alert);
                        
                        setTimeout(() => {
                            alert.alert('close');
                        }, 5000);
                    } else {
                        // 显示错误提示
                        const alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">')
                            .html(`
                                <i class="fas fa-exclamation-circle me-2"></i>${response.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            `);
                        $('.modal-body').prepend(alert);
                        
                        setTimeout(() => {
                            alert.alert('close');
                        }, 5000);
                    }
                },
                error: function(xhr, status, error) {
                    loadingAlert.remove();

                    let errorMessage = '导入失败，请稍后重试';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error('解析错误响应失败:', e);
                    }

                    const alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">')
                        .html(`
                            <i class="fas fa-exclamation-circle me-2"></i>${errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `);
                    $('.modal-body').prepend(alert);

                    setTimeout(() => {
                        alert.alert('close');
                    }, 5000);
                },
                complete: function() {
                    // 重置文件输入
                    $('#excelFile').val('');
                }
            });

        } else {
            const text = $('#pasteArea').val();
            if (!text.trim()) {
                alert('请粘贴数据');
                return;
            }

            try {
                const rows = text.trim().split('\n');
                const jsonData = rows.map(row => {
                    const [name, teaching_subject, required_subjects, unavailable_subjects, required_rooms, unavailable_rooms, session_limit] = row.split('\t');
                    return {
                        name: name.trim(),
                        teaching_subject: teaching_subject?.trim() || '',
                        required_subjects: required_subjects?.trim().split(',').map(s => s.trim()).filter(s => s) || [],
                        unavailable_subjects: unavailable_subjects?.trim().split(',').map(s => s.trim()).filter(s => s) || [],
                        required_rooms: required_rooms?.trim() || '',
                        unavailable_rooms: unavailable_rooms?.trim() || '',
                        session_limit: session_limit?.trim() || ''
                    };
                });

                importProctors(jsonData);
                $('#importModal').modal('hide');
            } catch (error) {
                alert('数据格式错误：' + error.message);
            }
        }
    });

    $('#downloadTemplate').click(function() {
        const template = [
            ['监考老师', '任教科目', '必监考科目(逗号分隔)', '不监考科目(逗号分隔)', '必监考考场(逗号分隔)', '不监考考场(逗号分隔)', '场次限制'],
            ['张三', '语文', '语文', '数学', '1考场,2考场', '3考场', '2'],
            ['李四', '数学', '数学,英语', '', '', '', '3']
        ];

        const ws = XLSX.utils.aoa_to_sheet(template);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '监考员导入模板');
        XLSX.writeFile(wb, '监考员导入模板.xlsx');
    });

    function importProctors(jsonData) {
        if (!Array.isArray(jsonData) || jsonData.length === 0) {
            alert('没有找到有效数据');
            return;
        }

        const invalidData = jsonData.filter(row => !row.name?.trim());
        if (invalidData.length > 0) {
            alert('发现 ' + invalidData.length + ' 条无效数据（姓名为空），这些数据将被忽略');
        }

        const validData = jsonData.filter(row => row.name?.trim());
        proctors = proctors.concat(validData);
        renderProctors();
    }

    function collectDataFromDOM() {
        const collectedProctors = [];
        $('#proctors-list tr').each(function() {
            const name = $(this).find('input[name="proctor_name"]').val().trim();

            // 从隐藏输入框获取科目数据
            let requiredSubjects = [];
            let unavailableSubjects = [];
            let requiredRooms = [];
            let unavailableRooms = [];

            try {
                requiredSubjects = JSON.parse($(this).find('input[name="required_subjects"]').val() || '[]');
            } catch (e) {
                requiredSubjects = [];
            }

            try {
                unavailableSubjects = JSON.parse($(this).find('input[name="unavailable_subjects"]').val() || '[]');
            } catch (e) {
                unavailableSubjects = [];
            }

            try {
                requiredRooms = JSON.parse($(this).find('input[name="required_rooms"]').val() || '[]');
            } catch (e) {
                requiredRooms = [];
            }

            try {
                unavailableRooms = JSON.parse($(this).find('input[name="unavailable_rooms"]').val() || '[]');
            } catch (e) {
                unavailableRooms = [];
            }

            collectedProctors.push({
                name: name,
                teaching_subject: $(this).find('input[name="teaching_subject"]').val().trim(),
                required_subjects: requiredSubjects,
                unavailable_subjects: unavailableSubjects,
                required_rooms: requiredRooms,
                unavailable_rooms: unavailableRooms,
                session_limit: $(this).find('input[name="session_limit"]').val()
            });
        });
        return collectedProctors;
    }

    function renderProctors() {
        tableBody.empty();
        proctors.forEach((proctor, index) => {
            const proctorEl = $(template);
            proctorEl.find('.proctor-index').text(index + 1);
            proctorEl.find('input[name="proctor_name"]').val(proctor.name || '');
            proctorEl.find('input[name="teaching_subject"]').val(proctor.teaching_subject || '');

            // 处理必监考科目
            const requiredSubjects = proctor.required_subjects || [];
            proctorEl.find('input[name="required_subjects"]').val(JSON.stringify(requiredSubjects));
            proctorEl.find('[data-field="required_subjects"] .subject-count').text(requiredSubjects.length);

            // 处理不监考科目
            const unavailableSubjects = proctor.unavailable_subjects || [];
            proctorEl.find('input[name="unavailable_subjects"]').val(JSON.stringify(unavailableSubjects));
            proctorEl.find('[data-field="unavailable_subjects"] .subject-count').text(unavailableSubjects.length);

            // 处理必监考考场
            const requiredRooms = proctor.required_rooms || [];
            proctorEl.find('input[name="required_rooms"]').val(JSON.stringify(requiredRooms));
            proctorEl.find('[data-field="required_rooms"] .room-count').text(requiredRooms.length);

            // 处理不监考考场
            const unavailableRooms = proctor.unavailable_rooms || [];
            proctorEl.find('input[name="unavailable_rooms"]').val(JSON.stringify(unavailableRooms));
            proctorEl.find('[data-field="unavailable_rooms"] .room-count').text(unavailableRooms.length);

            proctorEl.find('input[name="session_limit"]').val(proctor.session_limit || '');

            tableBody.append(proctorEl);
        });

        // 渲染完成后更新全选状态
        setTimeout(() => {
            updateSelectAllStatus();
        }, 10);
    }

    $('#add-proctor-btn').click(function() {
        // 先同步当前编辑到原始数据
        syncCurrentEditsToOriginalData();

        // 添加新的监考员到原始数据
        proctors.push({});

        // 重新应用筛选和排序
        if (Object.keys(currentFilters).length > 0 || currentSort.field) {
            applyFiltersAndSort();
        } else {
            renderProctors();
        }
    });

    $(document).on('click', '.copy-proctor-btn', function() {
        // 先同步当前编辑到原始数据
        syncCurrentEditsToOriginalData();

        const row = $(this).closest('tr');
        const rowIndex = row.index();

        // 根据筛选状态选择正确的数据源
        const sourceData = Object.keys(currentFilters).length > 0 ? filteredProctors : proctors;
        if (sourceData[rowIndex]) {
            const proctorData = {...sourceData[rowIndex]};
            proctorData.name = proctorData.name + '(副本)';
            proctors.push(proctorData);

            // 重新应用筛选和排序
            if (Object.keys(currentFilters).length > 0 || currentSort.field) {
                applyFiltersAndSort();
            } else {
                renderProctors();
            }
        }
    });

    $(document).on('click', '.remove-proctor-btn', function() {
        // 先同步当前编辑到原始数据
        syncCurrentEditsToOriginalData();

        const row = $(this).closest('tr');
        const rowIndex = row.index();

        // 根据筛选状态选择正确的数据源
        const sourceData = Object.keys(currentFilters).length > 0 ? filteredProctors : proctors;
        if (sourceData[rowIndex]) {
            const proctorToDelete = sourceData[rowIndex];

            // 从原始数据中删除
            const originalIndex = proctors.findIndex(p =>
                p.name === proctorToDelete.name &&
                p.teaching_subject === proctorToDelete.teaching_subject
            );
            if (originalIndex !== -1) {
                proctors.splice(originalIndex, 1);
            }

            // 重新应用筛选和排序
            if (Object.keys(currentFilters).length > 0 || currentSort.field) {
                applyFiltersAndSort();
            } else {
                renderProctors();
            }
        }
    });

    $('#wizard-form').submit(function(e) {
        // 先同步当前编辑到原始数据
        syncCurrentEditsToOriginalData();
        let isValid = true;

        if (proctors.length > 0) {
            const name = proctors[0].name;
            if (!name) {
                 if (proctors.length === 1) {
                    proctors = [];
                } else {
                isValid = false;
                }
            }
        }
        
        if (proctors.length > 0) {
            isValid = !proctors.some(p => !p.name);
        }

        if (!isValid) {
            e.preventDefault();
            alert('"监考老师"的姓名不能为空。');
            return;
        }

        if (proctors.length === 0) {
            e.preventDefault();
            alert('请至少添加一名监考员。');
            return;
        }

        $('#proctors_data').val(JSON.stringify(proctors));
    });

    // 通用Excel导入组件JavaScript
    function initExcelImportComponent() {
        const componentId = 'proctors';
        const importType = 'proctors';
        const importUrl = '/wizard/import-excel/' + importType;
        
        console.log(`初始化Excel导入组件: ${componentId}, 类型: ${importType}`);
        
        const importBtn = document.getElementById(`import-excel-btn-${componentId}`);
        const resultArea = document.getElementById(`import-result-${componentId}`);
        
        if (!importBtn || !resultArea) {
            console.error('Excel导入组件元素未找到');
            return;
        }
        
        // 导入按钮点击事件
        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`${componentId}: Excel导入按钮被点击`);
            
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.style.display = 'none';
            
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) {
                    console.log(`${componentId}: 没有选择文件`);
                    return;
                }
                
                console.log(`${componentId}: 文件已选择:`, file.name, file.size, 'bytes');
                handleFileUpload(file);
                
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };
            
            document.body.appendChild(input);
            input.click();
        });
        
        function handleFileUpload(file) {
            console.log(`${componentId}: 开始处理文件:`, file.name);
            
            const allowedTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                showImportMessage('请上传Excel文件（.xls或.xlsx格式）', 'danger');
                return;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                showImportMessage('文件大小不能超过5MB', 'danger');
                return;
            }
            
            const csrfToken = document.querySelector('input[name=csrf_token]')?.value;
            if (!csrfToken) {
                showImportMessage('CSRF令牌未找到，请刷新页面重试', 'danger');
                return;
            }
            
            showLoadingMessage();
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('csrf_token', csrfToken);
            
            fetch(importUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log(`${componentId}: 服务器响应状态:`, response.status);
                return response.json();
            })
            .then(data => {
                console.log(`${componentId}: 导入成功:`, data);
                hideLoadingMessage();
                
                if (data.success) {
                    showImportMessage(data.message || '导入成功', 'success');

                    // 更新监考员数据
                    proctors = data.data;

                    // 重置筛选和排序状态
                    currentFilters = {};
                    currentSort = { field: null, direction: 'asc' };
                    $('.filter-input, .filter-select').val('').removeClass('filter-active');
                    $('.sortable').removeClass('sort-asc sort-desc');

                    // 更新筛选数据
                    filteredProctors = proctors.slice();

                    // 重新渲染
                    renderProctors();
                    
                    setTimeout(() => {
                        hideImportMessage();
                    }, 5000);
                } else {
                    showImportMessage(data.message || '导入失败', 'danger');
                }
            })
            .catch(error => {
                console.error(`${componentId}: 导入失败:`, error);
                hideLoadingMessage();
                
                let errorMessage = '导入失败，请稍后重试';
                if (error.message.includes('403')) {
                    errorMessage = '权限不足或CSRF验证失败，请刷新页面重试';
                } else if (error.message.includes('413')) {
                    errorMessage = '文件过大，请选择较小的文件';
                } else if (error.message.includes('500')) {
                    errorMessage = '服务器内部错误，请联系管理员';
                }
                
                showImportMessage(errorMessage, 'danger');
            });
        }
        
        function showLoadingMessage() {
            resultArea.innerHTML = `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideLoadingMessage() {
            const loadingAlert = resultArea.querySelector('.alert-info');
            if (loadingAlert) {
                loadingAlert.remove();
            }
        }
        
        function showImportMessage(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const iconClass = type === 'success' ? 'fa-check-circle' : 
                             type === 'danger' ? 'fa-exclamation-circle' : 
                             'fa-info-circle';
            
            resultArea.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${iconClass} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function hideImportMessage() {
            resultArea.innerHTML = '';
        }
        
        console.log(`Excel导入组件 ${componentId} 初始化完成`);
    }

    renderProctors();
    
    // 科目编辑功能
    let currentEditingElement = null;
    let currentEditingField = null;

    // 科目编辑按钮点击事件
    $(document).on('click', '.subject-edit-btn', function() {
        currentEditingElement = $(this);
        currentEditingField = $(this).data('field');

        // 更新模态框标题
        const fieldLabel = currentEditingField === 'required_subjects' ? '必监考科目' : '不监考科目';
        $('#subject-field-label').text(fieldLabel);

        // 获取当前选择的科目
        const hiddenInput = $(this).siblings('input[type="hidden"]');
        let selectedSubjects = [];
        try {
            selectedSubjects = JSON.parse(hiddenInput.val() || '[]');
        } catch (e) {
            selectedSubjects = [];
        }

        // 重置所有复选框
        $('.subject-checkbox').prop('checked', false);

        // 设置已选择的科目
        selectedSubjects.forEach(subject => {
            $(`.subject-checkbox[value="${subject}"]`).prop('checked', true);
        });

        // 显示模态框
        $('#subjectEditModal').modal('show');
    });

    // 确认科目编辑
    $('#confirm-subject-edit').click(function() {
        if (!currentEditingElement || !currentEditingField) return;

        // 收集选择的科目
        const selectedSubjects = [];
        $('.subject-checkbox:checked').each(function() {
            selectedSubjects.push($(this).val());
        });

        // 更新隐藏输入框
        const hiddenInput = currentEditingElement.siblings('input[type="hidden"]');
        hiddenInput.val(JSON.stringify(selectedSubjects));

        // 更新按钮显示
        currentEditingElement.find('.subject-count').text(selectedSubjects.length);

        // 关闭模态框
        $('#subjectEditModal').modal('hide');

        // 重置编辑状态
        currentEditingElement = null;
        currentEditingField = null;
    });

    // 模态框关闭时重置状态
    $('#subjectEditModal').on('hidden.bs.modal', function() {
        currentEditingElement = null;
        currentEditingField = null;
    });

    // 考场编辑功能
    let currentEditingRoomElement = null;
    let currentEditingRoomField = null;

    // 考场编辑按钮点击事件
    $(document).on('click', '.room-edit-btn', function() {
        currentEditingRoomElement = $(this);
        currentEditingRoomField = $(this).data('field');

        // 更新模态框标题
        const fieldLabel = currentEditingRoomField === 'required_rooms' ? '必监考考场' : '不监考考场';
        $('#room-field-label').text(fieldLabel);

        // 获取当前选择的考场
        const hiddenInput = $(this).siblings('input[type="hidden"]');
        let selectedRooms = [];
        try {
            selectedRooms = JSON.parse(hiddenInput.val() || '[]');
        } catch (e) {
            selectedRooms = [];
        }

        // 重置所有复选框
        $('.room-checkbox').prop('checked', false);

        // 设置已选择的考场
        selectedRooms.forEach(room => {
            $(`.room-checkbox[value="${room}"]`).prop('checked', true);
        });

        // 显示模态框
        $('#roomEditModal').modal('show');
    });

    // 确认考场编辑
    $('#confirm-room-edit').click(function() {
        if (!currentEditingRoomElement || !currentEditingRoomField) return;

        // 收集选择的考场
        const selectedRooms = [];
        $('.room-checkbox:checked').each(function() {
            selectedRooms.push($(this).val());
        });

        // 更新隐藏输入框
        const hiddenInput = currentEditingRoomElement.siblings('input[type="hidden"]');
        hiddenInput.val(JSON.stringify(selectedRooms));

        // 更新按钮显示
        currentEditingRoomElement.find('.room-count').text(selectedRooms.length);

        // 关闭模态框
        $('#roomEditModal').modal('hide');

        // 重置编辑状态
        currentEditingRoomElement = null;
        currentEditingRoomField = null;
    });

    // 考场模态框关闭时重置状态
    $('#roomEditModal').on('hidden.bs.modal', function() {
        currentEditingRoomElement = null;
        currentEditingRoomField = null;
    });

    // 排序和筛选功能
    let currentSort = { field: null, direction: 'asc' };
    let currentFilters = {};
    let filteredProctors = [];

    // 排序功能
    $('.sortable').click(function() {
        const field = $(this).data('sort');

        // 更新排序状态
        if (currentSort.field === field) {
            currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            currentSort.field = field;
            currentSort.direction = 'asc';
        }

        // 更新排序图标
        $('.sortable').removeClass('sort-asc sort-desc');
        $(this).addClass('sort-' + currentSort.direction);

        // 执行排序
        applyFiltersAndSort();
    });

    // 筛选功能
    $('.filter-input, .filter-select').on('input change', function() {
        const field = $(this).data('filter');
        const value = $(this).val().trim();

        if (value) {
            currentFilters[field] = value;
            $(this).addClass('filter-active');
        } else {
            delete currentFilters[field];
            $(this).removeClass('filter-active');
        }

        // 执行筛选
        applyFiltersAndSort();
    });

    // 清除筛选
    $('.clear-filters').click(function() {
        currentFilters = {};
        $('.filter-input, .filter-select').val('').removeClass('filter-active');
        applyFiltersAndSort();
    });

    // 同步当前显示的编辑到原始数据
    function syncCurrentEditsToOriginalData() {
        // 只在有筛选的情况下才需要同步，因为无筛选时显示的就是完整数据
        if (Object.keys(currentFilters).length > 0 || currentSort.field) {
            const currentDisplayData = collectDataFromDOM();

            // 将当前显示的编辑同步到原始数据中
            currentDisplayData.forEach((editedProctor, displayIndex) => {
                // 找到对应的原始数据项
                const originalProctor = filteredProctors[displayIndex];
                if (originalProctor) {
                    const originalIndex = proctors.findIndex(p =>
                        p.name === originalProctor.name &&
                        p.teaching_subject === originalProctor.teaching_subject
                    );
                    if (originalIndex !== -1) {
                        // 更新原始数据
                        proctors[originalIndex] = {...editedProctor};
                    }
                }
            });
        }
    }

    // 应用筛选和排序
    function applyFiltersAndSort() {
        // 先同步当前的编辑到原始数据
        syncCurrentEditsToOriginalData();

        // 应用筛选
        filteredProctors = proctors.filter(proctor => {
            for (let field in currentFilters) {
                const filterValue = currentFilters[field].toLowerCase();
                let proctorValue = '';

                if (field === 'name') {
                    proctorValue = (proctor.name || '').toLowerCase();
                } else if (field === 'teaching_subject') {
                    proctorValue = (proctor.teaching_subject || '').toLowerCase();
                } else if (field === 'session_limit') {
                    proctorValue = (proctor.session_limit || '').toString();
                } else if (field === 'required_subjects' || field === 'unavailable_subjects') {
                    const subjects = proctor[field] || [];
                    proctorValue = subjects.join(',').toLowerCase();
                } else if (field === 'required_rooms' || field === 'unavailable_rooms') {
                    const rooms = proctor[field] || [];
                    proctorValue = rooms.join(',').toLowerCase();
                }

                if (field === 'session_limit') {
                    if (proctorValue !== filterValue) return false;
                } else if (field.includes('subjects') || field.includes('rooms')) {
                    if (!proctorValue.includes(filterValue)) return false;
                } else {
                    if (!proctorValue.includes(filterValue)) return false;
                }
            }
            return true;
        });

        // 应用排序
        if (currentSort.field) {
            filteredProctors.sort((a, b) => {
                let aValue = a[currentSort.field] || '';
                let bValue = b[currentSort.field] || '';

                // 处理数字类型
                if (currentSort.field === 'session_limit') {
                    aValue = parseInt(aValue) || 0;
                    bValue = parseInt(bValue) || 0;
                } else if (currentSort.field === 'index') {
                    aValue = proctors.indexOf(a) + 1;
                    bValue = proctors.indexOf(b) + 1;
                } else {
                    aValue = aValue.toString().toLowerCase();
                    bValue = bValue.toString().toLowerCase();
                }

                if (currentSort.direction === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
        }

        // 重新渲染表格
        renderFilteredProctors();
    }

    // 渲染筛选后的监考员
    function renderFilteredProctors() {
        tableBody.empty();
        filteredProctors.forEach((proctor, index) => {
            const proctorEl = $(template);
            const originalIndex = proctors.indexOf(proctor) + 1;

            proctorEl.find('.proctor-index').text(originalIndex);
            proctorEl.find('input[name="proctor_name"]').val(proctor.name || '');
            proctorEl.find('input[name="teaching_subject"]').val(proctor.teaching_subject || '');

            // 处理必监考科目
            const requiredSubjects = proctor.required_subjects || [];
            proctorEl.find('input[name="required_subjects"]').val(JSON.stringify(requiredSubjects));
            proctorEl.find('[data-field="required_subjects"] .subject-count').text(requiredSubjects.length);

            // 处理不监考科目
            const unavailableSubjects = proctor.unavailable_subjects || [];
            proctorEl.find('input[name="unavailable_subjects"]').val(JSON.stringify(unavailableSubjects));
            proctorEl.find('[data-field="unavailable_subjects"] .subject-count').text(unavailableSubjects.length);

            // 处理必监考考场
            const requiredRooms = proctor.required_rooms || [];
            proctorEl.find('input[name="required_rooms"]').val(JSON.stringify(requiredRooms));
            proctorEl.find('[data-field="required_rooms"] .room-count').text(requiredRooms.length);

            // 处理不监考考场
            const unavailableRooms = proctor.unavailable_rooms || [];
            proctorEl.find('input[name="unavailable_rooms"]').val(JSON.stringify(unavailableRooms));
            proctorEl.find('[data-field="unavailable_rooms"] .room-count').text(unavailableRooms.length);

            proctorEl.find('input[name="session_limit"]').val(proctor.session_limit || '');

            tableBody.append(proctorEl);
        });

        // 渲染完成后更新全选状态
        setTimeout(() => {
            updateSelectAllStatus();
        }, 10);
    }

    // 重写原来的renderProctors函数，使其支持筛选
    const originalRenderProctors = renderProctors;
    renderProctors = function() {
        if (Object.keys(currentFilters).length > 0 || currentSort.field) {
            applyFiltersAndSort();
        } else {
            originalRenderProctors();
        }
    };

    // 初始化筛选数据
    filteredProctors = proctors.slice();

    // 初始化Excel导入组件
    initExcelImportComponent();
});
</script>
{% endblock %} 
