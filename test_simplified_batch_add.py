#!/usr/bin/env python3
"""
测试简化的批量添加考场功能
验证只需要设置考场数量和监考人数的简化版本
"""

import os

def check_simplified_ui():
    """检查简化后的UI组件"""
    print("🔍 检查简化后的UI组件...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查保留的UI组件
        required_components = {
            '批量添加按钮': 'id="batch-add-room-btn"' in content,
            '批量添加模态框': 'id="batchAddModal"' in content,
            '考场数量输入': 'id="room-count"' in content,
            '监考人数设置': 'default-demand' in content,
            '预览文本': 'id="preview-text"' in content,
            '确认添加按钮': 'id="confirm-batch-add"' in content
        }
        
        # 检查移除的UI组件
        removed_components = {
            '考场前缀输入': 'id="room-prefix"' not in content,
            '起始编号输入': 'id="start-number"' not in content,
            '前缀标签': '考场名称前缀' not in content,
            '起始编号标签': '起始编号' not in content
        }
        
        all_passed = True
        
        print("   保留的组件:")
        for check_name, result in required_components.items():
            status = "✅" if result else "❌"
            print(f"     {status} {check_name}")
            if not result:
                all_passed = False
        
        print("   移除的组件:")
        for check_name, result in removed_components.items():
            status = "✅" if result else "❌"
            print(f"     {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_simplified_javascript():
    """检查简化后的JavaScript功能"""
    print("\n🔍 检查简化后的JavaScript功能...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查保留的JavaScript功能
        required_js = {
            '预览更新函数': 'function updateBatchAddPreview()' in content,
            '考场数量监听': '#room-count' in content and 'on(\'input change\'' in content,
            '确认添加处理': '#confirm-batch-add' in content,
            '监考人数收集': '.default-demand' in content,
            '考场编号计算': 'nextRoomNumber' in content,
            '简化的考场命名': '+ \'考场\'' in content
        }
        
        # 检查移除的JavaScript功能
        removed_js = {
            '前缀处理': 'room-prefix' not in content,
            '起始编号处理': 'start-number' not in content,
            '复杂命名逻辑': 'prefix ?' not in content
        }
        
        all_passed = True
        
        print("   保留的功能:")
        for check_name, result in required_js.items():
            status = "✅" if result else "❌"
            print(f"     {status} {check_name}")
            if not result:
                all_passed = False
        
        print("   移除的功能:")
        for check_name, result in removed_js.items():
            status = "✅" if result else "❌"
            print(f"     {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_simplified_naming_logic():
    """测试简化的考场命名逻辑"""
    print("\n🧪 测试简化的考场命名逻辑...")
    
    test_cases = [
        # (现有考场数, 添加数量, 期望的考场名称列表)
        (0, 3, ['1考场', '2考场', '3考场']),
        (2, 2, ['3考场', '4考场']),
        (5, 1, ['6考场']),
        (10, 5, ['11考场', '12考场', '13考场', '14考场', '15考场'])
    ]
    
    all_passed = True
    
    for existing_count, add_count, expected in test_cases:
        # 模拟简化的JavaScript逻辑
        next_room_number = existing_count + 1
        room_names = []
        for i in range(add_count):
            room_name = str(next_room_number + i) + '考场'
            room_names.append(room_name)
        
        if room_names == expected:
            print(f"   ✅ 现有{existing_count}个, 添加{add_count}个: {room_names}")
        else:
            print(f"   ❌ 现有{existing_count}个, 添加{add_count}个: 期望{expected}, 实际{room_names}")
            all_passed = False
    
    return all_passed

def test_simplified_demands_logic():
    """测试简化的监考人数逻辑"""
    print("\n🧪 测试简化的监考人数逻辑...")
    
    # 模拟科目和监考人数设置
    subjects = ['语文', '数学', '英语']
    demands_settings = {'语文': 2, '数学': 2, '英语': 1}
    
    # 模拟批量添加3个考场
    existing_rooms = 2  # 假设已有2个考场
    add_count = 3
    next_room_number = existing_rooms + 1
    
    rooms = []
    for i in range(add_count):
        room_name = str(next_room_number + i) + '考场'
        # 复制监考需求（模拟Object.assign）
        demands = demands_settings.copy()
        rooms.append({
            'name': room_name,
            'demands': demands
        })
    
    # 验证结果
    expected_rooms = [
        {'name': '3考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '4考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '5考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}}
    ]
    
    if rooms == expected_rooms:
        print("   ✅ 监考人数设置正确")
        for room in rooms:
            print(f"      {room['name']}: {room['demands']}")
        return True
    else:
        print("   ❌ 监考人数设置错误")
        print(f"      期望: {expected_rooms}")
        print(f"      实际: {rooms}")
        return False

def test_simplified_scenarios():
    """测试简化版本的使用场景"""
    print("\n🧪 测试简化版本的使用场景...")
    
    scenarios = [
        {
            'name': '新建考场',
            'existing_count': 0,
            'add_count': 5,
            'demands': {'语文': 2, '数学': 2, '英语': 2},
            'expected_names': ['1考场', '2考场', '3考场', '4考场', '5考场']
        },
        {
            'name': '追加考场',
            'existing_count': 3,
            'add_count': 2,
            'demands': {'高等数学': 2, '大学物理': 1},
            'expected_names': ['4考场', '5考场']
        },
        {
            'name': '大批量添加',
            'existing_count': 10,
            'add_count': 20,
            'demands': {'计算机基础': 3},
            'expected_names': [f'{i}考场' for i in range(11, 31)]
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        try:
            # 模拟简化的批量添加逻辑
            next_room_number = scenario['existing_count'] + 1
            rooms = []
            
            for i in range(scenario['add_count']):
                room_name = str(next_room_number + i) + '考场'
                rooms.append({
                    'name': room_name,
                    'demands': scenario['demands'].copy()
                })
            
            # 验证考场名称
            actual_names = [room['name'] for room in rooms]
            if actual_names == scenario['expected_names']:
                print(f"   ✅ 考场名称: {actual_names[:3]}{'...' if len(actual_names) > 3 else ''}")
            else:
                print(f"   ❌ 考场名称错误")
                print(f"      期望: {scenario['expected_names']}")
                print(f"      实际: {actual_names}")
                all_passed = False
            
            # 验证监考需求
            if all(room['demands'] == scenario['demands'] for room in rooms):
                print(f"   ✅ 监考需求: {scenario['demands']}")
            else:
                print(f"   ❌ 监考需求错误")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 场景执行失败: {e}")
            all_passed = False
    
    return all_passed

def test_ui_simplification():
    """测试UI简化效果"""
    print("\n🧪 测试UI简化效果...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计表单字段数量
        form_fields = content.count('<input')
        
        # 检查模态框内容简洁性
        modal_start = content.find('id="batchAddModal"')
        modal_end = content.find('</div>', modal_start + 500)  # 大概范围
        modal_content = content[modal_start:modal_end] if modal_start != -1 and modal_end != -1 else ""
        
        # 检查简化指标
        simplification_checks = {
            '表单字段适中': form_fields <= 10,  # 不超过10个输入字段
            '无复杂前缀设置': '前缀' not in modal_content,
            '无起始编号设置': '起始编号' not in modal_content,
            '保留核心功能': '考场数量' in content and '监考人数' in content,
            '预览信息简洁': '将添加' in content
        }
        
        all_passed = True
        for check_name, result in simplification_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        print(f"   📊 表单字段总数: {form_fields}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 简化批量添加考场功能测试")
    print("=" * 50)
    print("测试只需要设置考场数量和监考人数的简化版本")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查简化后的UI组件
    test_results.append(("简化UI组件检查", check_simplified_ui()))
    
    # 2. 检查简化后的JavaScript功能
    test_results.append(("简化JavaScript功能", check_simplified_javascript()))
    
    # 3. 测试简化的考场命名逻辑
    test_results.append(("简化命名逻辑", test_simplified_naming_logic()))
    
    # 4. 测试简化的监考人数逻辑
    test_results.append(("简化监考人数逻辑", test_simplified_demands_logic()))
    
    # 5. 测试简化版本的使用场景
    test_results.append(("简化使用场景", test_simplified_scenarios()))
    
    # 6. 测试UI简化效果
    test_results.append(("UI简化效果", test_ui_simplification()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 简化后的功能特点:")
        print("✅ 只需设置考场数量和监考人数")
        print("✅ 自动按数字顺序命名考场")
        print("✅ 界面简洁，操作简单")
        print("✅ 保留核心批量添加功能")
        print("✅ 移除了复杂的命名设置")
        
        print("\n🚀 简化后的使用方法:")
        print("1. 点击'批量添加'按钮")
        print("2. 设置要添加的考场数量")
        print("3. 为每个科目设置监考人数")
        print("4. 点击'确认添加'完成操作")
        print("5. 考场自动按数字顺序命名")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
