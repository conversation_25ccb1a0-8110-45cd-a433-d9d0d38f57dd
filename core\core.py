#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强化场次优化器
专门解决监考员利用率低和未达预设场次的问题

修改日志 - 基于数学模型的考场约束逻辑：
1. 实现了基于科目的考场约束逻辑
2. 必监考考场设为硬约束（如果监考员i监考科目k且存在必监考考场限制，则只能在必监考考场中选择）
3. 不监考考场设为软约束（通过目标函数惩罚机制尽量避免）
4. 添加了考场选择优先级奖励机制：
   - 必监考考场奖励：8000分
   - 不监考考场惩罚：2000分
5. 增强了结果分析，包含考场约束满足情况统计

最新修改 - 按照修正版数学模型：
6. 实现约束2：场次平衡硬约束
   - L_i - 1 <= 实际场次 <= L_i + 1
7. 实现约束5：监考科目指示器约束
   - 添加monitor_subject变量来精确表达条件约束
8. 实现约束4（修正版）：必监考考场硬约束
   - 使用条件约束：只有在监考该科目时才受必监考考场限制
9. 实现约束6：考场人数平衡约束（软约束）
   - 恢复超额松弛变量，允许适度超员
10. 实现约束7：必监考科目约束（软约束）
    - 使用monitor_subject变量而不是直接的x变量

数学模型对应（修正版）：
- 约束1：科目冲突约束 → _add_hard_constraints()
- 约束2：场次平衡硬约束 → _add_hard_constraints()
- 约束3：禁监考科目约束 → _add_hard_constraints()
- 约束4：必监考考场硬约束（修正版）→ _add_hard_constraints()
- 约束5：监考科目指示器约束 → _add_hard_constraints()
- 约束6：考场人数平衡约束（软约束）→ _add_soft_constraints()
- 约束7：必监考科目约束（软约束）→ _add_soft_constraints()
- F_room_preference：考场选择优先级奖励 → _build_enhanced_objective()
"""

import json
import pandas as pd
from datetime import datetime
import random
import numpy as np
import math
from typing import Dict, List, Tuple, Set
import time
import os

# 导入OR-Tools
try:
    from ortools.sat.python import cp_model
    ORTOOLS_AVAILABLE = True
except ImportError:
    print("⚠️ OR-Tools未安装，请运行: pip install ortools")
    ORTOOLS_AVAILABLE = False


def load_config(config_file=None):
    """
    加载配置
    :param config_file: 配置文件路径。如果为None，则使用默认路径。
    :return: 配置字典
    """
    if config_file is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        config_file = os.path.join(base_dir, '考试配置处理结果.json')

    if not os.path.exists(config_file):
        print(f"ERROR: 配置文件不存在: {config_file}")
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    print(f"INFO: 从 {config_file} 加载配置")
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)


class EnhancedProctorScheduler:
    """强化场次分配优化器"""
    
    def __init__(self, config_file=None):
        """初始化优化器"""
        print("🔧 初始化强化场次分配优化器...")
        
        # 加载配置
        self.config = load_config(config_file)
        self.proctors = self.config['data']['proctor_settings']['records']
        self.subjects = [record['课程名称']
                         for record in self.config['data']['subject_settings']['records']]
        # 智能排除汇总行（如果存在）
        room_records = self.config['data']['room_settings']['records']
        
        # 检查最后一行是否为汇总行
        if room_records and isinstance(room_records[-1].get('考场'), str):
            last_room_name = room_records[-1]['考场']
            # 常见的汇总行标识
            summary_keywords = ['总监考人员需求', '汇总', '总计', '合计', '总需求']
            is_summary = any(keyword in last_room_name for keyword in summary_keywords)
            
            if is_summary:
                print(f"🔍 发现汇总行: {last_room_name}，将其排除")
                self.rooms = room_records[:-1]
            else:
                print(f"✅ 最后一行是真实考场: {last_room_name}，保留所有考场")
                self.rooms = room_records
        else:
            self.rooms = room_records
        
        # 强化配置
        self.duration_balance_threshold = 60  # 时长均衡阈值
        self.utilization_target = 0.85  # 目标利用率85%
        self.min_sessions_enforcement = True  # 强制最小场次分配
        
        # 监考时间策略配置（新增）
        self.schedule_strategy = "concentrated"  # "concentrated"(集中监考) 或 "distributed"(分散监考)
        self.time_slot_preference_weight = 5000  # 时间段偏好权重
        
        # 时间段定义
        self.time_slots = {
            "morning": {"start": 7, "end": 12},      # 上午 7:00-12:00
            "afternoon": {"start": 12, "end": 18},   # 下午 12:00-18:00  
            "evening": {"start": 18, "end": 23}      # 晚上 18:00-23:00
        }
        
        # 基础数据
        self.n_proctors = len(self.proctors)
        self.n_subjects = len(self.subjects)
        self.n_rooms = len(self.rooms)
        
        # 创建映射
        self.proctor_to_idx = {p['监考老师']: i for i,
                               p in enumerate(self.proctors)}
        self.subject_to_idx = {s: i for i, s in enumerate(self.subjects)}
        self.room_to_idx = {r['考场']: i for i, r in enumerate(self.rooms)}
        
        # 加载科目时长
        self._load_subject_durations()
        self._load_time_slot_mapping()
        
        # 构建需求矩阵和约束
        self._build_demand_matrix()
        self._build_constraints()
        
        # 初始化模型组件
        self.model = None
        self.solver = None
        self.variables = {}
        self.slack_variables = {}
        self.utilization_vars = {}
        self.min_sessions_vars = {}
        
        # 结果存储
        self.assignment = {}
        self.workload = {}
        self.workload_durations = {}
        
        # 打印考场约束配置概览
        self._print_room_constraints_summary()

        print(
            f"✅ 初始化完成: {self.n_proctors}名监考员, {self.n_subjects}个科目, {self.n_rooms}个考场")
        print(f"🎯 目标利用率: {self.utilization_target*100:.1f}%")
    
    def set_schedule_strategy(self, strategy="concentrated", weight=5000):
        """设置监考时间策略
        
        Args:
            strategy (str): 监考策略
                - "concentrated": 集中监考（默认）- 减少监考员往返学校次数
                - "distributed": 分散监考 - 减少监考员连续监考
            weight (int): 时间段偏好权重，数值越大影响越强
        """
        if strategy not in ["concentrated", "distributed"]:
            raise ValueError("策略必须是 'concentrated' 或 'distributed'")
        
        self.schedule_strategy = strategy
        self.time_slot_preference_weight = weight
        
        strategy_name = "集中监考" if strategy == "concentrated" else "分散监考"
        print(f"📅 监考时间策略设置为: {strategy_name} (权重: {weight})")
        
        if strategy == "concentrated":
            print("   - 目标: 减少监考员往返学校次数")
            print("   - 效果: 将监考员的任务集中到特定时间段（上午、下午、晚上）")
        else:
            print("   - 目标: 减少监考员连续监考工作量")
            print("   - 效果: 将监考员的任务分散到不同时间段")
    
    def _load_subject_durations(self):
        """加载各科目考试时长"""
        subject_records = self.config['data']['subject_settings']['records']
        self.subject_durations = {}
        
        for record in subject_records:
            subject_name = record['课程名称']
            duration = record['考试时长(分钟)']
            self.subject_durations[subject_name] = duration
        
        print(f"📊 考试时长数据加载完成: {len(self.subject_durations)} 个科目")
    
    def _load_time_slot_mapping(self):
        """加载科目时间段映射"""
        subject_records = self.config['data']['subject_settings']['records']
        self.subject_time_slots = {}  # 科目到时间段的映射
        self.time_slot_subjects = {"morning": [], "afternoon": [], "evening": []}  # 时间段到科目的映射
        self.subject_dates = {}  # 科目到日期的映射
        
        for record in subject_records:
            subject_name = record['课程名称']
            start_time_str = record['开始时间']
            
            # 解析开始时间
            start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
            hour = start_time.hour
            date_str = start_time.strftime("%Y-%m-%d")
            
            # 确定时间段
            if self.time_slots["morning"]["start"] <= hour < self.time_slots["morning"]["end"]:
                time_slot = "morning"
            elif self.time_slots["afternoon"]["start"] <= hour < self.time_slots["afternoon"]["end"]:
                time_slot = "afternoon"
            elif self.time_slots["evening"]["start"] <= hour < self.time_slots["evening"]["end"]:
                time_slot = "evening"
            else:
                time_slot = "morning"  # 默认为上午
            
            self.subject_time_slots[subject_name] = time_slot
            self.time_slot_subjects[time_slot].append(subject_name)
            self.subject_dates[subject_name] = date_str
        
        print(f"📅 时间段映射加载完成:")
        for slot, subjects in self.time_slot_subjects.items():
            print(f"   {slot}: {', '.join(subjects)}")
        
        # 构建时间段-日期组合
        self._build_time_slot_groups()
    
    def _build_time_slot_groups(self):
        """构建时间段-日期组合"""
        self.time_slot_groups = {}  # {(date, time_slot): [subject_indices]}
        
        for subject_name, time_slot in self.subject_time_slots.items():
            date = self.subject_dates[subject_name]
            subject_idx = self.subject_to_idx[subject_name]
            
            group_key = (date, time_slot)
            if group_key not in self.time_slot_groups:
                self.time_slot_groups[group_key] = []
            self.time_slot_groups[group_key].append(subject_idx)
        
        print(f"🕐 时间段组合构建完成: {len(self.time_slot_groups)}个时间段组合")
    
    def _build_demand_matrix(self):
        """构建需求矩阵"""
        self.demand_matrix = np.zeros(
            (self.n_rooms, self.n_subjects), dtype=int)
        
        for j, room in enumerate(self.rooms):
            for k, subject in enumerate(self.subjects):
                self.demand_matrix[j, k] = room.get(subject, 0)
        
        total_demand = np.sum(self.demand_matrix)
        print(f"📊 需求矩阵构建完成: 总监考需求 {total_demand} 人次")
    
    def _build_constraints(self):
        """构建约束条件"""
        self.proctor_limits = np.array([p['场次限制'] for p in self.proctors])
        self.priority_matrix = np.zeros((self.n_proctors, self.n_subjects, 7), dtype=int)
        
        self.must_subjects = {}
        self.forbidden_subjects = {}
        self.must_rooms_by_subject = {}  # 必监考考场：监考员i监考科目k时的必须考场
        self.forbidden_rooms_by_subject = {}  # 不监考考场：监考员i监考科目k时的避免考场
        self.bp_marks = {}  # 存储b+标记信息 {(i, k, j): 1}
        self.yp_marks = {}  # 存储y+标记信息 {(i, k, j): 1}

        # b+标记一致性检查
        bp_conflicts = []  # 存储b+标记冲突
        
        for i, proctor in enumerate(self.proctors):
            # 解析必监考科目和不监考科目约束
            must_subjects = self._parse_constraint_list(proctor.get('必监考科目', ''))
            self.must_subjects[i] = [self.subject_to_idx[s] for s in must_subjects if s in self.subject_to_idx]
            
            forbidden_subjects = self._parse_constraint_list(proctor.get('不监考科目', ''))
            self.forbidden_subjects[i] = [self.subject_to_idx[s] for s in forbidden_subjects if s in self.subject_to_idx]
            
            # 初始化基于科目的考场约束字典
            self.must_rooms_by_subject[i] = {}
            self.forbidden_rooms_by_subject[i] = {}

            # 解析基于科目的考场约束
            must_rooms = self._parse_constraint_list(proctor.get('必监考考场', ''))
            forbidden_rooms = self._parse_constraint_list(proctor.get('不监考考场', ''))

            # 为每个科目设置考场约束
            for k, subject in enumerate(self.subjects):
                mark = proctor.get(f'{subject}_监考', 'y')
                subject_idx = self.subject_to_idx[subject]

                # 处理b+标记（必监考科目才指定监考考场）
                if mark == 'b+' and must_rooms and subject_idx in self.must_subjects[i]:
                    for room_name in must_rooms:
                        if room_name in self.room_to_idx:
                            j = self.room_to_idx[room_name]
                            self.bp_marks[(i, k, j)] = 1

                # 处理y+标记（任何参加监考的科目都指定监考考场）
                elif mark == 'y+' and must_rooms and subject_idx not in self.forbidden_subjects[i]:
                    for room_name in must_rooms:
                        if room_name in self.room_to_idx:
                            j = self.room_to_idx[room_name]
                            self.yp_marks[(i, k, j)] = 1

                # 必监考考场约束（修正逻辑：只对b+科目设置必监考考场）
                subject_must_rooms = []
                if mark == 'b+' and must_rooms and subject_idx in self.must_subjects[i]:
                    # 只有当此科目是b+标记且在必监考科目中时，才设置必监考考场
                    subject_must_rooms = [self.room_to_idx[r] for r in must_rooms if r in self.room_to_idx]
                # 移除y+标记的必监考考场约束，避免冲突
                # elif mark == 'y+' and must_rooms and subject_idx not in self.forbidden_subjects[i]:
                #     # 对于y+标记的科目，也设置必监考考场约束（但权重会在目标函数中区别处理）
                #     subject_must_rooms = [self.room_to_idx[r] for r in must_rooms if r in self.room_to_idx]

                # 不监考考场约束（软约束）
                subject_forbidden_rooms = []
                if forbidden_rooms:  # 如果有不监考考场设置，应用到所有可监考的科目
                    subject_forbidden_rooms = [self.room_to_idx[r] for r in forbidden_rooms if r in self.room_to_idx]

                # 只对可监考的科目设置考场约束（不包括禁监考科目）
                if k not in self.forbidden_subjects.get(i, []):
                    self.must_rooms_by_subject[i][k] = subject_must_rooms
                    self.forbidden_rooms_by_subject[i][k] = subject_forbidden_rooms
            
            # 构建优先级矩阵
            for k, subject in enumerate(self.subjects):
                mark = proctor.get(f'{subject}_监考', 'y')
                level = self._get_priority_level(mark)
                if level >= 0:
                    self.priority_matrix[i, k, level] = 1
    
        # 检查b+标记冲突
        for j in range(self.n_rooms):
            for k in range(self.n_subjects):
                bp_count = sum(1 for (pi, pk, pj) in self.bp_marks if pj == j and pk == k)
                required = self.demand_matrix[j, k]
                if bp_count > required:
                    room_name = self.rooms[j]['考场']
                    subject_name = self.subjects[k]
                    bp_conflicts.append(f"考场{room_name}科目{subject_name}的b+标记数({bp_count})超过需求({required})")

        if bp_conflicts:
            print("\n⚠️ 发现b+标记冲突:")
            for conflict in bp_conflicts:
                print(f"  - {conflict}")
            raise ValueError("b+标记数量超过考场需求，请修正数据后重试")

    def _parse_constraint_list(self, constraint_str):
        """解析约束字符串"""
        if not constraint_str or constraint_str in ['0', 0, 'NaN', 'nan']:
            return []
        
        if isinstance(constraint_str, (int, float)) and constraint_str == 0:
            return []
        
        constraint_str = str(constraint_str).replace(
            '，', ',').replace('、', ',').replace(';', ',')
        items = [item.strip()
                 for item in constraint_str.split(',') if item.strip()]
        return [item for item in items if item not in ['0', 'NaN', 'nan']]
    
    def _get_priority_level(self, mark):
        """获取优先级层级"""
        mark_to_level = {
            'b+': 0, 'b': 1, 'b-': 2, 'y+': 3, 'y': 4, 'y-': 5, 'n': 6
        }
        return mark_to_level.get(mark, 4)
    
    def build_enhanced_model(self):
        """构建强化的CP-SAT模型"""
        if not ORTOOLS_AVAILABLE:
            raise ImportError("OR-Tools未安装")
        
        print("🔧 构建强化场次分配CP-SAT模型...")
        self.model = cp_model.CpModel()
        
        # 主要决策变量
        self.variables = {}
        for i in range(self.n_proctors):
            for j in range(self.n_rooms):
                for k in range(self.n_subjects):
                    var_name = f'x_{i}_{j}_{k}'
                    self.variables[(i, j, k)] = self.model.NewBoolVar(var_name)
        
        # 添加强化变量和约束
        self._add_enhanced_variables()
        self._add_hard_constraints()
        self._add_soft_constraints()
        self._add_utilization_constraints()
        self._build_enhanced_objective()
        
        print(f"✅ 强化CP-SAT模型构建完成")
    
    def _add_enhanced_variables(self):
        """添加强化变量"""
        self.utilization_vars = {}
        for i in range(self.n_proctors):
            max_sessions = self.proctor_limits[i]
            self.utilization_vars[f'actual_sessions_{i}'] = self.model.NewIntVar(
                0, max_sessions, f'actual_sessions_{i}')
            self.utilization_vars[f'unassigned_sessions_{i}'] = self.model.NewIntVar(
                0, max_sessions, f'unassigned_sessions_{i}')

        # 添加监考科目指示器变量（数学模型约束5）
        self.monitor_subject_vars = {}
        for i in range(self.n_proctors):
            for k in range(self.n_subjects):
                var_name = f'monitor_subject_{i}_{k}'
                self.monitor_subject_vars[(
                    i, k)] = self.model.NewBoolVar(var_name)
        
        self.min_sessions_vars = {}
        for i in range(self.n_proctors):
            limit = self.proctor_limits[i]
            if limit >= 3:
                min_required = max(2, limit // 2)
                self.min_sessions_vars[f'min_violation_{i}'] = self.model.NewIntVar(
                    0, min_required, f'min_violation_{i}')

        # 添加时间段相关变量（新增）
        self._add_time_slot_variables()
    
    def _add_time_slot_variables(self):
        """添加时间段相关变量"""
        self.time_slot_vars = {}
        
        # 为每个监考员在每个时间段-日期组合添加指示器变量
        for i in range(self.n_proctors):
            for group_key in self.time_slot_groups:
                date, time_slot = group_key
                var_name = f'proctor_{i}_timeslot_{date}_{time_slot}'
                self.time_slot_vars[(i, group_key)] = self.model.NewBoolVar(var_name)
        
        # 添加时间段内监考场次计数变量
        self.time_slot_session_vars = {}
        for i in range(self.n_proctors):
            for group_key in self.time_slot_groups:
                var_name = f'sessions_{i}_timeslot_{group_key[0]}_{group_key[1]}'
                max_sessions_in_slot = len(self.time_slot_groups[group_key])
                self.time_slot_session_vars[(i, group_key)] = self.model.NewIntVar(
                    0, max_sessions_in_slot, var_name)
    
    def _add_hard_constraints(self):
        """添加硬约束"""
        # 约束1：科目冲突约束
        for i in range(self.n_proctors):
            for k in range(self.n_subjects):
                self.model.Add(sum(self.variables[(i, j, k)] for j in range(self.n_rooms)) <= 1)

        # 约束2：场次平衡约束（修正版 - 移除b+特殊限制）
        for i in range(self.n_proctors):
            actual_sessions = sum(self.variables[(i, j, k)] for j in range(self.n_rooms) for k in range(self.n_subjects))
            limit = self.proctor_limits[i]
            self.model.Add(actual_sessions >= max(0, limit - 1))
            self.model.Add(actual_sessions <= limit + 1)

        # 约束3：禁监考科目约束
        for i in range(self.n_proctors):
            for k in self.forbidden_subjects.get(i, []):
                for j in range(self.n_rooms):
                    self.model.Add(self.variables[(i, j, k)] == 0)
                # 同时约束monitor_subject变量
                self.model.Add(self.monitor_subject_vars[(i, k)] == 0)

        # 约束4：b+标记强制分配约束（仅约束特定科目考场）
        for (i, k, j) in self.bp_marks:
            # b+标记位置强制分配
            self.model.Add(self.variables[(i, j, k)] == 1)

        # 约束5：y+标记考场约束（参加监考科目指定考场）
        for i in range(self.n_proctors):
            # 检查此监考员是否有b+标记
            has_bp = any((i, k, j) in self.bp_marks for k in range(self.n_subjects) for j in range(self.n_rooms))
            
            # 如果有b+标记，则不应用y+约束（避免冲突）
            if not has_bp:
                for k in range(self.n_subjects):
                    # 检查是否有y+标记的考场
                    yp_rooms = [j for (pi, pk, j) in self.yp_marks.keys() if pi == i and pk == k]
                    if yp_rooms:
                        # 如果监考这个科目，则必须在指定考场中选择
                        monitor_var = self.monitor_subject_vars[(i, k)]
                        non_yp_sessions = sum(self.variables[(i, j, k)] for j in range(self.n_rooms) if j not in yp_rooms)
                        self.model.Add(non_yp_sessions <= self.n_rooms * (1 - monitor_var))

        # 约束6：监考科目指示器约束
        for i in range(self.n_proctors):
            for k in range(self.n_subjects):
                sessions_in_subject = sum(self.variables[(i, j, k)] for j in range(self.n_rooms))
                monitor_var = self.monitor_subject_vars[(i, k)]

                # monitor_subject_{i,k} >= sum(x_{i,j,k})
                self.model.Add(monitor_var >= sessions_in_subject)
                # monitor_subject_{i,k} <= sum(x_{i,j,k}) * |J|
                self.model.Add(monitor_var <= sessions_in_subject * self.n_rooms)
    
    def _add_soft_constraints(self):
        """添加软约束"""
        self.slack_variables = {}

        # 约束7：考场人数平衡约束（软约束）- 修正版
        for j in range(self.n_rooms):
            for k in range(self.n_subjects):
                if self.demand_matrix[j, k] > 0:
                    # 计算b+标记在此位置的监考员数量
                    bp_count_here = sum(1 for (pi, pk, pj) in self.bp_marks if pj == j and pk == k)
                    
                    # 总需求量
                    total_demand = self.demand_matrix[j, k]
                    
                    # 去除b+标记后的剩余需求
                    remaining_demand = total_demand - bp_count_here
                    
                    if remaining_demand > 0:
                        # 只为剩余需求创建松弛变量
                        slack_under = self.model.NewIntVar(0, remaining_demand, f'slack_under_{j}_{k}')
                        slack_over = self.model.NewIntVar(0, self.n_proctors, f'slack_over_{j}_{k}')

                        self.slack_variables[(j, k, 'under')] = slack_under
                        self.slack_variables[(j, k, 'over')] = slack_over

                        # 计算此位置的非b+监考员分配（修复逻辑）
                        non_bp_assignments = []
                        bp_assignments_here = []
                        
                        for i in range(self.n_proctors):
                            # 检查监考员i是否在此位置有b+标记
                            has_bp_here = (i, k, j) in self.bp_marks
                            
                            if has_bp_here:
                                # 有b+标记且在此位置，已经通过硬约束强制分配
                                bp_assignments_here.append(1)  # b+标记必定分配
                            else:
                                # 其他所有监考员（包括有b+标记但不在此位置的）都参与软约束
                                non_bp_assignments.append(self.variables[(i, j, k)])

                        # 考场人数平衡约束：所有监考员分配 + 缺额 - 超额 = 总需求
                        # 即：非b+监考员分配 + b+监考员分配 + 缺额 - 超额 = 总需求
                        total_bp_here = sum(bp_assignments_here)
                        
                        if non_bp_assignments:
                            self.model.Add(
                                sum(non_bp_assignments) + total_bp_here + slack_under - slack_over == total_demand
                            )
                    elif remaining_demand == 0:
                        # 如果b+标记恰好满足需求，则此位置不能有其他监考员
                        for i in range(self.n_proctors):
                            if (i, k, j) not in self.bp_marks:
                                self.model.Add(self.variables[(i, j, k)] == 0)

        # 约束8：必监考科目约束（软约束）
        for i in range(self.n_proctors):
            for k in self.must_subjects.get(i, []):
                # 如果不是b+标记的科目，才添加软约束
                if not any((i, k, j) in self.bp_marks for j in range(self.n_rooms)):
                    slack_must = self.model.NewBoolVar(f'slack_must_{i}_{k}')
                    self.slack_variables[(i, k, 'must')] = slack_must

                    # monitor_subject_{i,k} + slack_must_{i,k} >= 1
                    self.model.Add(
                        self.monitor_subject_vars[(i, k)] + slack_must >= 1
                    )

        # 添加时间段约束（软约束）- 最后执行
        self._add_time_slot_constraints()
    
    def _add_time_slot_constraints(self):
        """添加时间段约束（软约束）"""
        # 如果策略设为none，则跳过时间段约束
        if self.schedule_strategy == "none":
            return
            
        # 构建时间段指示器约束
        for i in range(self.n_proctors):
            for group_key in self.time_slot_groups:
                date, time_slot = group_key
                subjects_in_group = self.time_slot_groups[group_key]
                
                # 计算此时间段内的监考场次数
                sessions_in_timeslot = sum(
                    self.variables[(i, j, k)] 
                    for j in range(self.n_rooms) 
                    for k in subjects_in_group
                )
                
                # 时间段内的场次数约束
                self.model.Add(
                    self.time_slot_session_vars[(i, group_key)] == sessions_in_timeslot
                )
                
                # 时间段指示器约束：如果有任何监考任务，则激活时间段
                max_sessions = len(subjects_in_group)
                
                # 修正：如果sessions_in_timeslot > 0，则time_slot_var = 1
                # 这等价于：time_slot_var >= sessions_in_timeslot 且 time_slot_var <= 1 当 sessions_in_timeslot > 0 时
                self.model.Add(
                    sessions_in_timeslot <= self.time_slot_vars[(i, group_key)] * max_sessions
                )
    
    def _add_utilization_constraints(self):
        """添加利用率约束"""
        for i in range(self.n_proctors):
            actual_sessions = sum(self.variables[(i, j, k)] 
                                for j in range(self.n_rooms) 
                                for k in range(self.n_subjects))
            
            self.model.Add(
                self.utilization_vars[f'actual_sessions_{i}'] == actual_sessions)
            self.model.Add(
                self.utilization_vars[f'unassigned_sessions_{i}'] == 
                self.proctor_limits[i] -
                self.utilization_vars[f'actual_sessions_{i}']
            )
            
            # 最小场次约束
            limit = self.proctor_limits[i]
            if limit >= 3 and f'min_violation_{i}' in self.min_sessions_vars:
                min_required = max(2, limit // 2)
                self.model.Add(
                    self.utilization_vars[f'actual_sessions_{i}'] +
                    self.min_sessions_vars[f'min_violation_{i}'] >= min_required
                )
    
    def _build_enhanced_objective(self):
        """构建强化目标函数"""
        objective_terms = []
        
        # 权重设置
        priority_weights = [1000, 500, 400, 100, 50, 10, -1000]
        room_penalty = 50000
        must_penalty = 30000
        utilization_reward = 20000  # 提高利用率奖励权重
        min_sessions_penalty = 25000  # 提高最小场次惩罚权重
        # 新增：考场选择优先级权重（数学模型中的4.2.4）
        must_room_reward = 8000     # 必监考考场奖励
        forbidden_room_penalty = 2000  # 不监考考场惩罚
        # 新增：场次平衡权重
        balance_penalty = 40000     # 场次平衡惩罚（高权重）
        
        # 分层优先级奖励（修正版：避免b+监考员获得y+奖励）
        for i in range(self.n_proctors):
            # 检查此监考员是否有任何b+标记
            has_any_bp = any((i, any_k, any_j) in self.bp_marks 
                           for any_k in range(self.n_subjects) 
                           for any_j in range(self.n_rooms))
            
            for j in range(self.n_rooms):
                for k in range(self.n_subjects):
                    for level in range(7):
                        if self.priority_matrix[i, k, level] == 1:
                            weight = priority_weights[level]
                            
                            # 特殊处理：如果监考员有b+标记，且当前科目不是b+科目，且当前是y+权重(level=3)
                            if has_any_bp and level == 3:  # level 3 对应 y+
                                # 检查此科目是否是该监考员的b+科目
                                has_bp_for_subject = any((i, k, any_j) in self.bp_marks for any_j in range(self.n_rooms))
                                if not has_bp_for_subject:
                                    # 对于有b+标记监考员的非b+科目，不给予y+权重奖励
                                    continue
                            
                            objective_terms.append(
                                self.variables[(i, j, k)] * weight)

        # 考场选择优先级奖励（数学模型中的F_room_preference）
        for i in range(self.n_proctors):
            for k in range(self.n_subjects):
                # 检查此监考员在此科目是否有b+标记
                has_bp_for_subject = any((i, k, j) in self.bp_marks for j in range(self.n_rooms))
                
                # 必监考考场奖励逻辑
                if k in self.must_rooms_by_subject.get(i, {}) and self.must_rooms_by_subject[i][k]:
                    for j in self.must_rooms_by_subject[i][k]:
                        if has_bp_for_subject:
                            # 对于b+科目，b+标记已经通过硬约束强制分配，不需要额外奖励
                            # 这里不添加奖励，避免重复奖励
                            pass
                        else:
                            # 对于非b+科目（包括y+科目），给予适度的必监考考场奖励
                            # 但检查此监考员是否有任何b+标记，如果有则不给予y+奖励
                            has_any_bp = any((i, any_k, any_j) in self.bp_marks 
                                           for any_k in range(self.n_subjects) 
                                           for any_j in range(self.n_rooms))
                            
                            if not has_any_bp:
                                # 只有完全没有b+标记的监考员才能获得y+必监考考场奖励
                                objective_terms.append(
                                    self.variables[(i, j, k)] * must_room_reward)

                # 不监考考场惩罚（软约束）
                if k in self.forbidden_rooms_by_subject.get(i, {}) and self.forbidden_rooms_by_subject[i][k]:
                    for j in self.forbidden_rooms_by_subject[i][k]:
                        objective_terms.append(
                            self.variables[(i, j, k)] * (-forbidden_room_penalty))
        
        # 松弛变量惩罚
        for key, var in self.slack_variables.items():
            if len(key) == 3 and key[2] == 'under':
                objective_terms.append(var * (-room_penalty))
            elif len(key) == 3 and key[2] == 'over':
                objective_terms.append(var * (-room_penalty // 2))
            elif len(key) == 3 and key[2] == 'must':
                objective_terms.append(var * (-must_penalty))
        
        # 强化利用率奖励
        for i in range(self.n_proctors):
            objective_terms.append(
                self.utilization_vars[f'actual_sessions_{i}'] * utilization_reward)
            objective_terms.append(
                self.utilization_vars[f'unassigned_sessions_{i}'] * (-utilization_reward))
        
        # 最小场次惩罚
        for i in range(self.n_proctors):
            if f'min_violation_{i}' in self.min_sessions_vars:
                objective_terms.append(
                    self.min_sessions_vars[f'min_violation_{i}'] * (-min_sessions_penalty))
        
        # 时间段偏好权重（新增）- 最后执行的软约束
        self._add_time_slot_preference_terms(objective_terms)
        
        if objective_terms:
            self.model.Maximize(sum(objective_terms))
    
    def _add_time_slot_preference_terms(self, objective_terms):
        """添加时间段偏好权重项（简化版本）"""
        # 如果策略设为none，则跳过时间段偏好
        if self.schedule_strategy == "none":
            return
            
        if self.schedule_strategy == "concentrated":
            # 集中监考：简化版本 - 直接奖励时间段内的场次数
            for i in range(self.n_proctors):
                for group_key in self.time_slot_groups:
                    # 时间段激活奖励
                    objective_terms.append(
                        self.time_slot_vars[(i, group_key)] * self.time_slot_preference_weight
                    )
                    
                    # 场次数线性奖励（避免复杂的平方线性化）
                    sessions_var = self.time_slot_session_vars[(i, group_key)]
                    objective_terms.append(
                        sessions_var * (self.time_slot_preference_weight // 2)
                    )
                        
        elif self.schedule_strategy == "distributed":
            # 分散监考：简化版本 - 惩罚时间段内的场次数
            for i in range(self.n_proctors):
                for group_key in self.time_slot_groups:
                    sessions_var = self.time_slot_session_vars[(i, group_key)]
                    
                    # 简单的线性惩罚：场次数越多惩罚越重
                    objective_terms.append(
                        sessions_var * (-self.time_slot_preference_weight // 2)
                    )
    
    def solve_enhanced_model(self, time_limit_seconds=600):
        """求解强化模型"""
        if not self.model:
            self.build_enhanced_model()
        
        print(f"🚀 开始强化求解 (时间限制: {time_limit_seconds}秒)...")
        
        self.solver = cp_model.CpSolver()
        self.solver.parameters.max_time_in_seconds = time_limit_seconds
        
        start_time = time.time()
        status = self.solver.Solve(self.model)
        solve_time = time.time() - start_time
        
        print(f"⏱️ 求解时间: {solve_time:.2f}秒")
        
        if status in [cp_model.OPTIMAL, cp_model.FEASIBLE]:
            print("✅ 找到解!")
            self._extract_enhanced_solution()
            return True
        else:
            print(f"❌ 求解失败: {self.solver.StatusName(status)}")
            return False
    
    def _extract_enhanced_solution(self):
        """提取解"""
        self.assignment = {p['监考老师']: [] for p in self.proctors}
        self.workload = {p['监考老师']: 0 for p in self.proctors}
        self.workload_durations = {p['监考老师']: 0 for p in self.proctors}
        
        for i in range(self.n_proctors):
            proctor_name = self.proctors[i]['监考老师']
            for j in range(self.n_rooms):
                for k in range(self.n_subjects):
                    if self.solver.Value(self.variables[(i, j, k)]) == 1:
                        room_name = self.rooms[j]['考场']
                        subject_name = self.subjects[k]
                        self.assignment[proctor_name].append(
                            f"{room_name}-{subject_name}")
                        self.workload[proctor_name] += 1
                        self.workload_durations[proctor_name] += self.subject_durations[subject_name]
        
        self._analyze_enhancement_results()
    
    def _analyze_enhancement_results(self):
        """分析强化效果"""
        total_assigned = sum(self.workload.values())
        total_capacity = sum(self.proctor_limits)
        utilization = (total_assigned /
                       total_capacity) if total_capacity > 0 else 0

        under_utilized = sum(1 for proctor_name, assigned in self.workload.items()
                             if assigned < self.proctor_limits[self.proctor_to_idx[proctor_name]])

        # 分析场次平衡情况
        balanced_proctors = 0
        total_proctors = len(self.proctors)
        unbalanced_details = []

        for proctor in self.proctors:
            name = proctor['监考老师']
            limit = proctor.get('场次限制', 0)
            actual = self.workload.get(name, 0)
            difference = abs(actual - limit)

            if difference <= 1:
                balanced_proctors += 1
            else:
                unbalanced_details.append(
                    f"{name}: 预设{limit}, 实际{actual}, 差值{actual-limit}")

        # 分析考场人数超额情况
        room_overstaff = 0
        room_understaff = 0
        room_perfect = 0
        overstaff_details = []

        for j, room in enumerate(self.rooms):
            room_name = room['考场']
            for k, subject in enumerate(self.subjects):
                required = self.demand_matrix[j, k]
                if required > 0:
                    assigned_count = sum(1 for assignments in self.assignment.values()
                                         for assign in assignments
                                         if assign == f"{room_name}-{subject}")

                    if assigned_count > required:
                        room_overstaff += 1
                        overstaff_details.append(
                            f"{room_name}-{subject}: 需要{required}人, 实际{assigned_count}人")
                    elif assigned_count < required:
                        room_understaff += 1
                    else:
                        room_perfect += 1

        # 专门分析15考场的详细情况
        print(f"\n🔍 15考场详细分析:")
        room_15_idx = None
        for j, room in enumerate(self.rooms):
            if room['考场'] == '15考场':
                room_15_idx = j
                break
        
        if room_15_idx is not None:
            print(f"   考场索引: {room_15_idx}")
            for k, subject in enumerate(self.subjects):
                required = self.demand_matrix[room_15_idx, k]
                if required > 0:
                    print(f"   {subject}: 需求{required}人")
                    
                    # 找出分配到15考场这个科目的监考员
                    assigned_proctors = []
                    for proctor_name, assignments in self.assignment.items():
                        for assign in assignments:
                            if assign == f"15考场-{subject}":
                                assigned_proctors.append(proctor_name)
                    
                    print(f"     实际分配: {len(assigned_proctors)}人")
                    for proctor_name in assigned_proctors:
                        proctor_idx = self.proctor_to_idx[proctor_name]
                        # 检查是否有b+标记（针对此科目）
                        has_bp = (proctor_idx, k, room_15_idx) in self.bp_marks
                        bp_mark = "(b+)" if has_bp else ""
                        print(f"       - {proctor_name} {bp_mark}")

        # 专门分析55考场的详细情况
        print(f"\n🔍 55考场详细分析:")
        room_55_idx = None
        for j, room in enumerate(self.rooms):
            if room['考场'] == '55考场':
                room_55_idx = j
                break
        
        if room_55_idx is not None:
            print(f"   考场索引: {room_55_idx}")
            for k, subject in enumerate(self.subjects):
                required = self.demand_matrix[room_55_idx, k]
                if required >= 0:  # 包括需求为0的科目
                    print(f"   {subject}: 需求{required}人")
                    
                    # 找出分配到55考场这个科目的监考员
                    assigned_proctors = []
                    for proctor_name, assignments in self.assignment.items():
                        for assign in assignments:
                            if assign == f"55考场-{subject}":
                                assigned_proctors.append(proctor_name)
                    
                    print(f"     实际分配: {len(assigned_proctors)}人")
                    for proctor_name in assigned_proctors:
                        proctor_idx = self.proctor_to_idx[proctor_name]
                        # 检查是否有b+标记（针对此科目）
                        has_bp = (proctor_idx, k, room_55_idx) in self.bp_marks
                        bp_mark = "(b+)" if has_bp else ""
                        print(f"       - {proctor_name} {bp_mark}")

        # 分析考场约束满足情况
        must_room_satisfied = 0
        must_room_total = 0
        forbidden_room_violations = 0
        forbidden_room_total = 0

        for proctor_name, assignments in self.assignment.items():
            i = self.proctor_to_idx[proctor_name]
            for assignment in assignments:
                room_name, subject_name = assignment.split('-')
                j = self.room_to_idx[room_name]
                k = self.subject_to_idx[subject_name]

                # 检查必监考考场约束满足情况
                if k in self.must_rooms_by_subject.get(i, {}) and self.must_rooms_by_subject[i][k]:
                    must_room_total += 1
                    if j in self.must_rooms_by_subject[i][k]:
                        must_room_satisfied += 1

                # 检查不监考考场约束违反情况
                if k in self.forbidden_rooms_by_subject.get(i, {}) and self.forbidden_rooms_by_subject[i][k]:
                    forbidden_room_total += 1
                    if j in self.forbidden_rooms_by_subject[i][k]:
                        forbidden_room_violations += 1

        print(f"📈 强化优化结果:")
        print(f"   实际利用率: {utilization:.2%}")
        print(f"   目标利用率: {self.utilization_target:.2%}")
        print(f"   未达预设值监考员: {under_utilized}人")
        print(f"   目标函数值: {self.solver.ObjectiveValue()}")

        print(f"🎯 场次平衡检查:")
        print(
            f"   平衡监考员: {balanced_proctors}/{total_proctors} ({balanced_proctors/total_proctors:.2%})")
        if unbalanced_details:
            print(
                f"   不平衡详情: {'; '.join(unbalanced_details[:3])}{'...' if len(unbalanced_details) > 3 else ''}")

        print(f"🏛️ 考场人数检查:")
        print(f"   人数完美匹配: {room_perfect}个")
        print(f"   人数不足: {room_understaff}个")
        print(f"   人数超额: {room_overstaff}个")
        if overstaff_details:
            print(
                f"   超额详情: {'; '.join(overstaff_details[:3])}{'...' if len(overstaff_details) > 3 else ''}")

        print(f"📊 考场约束满足情况:")
        if must_room_total > 0:
            print(
                f"   必监考考场满足率: {must_room_satisfied}/{must_room_total} ({must_room_satisfied/must_room_total:.2%})")
        else:
            print(f"   必监考考场约束: 无")
        if forbidden_room_total > 0:
            print(
                f"   不监考考场违反: {forbidden_room_violations}/{forbidden_room_total} ({forbidden_room_violations/forbidden_room_total:.2%})")
        else:
            print(f"   不监考考场约束: 无")

        # 添加时间段分析（新增）
        self._analyze_time_slot_distribution()

    def _analyze_time_slot_distribution(self):
        """分析时间段分配情况"""
        print(f"\n📅 时间段分配分析 (策略: {'集中监考' if self.schedule_strategy == 'concentrated' else '分散监考'}):")
        
        # 统计每个监考员的时间段分布
        proctor_time_slots = {}
        total_proctors_with_multi_slots = 0
        total_proctors_with_assignments = 0
        
        for proctor_name, assignments in self.assignment.items():
            if not assignments:
                continue
                
            total_proctors_with_assignments += 1
            i = self.proctor_to_idx[proctor_name]
            time_slots_used = set()
            time_slot_sessions = {}
            
            for assignment in assignments:
                room_name, subject_name = assignment.split('-')
                subject_time_slot = self.subject_time_slots[subject_name]
                subject_date = self.subject_dates[subject_name]
                group_key = (subject_date, subject_time_slot)
                
                time_slots_used.add(group_key)
                if group_key not in time_slot_sessions:
                    time_slot_sessions[group_key] = 0
                time_slot_sessions[group_key] += 1
            
            proctor_time_slots[proctor_name] = {
                'slots_used': len(time_slots_used),
                'time_slot_sessions': time_slot_sessions
            }
            
            if len(time_slots_used) > 1:
                total_proctors_with_multi_slots += 1
        
        # 整体统计
        if total_proctors_with_assignments > 0:
            multi_slot_rate = total_proctors_with_multi_slots / total_proctors_with_assignments
            print(f"   有监考任务的监考员: {total_proctors_with_assignments}人")
            print(f"   跨时间段监考员: {total_proctors_with_multi_slots}人 ({multi_slot_rate:.2%})")
            print(f"   单时间段监考员: {total_proctors_with_assignments - total_proctors_with_multi_slots}人")
        
        # 时间段集中度分析
        slot_concentration = {}
        for group_key in self.time_slot_groups:
            date, time_slot = group_key
            slot_name = f"{date} {time_slot}"
            concentrated_proctors = 0
            total_sessions_in_slot = 0
            
            for proctor_name in proctor_time_slots:
                sessions_in_this_slot = proctor_time_slots[proctor_name]['time_slot_sessions'].get(group_key, 0)
                if sessions_in_this_slot > 0:
                    total_sessions_in_slot += sessions_in_this_slot
                    if sessions_in_this_slot > 1:
                        concentrated_proctors += 1
            
            slot_concentration[slot_name] = {
                'total_sessions': total_sessions_in_slot,
                'concentrated_proctors': concentrated_proctors
            }
        
        print(f"\n   时间段详细分析:")
        for slot_name, stats in slot_concentration.items():
            total_sessions = stats['total_sessions']
            concentrated = stats['concentrated_proctors']
            if total_sessions > 0:
                print(f"     {slot_name}: {total_sessions}场次, {concentrated}人集中监考")
        
        # 策略效果评估
        if self.schedule_strategy == "concentrated":
            # 集中监考效果：单时间段监考员比例越高越好
            single_slot_rate = (total_proctors_with_assignments - total_proctors_with_multi_slots) / total_proctors_with_assignments if total_proctors_with_assignments > 0 else 0
            print(f"\n   集中监考效果评估:")
            print(f"     单时间段监考率: {single_slot_rate:.2%} (越高越好)")
            if single_slot_rate >= 0.7:
                print(f"     评估结果: ✅ 集中效果良好")
            elif single_slot_rate >= 0.5:
                print(f"     评估结果: ⚠️ 集中效果一般")
            else:
                print(f"     评估结果: ❌ 集中效果较差")
        else:
            # 分散监考效果：跨时间段监考员比例越高越好
            print(f"\n   分散监考效果评估:")
            print(f"     跨时间段监考率: {multi_slot_rate:.2%} (越高越好)")
            if multi_slot_rate >= 0.5:
                print(f"     评估结果: ✅ 分散效果良好")
            elif multi_slot_rate >= 0.3:
                print(f"     评估结果: ⚠️ 分散效果一般")
            else:
                print(f"     评估结果: ❌ 分散效果较差")

    def export_enhanced_results(self):
        """导出结果"""
        if not self.assignment:
            print("❌ 没有结果可导出")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"监考安排_{timestamp}.xlsx"
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 考场安排表
                room_df = self._create_room_arrangement_df()
                room_df.to_excel(writer, sheet_name='考场安排', index=False)
                
                # 监考员安排表
                proctor_df = self._create_enhanced_proctor_df()
                proctor_df.to_excel(writer, sheet_name='监考员安排', index=False)
                
                # 未安排场次表
                unassigned_df = self._create_unassigned_df()
                unassigned_df.to_excel(writer, sheet_name='未安排场次', index=False)
                
                # 统计信息表
                stats_df = self._create_statistics_df()
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            print(f"✅ 监考安排结果已导出到: {filename}")
            
        except Exception as e:
            print(f"❌ 导出失败: {str(e)}")
    
    def _create_room_arrangement_df(self):
        """创建考场安排数据框"""
        data = []
        
        # 按考场组织数据
        for room in self.rooms:
            room_name = room['考场']
            row = {'考场': room_name}
            
            # 为每个科目找到分配的监考员
            for subject in self.subjects:
                assigned_proctors = []
                for proctor_name, assignments in self.assignment.items():
                    for assign in assignments:
                        if assign == f"{room_name}-{subject}":
                            assigned_proctors.append(proctor_name)
                
                # 根据需求数量判断是否完整分配
                required = room.get(subject, 0)
                if required > 0:
                    if len(assigned_proctors) == required:
                        row[subject] = ', '.join(assigned_proctors)
                    elif len(assigned_proctors) < required:
                        row[subject] = ', '.join(
                            assigned_proctors) + f" (缺{required - len(assigned_proctors)}人)"
                    else:
                        row[subject] = ', '.join(
                            assigned_proctors) + f" (超{len(assigned_proctors) - required}人)"
                else:
                    row[subject] = ''
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def _create_enhanced_proctor_df(self):
        """创建监考员安排数据框"""
        data = []
        for proctor in self.proctors:
            name = proctor['监考老师']
            limit = proctor.get('场次限制', 0)
            actual = self.workload.get(name, 0)
            
            row = {
                '监考老师': name,
                '必监考科目': proctor.get('必监考科目', ''),
                '不监考科目': proctor.get('不监考科目', ''),
                '必监考考场': proctor.get('必监考考场', ''),
                '不监考考场': proctor.get('不监考考场', ''),
                '场次限制': limit,
                '实际安排次数': actual,
                '未安排场次': max(0, limit - actual),
                '总监考时长(分钟)': self.workload_durations.get(name, 0),
                '利用率(%)': round((actual / limit * 100), 2) if limit > 0 else 0
            }
            
            # 各科目分配的考场
            for subject in self.subjects:
                assigned_rooms = []
                for assign in self.assignment.get(name, []):
                    if assign.endswith(f'-{subject}'):
                        room = assign.split('-')[0]
                        assigned_rooms.append(room)
                row[subject] = ', '.join(
                    assigned_rooms) if assigned_rooms else ''
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def _create_unassigned_df(self):
        """创建未安排数据框"""
        data = []
        for proctor in self.proctors:
            name = proctor['监考老师']
            limit = proctor.get('场次限制', 0)
            actual = self.workload.get(name, 0)
            unassigned = max(0, limit - actual)
            
            if unassigned > 0:
                data.append({
                    '监考老师': name,
                    '预期安排次数': limit,
                    '实际安排次数': actual,
                    '未安排场次': unassigned,
                    '利用率(%)': round((actual / limit * 100), 2) if limit > 0 else 0
                })
        
        df = pd.DataFrame(data)
        if not df.empty:
            df = df.sort_values('未安排场次', ascending=False)
        
        return df
    
    def _create_statistics_df(self):
        """创建统计信息数据框"""
        # 基础统计
        total_proctors = len(self.proctors)
        total_subjects = len(self.subjects)
        total_rooms = len(self.rooms)
        
        # 计算需求场次和实际安排场次
        total_demand = np.sum(self.demand_matrix)
        total_assigned = sum(self.workload.values())
        
        # 计算完成率
        completion_rate = (total_assigned / total_demand *
                           100) if total_demand > 0 else 0
        
        # 计算利用率
        total_capacity = sum(self.proctor_limits)
        utilization_rate = (total_assigned / total_capacity *
                            100) if total_capacity > 0 else 0
        
        # 分配状况统计
        fully_assigned = sum(1 for p in self.proctors 
                           if self.workload.get(p['监考老师'], 0) == p.get('场次限制', 0))
        partially_assigned = sum(1 for p in self.proctors 
                               if 0 < self.workload.get(p['监考老师'], 0) < p.get('场次限制', 0))
        unassigned = sum(1 for p in self.proctors 
                        if self.workload.get(p['监考老师'], 0) == 0)
        
        # 未满足需求的考场-科目组合
        unmet_demands = 0
        for j in range(self.n_rooms):
            for k in range(self.n_subjects):
                required = self.demand_matrix[j, k]
                if required > 0:
                    room_name = self.rooms[j]['考场']
                    subject_name = self.subjects[k]
                    assigned_count = sum(1 for assignments in self.assignment.values()
                                       for assign in assignments
                                       if assign == f"{room_name}-{subject_name}")
                    if assigned_count < required:
                        unmet_demands += 1
        
        data = [
            {'统计项目': '监考员总数', '数值': total_proctors, '单位': '人'},
            {'统计项目': '考试科目总数', '数值': total_subjects, '单位': '门'},
            {'统计项目': '考场总数', '数值': total_rooms, '单位': '个'},
            {'统计项目': '需求场次总数', '数值': total_demand, '单位': '人次'},
            {'统计项目': '实际安排场次', '数值': total_assigned, '单位': '人次'},
            {'统计项目': '完成率', '数值': round(completion_rate, 2), '单位': '%'},
            {'统计项目': '总容量', '数值': total_capacity, '单位': '人次'},
            {'统计项目': '利用率', '数值': round(utilization_rate, 2), '单位': '%'},
            {'统计项目': '完全分配监考员', '数值': fully_assigned, '单位': '人'},
            {'统计项目': '部分分配监考员', '数值': partially_assigned, '单位': '人'},
            {'统计项目': '未分配监考员', '数值': unassigned, '单位': '人'},
            {'统计项目': '未满足需求组合', '数值': unmet_demands, '单位': '个'}
        ]
        
        return pd.DataFrame(data)

    def _print_room_constraints_summary(self):
        """打印考场约束配置概览"""
        must_room_constraints = 0
        forbidden_room_constraints = 0

        for i in range(self.n_proctors):
            proctor_name = self.proctors[i]['监考老师']
            for k in range(self.n_subjects):
                subject_name = self.subjects[k]

                if k in self.must_rooms_by_subject.get(i, {}) and self.must_rooms_by_subject[i][k]:
                    must_room_constraints += 1

                if k in self.forbidden_rooms_by_subject.get(i, {}) and self.forbidden_rooms_by_subject[i][k]:
                    forbidden_room_constraints += 1

        print(f"🏛️ 考场约束配置:")
        print(f"   必监考考场约束: {must_room_constraints}个 (硬约束)")
        print(f"   不监考考场约束: {forbidden_room_constraints}个 (软约束)")


def main():
    """主函数"""
    if not ORTOOLS_AVAILABLE:
        print("❌ 请先安装OR-Tools: pip install ortools")
        return
    
    print("🚀 强化场次分配优化器（支持监考时间策略）")
    print("=" * 60)
    
    scheduler = EnhancedProctorScheduler()
    
    # 监考时间策略设置示例
    print("\n⚙️ 监考时间策略配置:")
    print("   当前策略: 集中监考 (默认)")
    print("   可选策略:")
    print("     - 集中监考: scheduler.set_schedule_strategy('concentrated', 5000)")
    print("     - 分散监考: scheduler.set_schedule_strategy('distributed', 5000)")
    print("   💡 如需更改策略，请在求解前调用set_schedule_strategy方法")
    
    # 可选：取消注释下面的行来设置为分散监考
    # scheduler.set_schedule_strategy("distributed", 5000)
    
    print("\n🔧 开始强化求解")
    if not scheduler.solve_enhanced_model(time_limit_seconds=600):
        print("❌ 强化求解失败")
        return
    
    print("\n📄 导出强化结果")
    scheduler.export_enhanced_results()
    
    print("\n🎯 强化优化完成!")
    print("\n📚 使用说明:")
    print("   1. 默认策略为'集中监考'，有助于减少监考员往返学校次数")
    print("   2. 如需改为'分散监考'，请在代码中调用scheduler.set_schedule_strategy('distributed')")
    print("   3. 权重值越大，时间策略的影响越强（建议范围：1000-10000）")
    print("   4. 时间段分析结果已包含在控制台输出中")


def demo_different_strategies():
    """演示不同监考策略的效果"""
    if not ORTOOLS_AVAILABLE:
        print("❌ 请先安装OR-Tools: pip install ortools")
        return
    
    strategies = [
        ("concentrated", "集中监考"),
        ("distributed", "分散监考")
    ]
    
    print("🔬 监考策略对比演示")
    print("=" * 60)
    
    for strategy, strategy_name in strategies:
        print(f"\n🧪 测试策略: {strategy_name}")
        print("-" * 40)
        
        scheduler = EnhancedProctorScheduler()
        scheduler.set_schedule_strategy(strategy, 5000)
        
        if scheduler.solve_enhanced_model(time_limit_seconds=300):
            print(f"✅ {strategy_name}策略求解成功")
            # 这里可以保存结果到不同文件进行对比
            # filename = f"监考安排_{strategy}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        else:
            print(f"❌ {strategy_name}策略求解失败")


def test_strategy_comparison():
    """测试监考策略对比"""
    if not ORTOOLS_AVAILABLE:
        print("❌ 请先安装OR-Tools: pip install ortools")
        return
    
    print("🔬 监考策略效果对比测试")
    print("=" * 60)
    
    results = {}
    
    for strategy in ["concentrated", "distributed"]:
        strategy_name = "集中监考" if strategy == "concentrated" else "分散监考"
        print(f"\n🧪 测试策略: {strategy_name}")
        print("-" * 40)
        
        scheduler = EnhancedProctorScheduler()
        scheduler.set_schedule_strategy(strategy, 8000)  # 提高权重
        
        if scheduler.solve_enhanced_model(time_limit_seconds=300):
            print(f"✅ {strategy_name}策略求解成功")
            
            # 收集关键指标
            total_assigned = sum(scheduler.workload.values())
            total_proctors_with_assignments = sum(1 for w in scheduler.workload.values() if w > 0)
            
            # 计算时间段分布
            proctor_time_slots = {}
            multi_slot_count = 0
            
            for proctor_name, assignments in scheduler.assignment.items():
                if not assignments:
                    continue
                    
                time_slots_used = set()
                for assignment in assignments:
                    _, subject_name = assignment.split('-')
                    subject_time_slot = scheduler.subject_time_slots[subject_name]
                    subject_date = scheduler.subject_dates[subject_name]
                    group_key = (subject_date, subject_time_slot)
                    time_slots_used.add(group_key)
                
                if len(time_slots_used) > 1:
                    multi_slot_count += 1
            
            single_slot_rate = (total_proctors_with_assignments - multi_slot_count) / total_proctors_with_assignments if total_proctors_with_assignments > 0 else 0
            multi_slot_rate = multi_slot_count / total_proctors_with_assignments if total_proctors_with_assignments > 0 else 0
            
            results[strategy] = {
                'total_assigned': total_assigned,
                'single_slot_rate': single_slot_rate,
                'multi_slot_rate': multi_slot_rate,
                'objective_value': scheduler.solver.ObjectiveValue()
            }
            
            print(f"   总分配场次: {total_assigned}")
            print(f"   单时间段监考率: {single_slot_rate:.2%}")
            print(f"   跨时间段监考率: {multi_slot_rate:.2%}")
            print(f"   目标函数值: {scheduler.solver.ObjectiveValue()}")
            
        else:
            print(f"❌ {strategy_name}策略求解失败")
    
    # 对比分析
    if len(results) == 2:
        print(f"\n📊 策略效果对比:")
        print("-" * 40)
        
        concentrated = results["concentrated"]
        distributed = results["distributed"]
        
        print(f"集中监考策略:")
        print(f"   单时间段监考率: {concentrated['single_slot_rate']:.2%} (集中监考的目标指标)")
        print(f"   目标函数值: {concentrated['objective_value']}")
        
        print(f"分散监考策略:")
        print(f"   跨时间段监考率: {distributed['multi_slot_rate']:.2%} (分散监考的目标指标)")
        print(f"   目标函数值: {distributed['objective_value']}")
        
        print(f"\n💡 结论:")
        if concentrated['single_slot_rate'] > distributed['single_slot_rate']:
            print(f"   集中监考策略在减少往返次数方面更有效")
        else:
            print(f"   分散监考策略在当前约束下表现更好")
            
        if distributed['multi_slot_rate'] > concentrated['multi_slot_rate']:
            print(f"   分散监考策略在分散工作负荷方面更有效")
        else:
            print(f"   集中监考策略意外地提供了更好的分散效果")


if __name__ == "__main__":
    main()
    
    # 取消注释下面的行来运行策略对比演示
    # demo_different_strategies()
    
    # 取消注释下面的行来运行详细的策略对比测试
    # test_strategy_comparison() 
