#!/usr/bin/env python3
"""
测试科目页面Excel导入功能修复
验证应用step2_rooms正常工作代码后的效果
"""

import sys
import os

def test_backend_import_function():
    """测试后端导入函数"""
    print("🧪 测试科目导入函数...")
    
    try:
        sys.path.append('.')
        from app import import_subjects_from_excel
        print("✅ 成功导入应用模块")
    except ImportError as e:
        print(f"❌ 无法导入应用模块: {e}")
        return False
    
    template_file = 'template-guide/kemu.xlsx'
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        subjects = import_subjects_from_excel(template_file)
        print(f"✅ 科目导入成功")
        print(f"   导入数据数量: {len(subjects)}")
        
        if len(subjects) > 0:
            print(f"   示例数据: {subjects[0]}")
            
            # 验证数据格式
            required_fields = ['subject_code', 'subject_name', 'start_time', 'end_time']
            first_subject = subjects[0]
            
            missing_fields = [field for field in required_fields if field not in first_subject]
            if missing_fields:
                print(f"   ⚠️  缺少字段: {missing_fields}")
                return False
            else:
                print(f"   ✅ 数据格式正确")
                return True
        else:
            print(f"   ⚠️  没有导入任何数据")
            return False
            
    except Exception as e:
        print(f"❌ 科目导入失败: {e}")
        return False

def check_template_structure():
    """检查模板文件结构"""
    print("\n📁 检查科目模板文件结构...")
    
    import pandas as pd
    
    template_file = 'template-guide/kemu.xlsx'
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(template_file)
        print(f"工作表: {excel_file.sheet_names}")
        
        # 读取第一个工作表
        df = pd.read_excel(template_file, sheet_name=excel_file.sheet_names[0])
        print(f"列名: {list(df.columns)}")
        print(f"数据行数: {len(df)}")
        
        # 检查必需的列
        required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ 缺少必需的列: {missing_columns}")
            return False
        else:
            print(f"✅ 模板格式正确")
            
        # 显示示例数据
        if len(df) > 0:
            print("示例数据:")
            for i, row in df.head(3).iterrows():
                print(f"  {i+1}. {row['课程代码']} - {row['课程名称']} ({row['开始时间']} ~ {row['结束时间']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取模板文件失败: {e}")
        return False

def check_page_template():
    """检查页面模板代码"""
    print("\n📄 检查科目页面模板代码...")
    
    template_file = 'templates/wizard/step1_subjects.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码片段
        checks = [
            ('handleSubjectFileUpload', '科目文件处理函数'),
            ('科目页面：导入按钮被点击', '导入按钮点击日志'),
            ('科目页面：文件已选择', '文件选择日志'),
            ('科目页面：开始处理文件', '文件处理开始日志'),
            ('wizard_import_excel", step_type="subjects"', '导入URL'),
            ('subjects = response.data', '数据更新'),
            ('updateSubjectsData()', '隐藏字段更新')
        ]
        
        all_present = True
        for pattern, description in checks:
            if pattern in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ 缺少: {description}")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ 读取页面模板失败: {e}")
        return False

def compare_with_rooms_page():
    """与考场页面代码进行对比"""
    print("\n🔍 与考场页面代码对比...")
    
    try:
        # 读取两个页面的代码
        with open('templates/wizard/step1_subjects.html', 'r', encoding='utf-8') as f:
            subjects_content = f.read()
        
        with open('templates/wizard/step2_rooms.html', 'r', encoding='utf-8') as f:
            rooms_content = f.read()
        
        # 检查关键模式是否一致
        patterns = [
            ('动态创建文件输入', 'document.createElement(\'input\')'),
            ('文件类型检查', 'allowedTypes.includes(file.type)'),
            ('文件大小检查', 'file.size > 5 * 1024 * 1024'),
            ('CSRF令牌', 'csrf_token'),
            ('Ajax请求', '$.ajax({'),
            ('成功处理', 'response.success'),
            ('错误处理', 'JSON.parse(xhr.responseText)')
        ]
        
        all_consistent = True
        for description, pattern in patterns:
            subjects_has = pattern in subjects_content
            rooms_has = pattern in rooms_content
            
            if subjects_has and rooms_has:
                print(f"   ✅ {description}: 两页面都有")
            elif not subjects_has and not rooms_has:
                print(f"   ⚠️  {description}: 两页面都没有")
            else:
                print(f"   ❌ {description}: 不一致 (科目:{subjects_has}, 考场:{rooms_has})")
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 科目页面Excel导入功能修复验证")
    print("=" * 50)
    print("参考step2_rooms页面的正常工作代码进行修复")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查模板文件结构
    test_results.append(("模板文件结构", check_template_structure()))
    
    # 2. 测试后端导入函数
    test_results.append(("后端导入函数", test_backend_import_function()))
    
    # 3. 检查页面模板代码
    test_results.append(("页面模板代码", check_page_template()))
    
    # 4. 与考场页面对比
    test_results.append(("与考场页面对比", compare_with_rooms_page()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 验证结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项验证通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有验证通过！")
        print("科目页面Excel导入功能已成功应用step2_rooms的正常工作代码。")
        print("\n📋 修复内容:")
        print("✅ 使用与考场页面相同的动态文件输入方法")
        print("✅ 统一的文件处理函数命名和日志")
        print("✅ 一致的数据更新和渲染逻辑")
        print("✅ 相同的错误处理机制")
        return 0
    else:
        print("\n❌ 部分验证失败，请检查相关代码。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
