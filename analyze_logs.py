#!/usr/bin/env python3
"""
分析运行日志
"""

import pandas as pd
import os
import re
from datetime import datetime
from collections import defaultdict, Counter

def analyze_app_log():
    """分析主应用日志"""
    print("=== 主应用日志分析 ===")
    
    if not os.path.exists('app.log'):
        print("app.log 文件不存在")
        return
    
    with open('app.log', 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    print(f"总日志行数: {len(lines)}")
    
    # 统计日志级别
    log_levels = Counter()
    error_count = 0
    warning_count = 0
    recent_errors = []
    
    for line in lines:
        if ' - ERROR - ' in line:
            error_count += 1
            log_levels['ERROR'] += 1
            recent_errors.append(line.strip())
        elif ' - WARNING - ' in line:
            warning_count += 1
            log_levels['WARNING'] += 1
        elif ' - INFO - ' in line:
            log_levels['INFO'] += 1
    
    print(f"\n日志级别统计:")
    for level, count in log_levels.most_common():
        print(f"  {level}: {count}")
    
    # 分析最近的活动
    recent_lines = lines[-50:] if len(lines) > 50 else lines
    print(f"\n最近活动分析 (最后{len(recent_lines)}行):")
    
    # 提取最近的请求
    requests = []
    for line in recent_lines:
        if 'HTTP/1.1' in line and ('GET' in line or 'POST' in line):
            # 提取请求信息
            match = re.search(r'"([A-Z]+) ([^"]+) HTTP/1.1"', line)
            if match:
                method, path = match.groups()
                requests.append(f"{method} {path}")
    
    if requests:
        print("  最近的HTTP请求:")
        for req in requests[-10:]:  # 显示最后10个请求
            print(f"    {req}")
    
    # 显示最近的错误
    if recent_errors:
        print(f"\n最近的错误 (最后{min(5, len(recent_errors))}个):")
        for error in recent_errors[-5:]:
            print(f"  {error}")
    
    return {
        'total_lines': len(lines),
        'errors': error_count,
        'warnings': warning_count,
        'recent_requests': requests[-10:] if requests else []
    }

def analyze_scheduler_log():
    """分析调度器日志"""
    print("\n=== 监考安排调度器日志分析 ===")
    
    if not os.path.exists('invigilator_scheduler.log'):
        print("invigilator_scheduler.log 文件不存在")
        return
    
    with open('invigilator_scheduler.log', 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    print(f"总日志行数: {len(lines)}")
    
    # 分析调度任务
    tasks = []
    current_task = None
    
    for line in lines:
        line = line.strip()
        if '处理进度: 0%' in line:
            # 新任务开始
            timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if timestamp_match:
                current_task = {
                    'start_time': timestamp_match.group(1),
                    'status': 'started',
                    'data_loaded': False,
                    'solution_found': False,
                    'solve_time': None
                }
        elif current_task and '成功加载数据:' in line:
            # 提取数据信息
            match = re.search(r'(\d+)位教师, (\d+)个科目, (\d+)个考场', line)
            if match:
                current_task['teachers'] = int(match.group(1))
                current_task['subjects'] = int(match.group(2))
                current_task['rooms'] = int(match.group(3))
                current_task['data_loaded'] = True
        elif current_task and '找到可行解！求解时间：' in line:
            # 提取求解时间
            match = re.search(r'求解时间：([\d.]+)秒', line)
            if match:
                current_task['solve_time'] = float(match.group(1))
                current_task['solution_found'] = True
        elif current_task and '处理进度: 100%' in line:
            # 任务完成
            current_task['status'] = 'completed'
            tasks.append(current_task)
            current_task = None
    
    print(f"\n调度任务统计:")
    print(f"  总任务数: {len(tasks)}")
    
    if tasks:
        successful_tasks = [t for t in tasks if t.get('solution_found', False)]
        print(f"  成功完成: {len(successful_tasks)}")
        
        if successful_tasks:
            solve_times = [t['solve_time'] for t in successful_tasks if t.get('solve_time')]
            if solve_times:
                avg_solve_time = sum(solve_times) / len(solve_times)
                print(f"  平均求解时间: {avg_solve_time:.2f}秒")
                print(f"  最快求解时间: {min(solve_times):.2f}秒")
                print(f"  最慢求解时间: {max(solve_times):.2f}秒")
        
        # 显示最近的任务
        recent_task = tasks[-1] if tasks else None
        if recent_task:
            print(f"\n最近任务信息:")
            print(f"  开始时间: {recent_task.get('start_time', 'N/A')}")
            print(f"  教师数量: {recent_task.get('teachers', 'N/A')}")
            print(f"  科目数量: {recent_task.get('subjects', 'N/A')}")
            print(f"  考场数量: {recent_task.get('rooms', 'N/A')}")
            print(f"  求解时间: {recent_task.get('solve_time', 'N/A')}秒")
            print(f"  状态: {recent_task.get('status', 'N/A')}")
    
    return {
        'total_tasks': len(tasks),
        'successful_tasks': len([t for t in tasks if t.get('solution_found', False)]),
        'recent_task': tasks[-1] if tasks else None
    }

def analyze_error_reports():
    """分析错误报告文件"""
    print("\n=== 错误报告分析 ===")
    
    # 查找错误报告文件
    error_files = [f for f in os.listdir('.') if f.startswith('error_report_') and f.endswith('.xlsx')]
    
    if not error_files:
        print("没有找到错误报告文件")
        return
    
    print(f"找到 {len(error_files)} 个错误报告文件")
    
    # 分析最新的错误报告
    latest_file = sorted(error_files)[-1]
    print(f"最新错误报告: {latest_file}")
    
    try:
        df = pd.read_excel(latest_file)
        print(f"错误记录数: {len(df)}")
        
        if len(df) > 0:
            print("列名:", list(df.columns))
            
            # 统计错误类型
            if '错误类型' in df.columns:
                error_types = df['错误类型'].value_counts()
                print("\n错误类型统计:")
                for error_type, count in error_types.items():
                    print(f"  {error_type}: {count}次")
            
            # 显示最近的错误
            print(f"\n最近的错误记录 (最多5条):")
            for i, row in df.head().iterrows():
                print(f"  {i+1}. ", end="")
                for col in df.columns:
                    print(f"{col}: {row[col]}, ", end="")
                print()
        
    except Exception as e:
        print(f"读取错误报告时出错: {e}")
    
    return {
        'error_files_count': len(error_files),
        'latest_file': latest_file
    }

def generate_summary(app_stats, scheduler_stats, error_stats):
    """生成总结报告"""
    print("\n" + "="*60)
    print("=== 系统运行状况总结 ===")
    print("="*60)
    
    print(f"\n📊 应用状态:")
    print(f"  • 日志总行数: {app_stats['total_lines']:,}")
    print(f"  • 错误数量: {app_stats['errors']}")
    print(f"  • 警告数量: {app_stats['warnings']}")
    
    print(f"\n🔧 监考安排调度器:")
    print(f"  • 总任务数: {scheduler_stats['total_tasks']}")
    print(f"  • 成功任务数: {scheduler_stats['successful_tasks']}")
    
    if scheduler_stats['recent_task']:
        recent = scheduler_stats['recent_task']
        print(f"  • 最近任务: {recent.get('teachers', 'N/A')}位教师, {recent.get('subjects', 'N/A')}个科目, {recent.get('rooms', 'N/A')}个考场")
        if recent.get('solve_time'):
            print(f"  • 最近求解时间: {recent['solve_time']:.2f}秒")
    
    print(f"\n⚠️  错误报告:")
    print(f"  • 错误报告文件数: {error_stats['error_files_count']}")
    if error_stats['latest_file']:
        print(f"  • 最新报告: {error_stats['latest_file']}")
    
    # 系统健康状况评估
    print(f"\n🏥 系统健康状况:")
    
    health_score = 100
    issues = []
    
    if app_stats['errors'] > 10:
        health_score -= 20
        issues.append(f"应用错误较多 ({app_stats['errors']}个)")
    
    if scheduler_stats['total_tasks'] > 0:
        success_rate = scheduler_stats['successful_tasks'] / scheduler_stats['total_tasks']
        if success_rate < 0.8:
            health_score -= 30
            issues.append(f"调度成功率较低 ({success_rate:.1%})")
    
    if error_stats['error_files_count'] > 5:
        health_score -= 10
        issues.append(f"错误报告文件较多 ({error_stats['error_files_count']}个)")
    
    if health_score >= 90:
        status = "🟢 优秀"
    elif health_score >= 70:
        status = "🟡 良好"
    elif health_score >= 50:
        status = "🟠 一般"
    else:
        status = "🔴 需要关注"
    
    print(f"  • 健康评分: {health_score}/100 {status}")
    
    if issues:
        print(f"  • 发现的问题:")
        for issue in issues:
            print(f"    - {issue}")
    else:
        print(f"  • 系统运行正常，未发现明显问题")

def main():
    print("开始分析运行日志...")
    
    # 分析各个日志文件
    app_stats = analyze_app_log()
    scheduler_stats = analyze_scheduler_log()
    error_stats = analyze_error_reports()
    
    # 生成总结
    generate_summary(app_stats, scheduler_stats, error_stats)

if __name__ == '__main__':
    main()
