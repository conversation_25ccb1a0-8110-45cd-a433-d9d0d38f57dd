#!/usr/bin/env python3
"""
测试批量添加考场功能
验证考场页面的批量添加功能是否正常工作
"""

import os

def check_batch_add_ui():
    """检查批量添加UI组件"""
    print("🔍 检查批量添加UI组件...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查UI组件
        ui_checks = {
            '批量添加按钮': 'id="batch-add-room-btn"' in content,
            '批量添加模态框': 'id="batchAddModal"' in content,
            '考场数量输入': 'id="room-count"' in content,
            '考场前缀输入': 'id="room-prefix"' in content,
            '起始编号输入': 'id="start-number"' in content,
            '默认监考人数设置': 'default-demand' in content,
            '预览文本': 'id="preview-text"' in content,
            '确认添加按钮': 'id="confirm-batch-add"' in content
        }
        
        all_passed = True
        for check_name, result in ui_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_batch_add_javascript():
    """检查批量添加JavaScript功能"""
    print("\n🔍 检查批量添加JavaScript功能...")
    
    template_file = 'templates/wizard/step2_rooms.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查JavaScript功能
        js_checks = {
            '预览更新函数': 'function updateBatchAddPreview()' in content,
            '事件监听器': '#room-count, #room-prefix, #start-number' in content,
            '确认添加处理': '#confirm-batch-add' in content,
            '数据收集': 'collectDataFromDOM()' in content,
            '默认需求收集': '.default-demand' in content,
            '考场名称生成': 'roomName = prefix' in content,
            '模态框关闭': '.modal(\'hide\')' in content,
            '成功提示': 'alert-success' in content
        }
        
        all_passed = True
        for check_name, result in js_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_room_naming_logic():
    """测试考场命名逻辑"""
    print("\n🧪 测试考场命名逻辑...")
    
    test_cases = [
        # (前缀, 起始编号, 数量, 期望的考场名称列表)
        ('', 1, 3, ['1考场', '2考场', '3考场']),
        ('A', 1, 3, ['A1考场', 'A2考场', 'A3考场']),
        ('B', 101, 2, ['B101考场', 'B102考场']),
        ('1号楼', 1, 2, ['1号楼1考场', '1号楼2考场']),
        ('', 5, 3, ['5考场', '6考场', '7考场'])
    ]
    
    all_passed = True
    
    for prefix, start_number, count, expected in test_cases:
        # 模拟JavaScript逻辑
        room_names = []
        for i in range(count):
            if prefix:
                room_name = prefix + str(start_number + i) + '考场'
            else:
                room_name = str(start_number + i) + '考场'
            room_names.append(room_name)
        
        if room_names == expected:
            print(f"   ✅ 前缀='{prefix}', 起始={start_number}, 数量={count}: {room_names}")
        else:
            print(f"   ❌ 前缀='{prefix}', 起始={start_number}, 数量={count}: 期望{expected}, 实际{room_names}")
            all_passed = False
    
    return all_passed

def test_default_demands_logic():
    """测试默认监考人数逻辑"""
    print("\n🧪 测试默认监考人数逻辑...")
    
    # 模拟科目和默认需求
    subjects = ['语文', '数学', '英语']
    default_demands = {'语文': 2, '数学': 2, '英语': 1}
    
    # 模拟批量添加3个考场
    rooms = []
    for i in range(3):
        room_name = f'{i+1}考场'
        # 复制默认需求（模拟Object.assign）
        demands = default_demands.copy()
        rooms.append({
            'name': room_name,
            'demands': demands
        })
    
    # 验证结果
    expected_rooms = [
        {'name': '1考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '2考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}},
        {'name': '3考场', 'demands': {'语文': 2, '数学': 2, '英语': 1}}
    ]
    
    if rooms == expected_rooms:
        print("   ✅ 默认监考人数设置正确")
        for room in rooms:
            print(f"      {room['name']}: {room['demands']}")
        return True
    else:
        print("   ❌ 默认监考人数设置错误")
        print(f"      期望: {expected_rooms}")
        print(f"      实际: {rooms}")
        return False

def test_validation_logic():
    """测试输入验证逻辑"""
    print("\n🧪 测试输入验证逻辑...")
    
    test_cases = [
        # (数量, 是否有效, 描述)
        (0, False, '数量为0'),
        (-1, False, '负数'),
        (1, True, '最小有效值'),
        (25, True, '中等数量'),
        (50, True, '最大有效值'),
        (51, False, '超过最大值'),
        (100, False, '远超最大值')
    ]
    
    all_passed = True
    
    for count, expected_valid, description in test_cases:
        # 模拟JavaScript验证逻辑
        is_valid = count > 0 and count <= 50
        
        if is_valid == expected_valid:
            status = "✅" if is_valid else "⚠️ "
            print(f"   {status} {description}: 数量={count}, 有效={is_valid}")
        else:
            print(f"   ❌ {description}: 数量={count}, 期望有效={expected_valid}, 实际有效={is_valid}")
            all_passed = False
    
    return all_passed

def test_integration_scenario():
    """测试完整集成场景"""
    print("\n🧪 测试完整集成场景...")
    
    scenarios = [
        {
            'name': '基础场景',
            'prefix': '',
            'start_number': 1,
            'count': 5,
            'default_demands': {'语文': 2, '数学': 2, '英语': 2},
            'expected_count': 5
        },
        {
            'name': '带前缀场景',
            'prefix': 'A',
            'start_number': 101,
            'count': 3,
            'default_demands': {'高等数学': 2, '大学物理': 1},
            'expected_count': 3
        },
        {
            'name': '大批量场景',
            'prefix': '1号楼',
            'start_number': 1,
            'count': 20,
            'default_demands': {'计算机基础': 3},
            'expected_count': 20
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        try:
            # 模拟批量添加逻辑
            rooms = []
            for i in range(scenario['count']):
                if scenario['prefix']:
                    room_name = scenario['prefix'] + str(scenario['start_number'] + i) + '考场'
                else:
                    room_name = str(scenario['start_number'] + i) + '考场'
                
                rooms.append({
                    'name': room_name,
                    'demands': scenario['default_demands'].copy()
                })
            
            # 验证结果
            if len(rooms) == scenario['expected_count']:
                print(f"   ✅ 考场数量: {len(rooms)}")
                print(f"   ✅ 第一个考场: {rooms[0]['name']}")
                print(f"   ✅ 最后一个考场: {rooms[-1]['name']}")
                print(f"   ✅ 监考需求: {rooms[0]['demands']}")
            else:
                print(f"   ❌ 考场数量错误: 期望{scenario['expected_count']}, 实际{len(rooms)}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 场景执行失败: {e}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🔧 批量添加考场功能测试")
    print("=" * 50)
    print("测试考场页面的批量添加功能")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查UI组件
    test_results.append(("UI组件检查", check_batch_add_ui()))
    
    # 2. 检查JavaScript功能
    test_results.append(("JavaScript功能检查", check_batch_add_javascript()))
    
    # 3. 测试考场命名逻辑
    test_results.append(("考场命名逻辑", test_room_naming_logic()))
    
    # 4. 测试默认监考人数逻辑
    test_results.append(("默认监考人数逻辑", test_default_demands_logic()))
    
    # 5. 测试输入验证逻辑
    test_results.append(("输入验证逻辑", test_validation_logic()))
    
    # 6. 测试集成场景
    test_results.append(("集成场景测试", test_integration_scenario()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 功能特点:")
        print("✅ 批量添加考场UI组件完整")
        print("✅ 支持自定义考场前缀和起始编号")
        print("✅ 支持设置默认监考人数")
        print("✅ 实时预览功能")
        print("✅ 输入验证和错误处理")
        print("✅ 一次最多添加50个考场")
        
        print("\n🚀 使用方法:")
        print("1. 点击'批量添加'按钮")
        print("2. 设置考场数量、前缀、起始编号")
        print("3. 为每个科目设置默认监考人数")
        print("4. 查看预览信息")
        print("5. 点击'确认添加'完成批量添加")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
