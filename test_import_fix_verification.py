#!/usr/bin/env python3
"""
测试科目页面Excel导入功能修复验证
验证是否修正了重复弹窗的错误
"""

import sys
import os
import time

def test_backend_import_function():
    """测试后端导入函数是否正常工作"""
    print("🧪 测试后端导入函数...")
    
    try:
        sys.path.append('.')
        from app import import_subjects_from_excel
        print("✅ 成功导入应用模块")
    except ImportError as e:
        print(f"❌ 无法导入应用模块: {e}")
        return False
    
    template_file = 'template-guide/kemu.xlsx'
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        subjects = import_subjects_from_excel(template_file)
        print(f"✅ 科目导入成功")
        print(f"   导入数据数量: {len(subjects)}")
        
        if len(subjects) > 0:
            print(f"   示例数据: {subjects[0]}")
            return True
        else:
            print(f"   ⚠️  没有导入任何数据")
            return False
            
    except Exception as e:
        print(f"❌ 科目导入失败: {e}")
        return False

def check_page_implementation():
    """检查页面实现是否已修复"""
    print("\n📄 检查页面实现...")
    
    template_file = 'templates/wizard/step1_subjects.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否只有一个Excel导入实现
        checks = {
            '独立脚本引用': 'subjects-excel-import.js' not in content,
            '组件函数定义': 'initExcelImportComponent' not in content,
            '重复初始化': '$(window).on(\'load\'' not in content,
            '简洁实现存在': '#import-excel-btn-subjects\').click' in content,
            '文件处理函数': 'handleSubjectFileUpload' in content,
            'CSRF令牌设置': 'csrf_token' in content,
            '动态文件输入': 'document.createElement(\'input\')' in content
        }
        
        all_passed = True
        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查页面实现失败: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("\n🌐 检查服务器状态...")
    
    import requests
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code in [200, 302]:  # 200或重定向到登录页面都是正常的
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"⚠️  服务器响应异常 (状态码: {response.status_code})")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def create_test_instructions():
    """创建手动测试说明"""
    print("\n📋 手动测试说明...")
    
    instructions = """
=== 手动测试步骤 ===

1. 打开浏览器访问: http://localhost:5000/wizard/step1_subjects
2. 使用管理员账号登录 (admin/admin123)
3. 点击"从Excel导入"按钮
4. 验证以下行为:
   ✅ 应该只弹出一次文件选择窗口
   ✅ 选择template-guide/kemu.xlsx文件
   ✅ 导入成功后显示正确的条数
   ❌ 导入完成后不应该再弹出文件选择窗口

=== 预期结果 ===
- 点击按钮 → 弹出文件选择窗口（仅一次）
- 选择文件 → 开始导入处理
- 导入成功 → 显示成功消息和条数
- 完成导入 → 不再有额外弹窗

=== 如果仍有问题 ===
- 检查浏览器控制台是否有JavaScript错误
- 确认看到"科目页面：导入按钮被点击"日志
- 验证没有重复的事件绑定

=== 调试信息 ===
打开浏览器开发者工具(F12)，在Console中应该看到:
- "科目页面：导入按钮被点击"
- "科目页面：文件已选择: filename.xlsx"
- "科目页面：开始处理文件: filename.xlsx"
    """
    
    print(instructions)
    return True

def main():
    """主测试函数"""
    print("🔧 科目页面Excel导入功能修复验证")
    print("=" * 60)
    print("测试是否修正了重复弹窗的错误")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查服务器状态
    test_results.append(("服务器状态", check_server_status()))
    
    # 2. 测试后端导入函数
    test_results.append(("后端导入函数", test_backend_import_function()))
    
    # 3. 检查页面实现
    test_results.append(("页面实现检查", check_page_implementation()))
    
    # 4. 创建测试说明
    test_results.append(("测试说明创建", create_test_instructions()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 自动化验证结果")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n自动化验证结果: {passed_count}/{len(test_results)} 项通过")
    
    if passed_count == len(test_results):
        print("\n🎉 自动化验证全部通过！")
        print("\n📝 修复总结:")
        print("✅ 删除了重复的Excel导入实现")
        print("✅ 保留了一个简洁的jQuery实现")
        print("✅ 参考了step2_rooms的成功模式")
        print("✅ 后端导入函数正常工作")
        print("✅ 服务器运行正常")
        
        print("\n🚀 请按照上述手动测试步骤验证前端功能:")
        print("   主要验证点: 导入成功后不再弹出文件选择窗口")
        return 0
    else:
        print("\n❌ 部分自动化验证失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
