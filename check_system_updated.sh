#!/bin/bash
# 监考安排系统状态检查脚本
# 作者：AI助手
# 日期：2024年4月20日

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
    log_warn "此脚本建议使用root权限运行以获取完整信息"
    log_info "请考虑使用 'sudo $0' 重新运行"
fi

# 应用目录
APP_DIR="/opt/jiankao"

# 服务器信息
SERVER_IP="**************"
DOMAIN_NAME="jiankao.her5.com"

# 检查系统信息
check_system_info() {
    log_step "系统信息"
    echo "操作系统: $(lsb_release -ds 2>/dev/null || cat /etc/*release 2>/dev/null | head -n1 || uname -om)"
    echo "内核版本: $(uname -r)"
    echo "CPU: $(grep -c processor /proc/cpuinfo) 核"
    echo "内存: $(free -h | grep Mem | awk '{print $2}')"
    echo "磁盘空间: $(df -h / | awk 'NR==2 {print $4}') 可用"
    echo "Python版本: $(python3 --version 2>/dev/null)"
}

# 检查服务状态
check_services() {
    log_step "服务状态"
    
    # 检查Supervisor
    if systemctl is-active --quiet supervisor; then
        echo "Supervisor: ${GREEN}运行中${NC}"
    else
        echo "Supervisor: ${RED}未运行${NC}"
    fi
    
    # 检查Nginx
    if systemctl is-active --quiet nginx; then
        echo "Nginx: ${GREEN}运行中${NC}"
    else
        echo "Nginx: ${RED}未运行${NC}"
    fi
    
    # 检查应用
    if supervisorctl status jiankao | grep -q "RUNNING"; then
        echo "监考安排系统: ${GREEN}运行中${NC}"
    else
        echo "监考安排系统: ${RED}未运行${NC}"
    fi
}

# 检查目录和文件
check_files() {
    log_step "目录和文件检查"
    
    # 检查应用目录
    if [ -d "$APP_DIR" ]; then
        echo "应用目录: ${GREEN}存在${NC}"
    else
        echo "应用目录: ${RED}不存在${NC}"
        return
    fi
    
    # 检查配置文件
    if [ -f "$APP_DIR/config.py" ]; then
        echo "配置文件: ${GREEN}存在${NC}"
    else
        echo "配置文件: ${RED}不存在${NC}"
    fi
    
    # 检查数据库
    if [ -f "$APP_DIR/app.db" ]; then
        echo "数据库: ${GREEN}存在${NC}"
        echo "数据库大小: $(du -h "$APP_DIR/app.db" | cut -f1)"
    else
        echo "数据库: ${RED}不存在${NC}"
    fi
    
    # 检查临时目录
    if [ -d "/tmp/jiankao" ]; then
        echo "临时目录: ${GREEN}存在${NC}"
    else
        echo "临时目录: ${RED}不存在${NC}"
    fi
    
    # 检查日志目录
    if [ -d "$APP_DIR/logs" ]; then
        echo "日志目录: ${GREEN}存在${NC}"
        echo "日志文件数量: $(find "$APP_DIR/logs" -type f | wc -l)"
    else
        echo "日志目录: ${RED}不存在${NC}"
    fi
}

# 检查域名解析
check_domain() {
    log_step "域名解析检查"
    
    echo "域名: $DOMAIN_NAME"
    echo "预期解析IP: $SERVER_IP"
    
    # 检查DNS解析
    RESOLVED_IP=$(dig +short $DOMAIN_NAME A 2>/dev/null || host -t A $DOMAIN_NAME 2>/dev/null | grep "has address" | awk '{print $NF}')
    
    if [ -z "$RESOLVED_IP" ]; then
        echo "DNS解析状态: ${RED}失败${NC} (无法解析域名)"
    elif [ "$RESOLVED_IP" != "$SERVER_IP" ]; then
        echo "DNS解析状态: ${YELLOW}不匹配${NC}"
        echo "当前解析到: $RESOLVED_IP"
        echo "应该解析到: $SERVER_IP"
    else
        echo "DNS解析状态: ${GREEN}正确${NC}"
        echo "解析结果: $DOMAIN_NAME -> $RESOLVED_IP"
    fi
    
    # 检查反向解析
    REVERSE_DNS=$(dig +short -x $SERVER_IP 2>/dev/null || host $SERVER_IP 2>/dev/null | grep "domain name pointer" | awk '{print $NF}')
    
    if [ -n "$REVERSE_DNS" ]; then
        echo "反向解析: $REVERSE_DNS"
    else
        echo "反向解析: ${YELLOW}未配置${NC}"
    fi
    
    # 测试连通性
    echo "测试连通性:"
    curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://$DOMAIN_NAME/
    curl -s -o /dev/null -w "HTTPS状态码: %{http_code}\n" -k https://$DOMAIN_NAME/
}

# 检查网络配置
check_network() {
    log_step "网络配置"
    
    # 检查Nginx配置
    if [ -f "/etc/nginx/sites-enabled/jiankao" ]; then
        echo "Nginx配置: ${GREEN}存在${NC}"
        
        # 检查是否配置了SSL
        if grep -q "ssl_certificate" "/etc/nginx/sites-enabled/jiankao"; then
            echo "SSL配置: ${GREEN}已启用${NC}"
            
            # 获取域名
            DOMAIN=$(grep -oP "server_name \K[^;]+" "/etc/nginx/sites-enabled/jiankao" | tr -d ' ')
            echo "域名: $DOMAIN"
            
            # 检查证书有效期
            if command -v certbot &> /dev/null; then
                CERT_INFO=$(certbot certificates 2>/dev/null | grep -A 2 "$DOMAIN")
                if [ -n "$CERT_INFO" ]; then
                    EXPIRY=$(echo "$CERT_INFO" | grep "Expiry" | awk '{print $3, $4, $5, $6, $7}')
                    echo "证书有效期至: $EXPIRY"
                else
                    echo "证书信息: ${RED}未找到${NC}"
                fi
            fi
        else
            echo "SSL配置: ${YELLOW}未启用${NC}"
        fi
    else
        echo "Nginx配置: ${RED}不存在${NC}"
    fi
    
    # 检查端口
    echo "开放端口:"
    if command -v netstat &> /dev/null; then
        netstat -tulpn 2>/dev/null | grep -E ':(80|443|5000)' | awk '{print $4, $7}'
    elif command -v ss &> /dev/null; then
        ss -tulpn | grep -E ':(80|443|5000)' | awk '{print $5, $7}'
    else
        echo "${YELLOW}无法检查端口，netstat和ss命令均不可用${NC}"
    fi
}

# 检查日志
check_logs() {
    log_step "最近日志"
    
    # 检查应用日志
    if [ -f "$APP_DIR/logs/app.log" ]; then
        echo "应用日志最后10行:"
        tail -n 10 "$APP_DIR/logs/app.log"
    else
        echo "应用日志: ${RED}不存在${NC}"
    fi
    
    # 检查Nginx错误日志
    if [ -f "/var/log/nginx/jiankao_error.log" ]; then
        echo -e "\nNginx错误日志最后10行:"
        tail -n 10 "/var/log/nginx/jiankao_error.log"
    fi
    
    # 检查Supervisor日志
    if [ -f "$APP_DIR/logs/supervisor_err.log" ]; then
        echo -e "\nSupervisor错误日志最后10行:"
        tail -n 10 "$APP_DIR/logs/supervisor_err.log"
    fi
}

# 主函数
main() {
    echo "========== 监考安排系统状态检查 =========="
    echo "检查时间: $(date)"
    echo "服务器IP: $SERVER_IP"
    echo "域名: $DOMAIN_NAME"
    echo "=========================================="
    
    check_system_info
    echo
    
    check_domain
    echo
    
    check_services
    echo
    
    check_files
    echo
    
    check_network
    echo
    
    check_logs
    echo
    
    echo "=========================================="
    echo "检查完成"
    echo "=========================================="
}

# 执行主函数
main
