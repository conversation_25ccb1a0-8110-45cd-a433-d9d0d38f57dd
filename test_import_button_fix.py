#!/usr/bin/env python3
"""
测试Excel导入按钮修复的脚本
"""

import time
import sys

def test_pages():
    """测试页面修复"""
    print("🧪 测试Excel导入按钮修复...")
    
    pages_to_test = [
        ("科目设置", "http://localhost:5000/wizard/step1_subjects"),
        ("考场设置", "http://localhost:5000/wizard/step2_rooms"),
        ("监考员设置", "http://localhost:5000/wizard/step3_proctors")
    ]
    
    print("\n=== 修复内容 ===")
    print("1. ✅ 将文件输入的 class='d-none' 改为 style='display: none;'")
    print("2. ✅ 添加了 e.preventDefault() 防止默认行为")
    print("3. ✅ 使用 fileInput[0].click() 直接调用DOM方法")
    print("4. ✅ 添加了文件输入元素存在性检查")
    print("5. ✅ 添加了详细的控制台日志和错误处理")
    
    print("\n=== 测试步骤 ===")
    for i, (name, url) in enumerate(pages_to_test, 1):
        print(f"{i}. 访问 {name} 页面: {url}")
        print(f"   - 点击'从Excel导入'按钮")
        print(f"   - 检查是否弹出文件选择对话框")
        print(f"   - 查看浏览器控制台的调试信息")
        print()
    
    print("=== 预期结果 ===")
    print("✅ 点击'从Excel导入'按钮应该立即弹出文件选择对话框")
    print("✅ 控制台应该显示调试信息，如'导入按钮被点击'")
    print("✅ 如果有错误，会显示具体的错误信息")
    
    print("\n=== 如果仍然不工作 ===")
    print("1. 检查浏览器控制台是否有JavaScript错误")
    print("2. 确认jQuery已正确加载")
    print("3. 检查是否有其他JavaScript冲突")
    print("4. 尝试在不同浏览器中测试")
    
    return True

def check_file_modifications():
    """检查文件修改"""
    print("\n=== 文件修改检查 ===")
    
    files_modified = [
        "templates/wizard/step1_subjects.html",
        "templates/wizard/step2_rooms.html"
    ]
    
    for file_path in files_modified:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查关键修改
            has_style_display = 'style="display: none;"' in content
            has_prevent_default = 'e.preventDefault()' in content
            has_dom_click = 'fileInput[0].click()' in content
            has_console_log = 'console.log(' in content
            
            print(f"\n📁 {file_path}:")
            print(f"  ✅ style='display: none;': {'是' if has_style_display else '否'}")
            print(f"  ✅ e.preventDefault(): {'是' if has_prevent_default else '否'}")
            print(f"  ✅ DOM click方法: {'是' if has_dom_click else '否'}")
            print(f"  ✅ 控制台日志: {'是' if has_console_log else '否'}")
            
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
        except Exception as e:
            print(f"❌ 读取文件出错: {file_path} - {e}")
    
    return True

def main():
    """主函数"""
    print("🔧 Excel导入按钮修复验证")
    print("=" * 50)
    
    # 检查文件修改
    check_file_modifications()
    
    # 测试说明
    test_pages()
    
    print("\n" + "=" * 50)
    print("✅ 修复已完成，请手动测试页面功能")
    print("💡 如果问题仍然存在，请检查浏览器控制台的错误信息")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
