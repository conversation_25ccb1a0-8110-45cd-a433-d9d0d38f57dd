#!/bin/bash

# 定义颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 域名和邮箱设置
DOMAIN="jiankao.her5.com"
EMAIL="<EMAIL>"

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}请使用root权限运行此脚本！${NC}"
        echo "使用方法: sudo $0"
        exit 1
    fi
}

# 显示状态
show_status() {
    echo -e "\n${YELLOW}检查 jiankao 服务状态:${NC}"
    systemctl status jiankao.service
    echo -e "\n${YELLOW}检查 Redis 服务状态:${NC}"
    systemctl status redis-server.service
    echo -e "\n${YELLOW}检查 Nginx 服务状态:${NC}"
    systemctl status nginx.service
}

# 启动服务
start_services() {
    echo -e "${YELLOW}正在启动 Redis 服务...${NC}"
    systemctl start redis-server.service
    echo -e "${YELLOW}正在启动 Nginx 服务...${NC}"
    systemctl start nginx.service
    echo -e "${YELLOW}正在启动 jiankao 服务...${NC}"
    systemctl start jiankao.service
    show_status
}

# 停止服务
stop_services() {
    echo -e "${YELLOW}正在停止 jiankao 服务...${NC}"
    systemctl stop jiankao.service
    echo -e "${YELLOW}正在停止 Nginx 服务...${NC}"
    systemctl stop nginx.service
    echo -e "${YELLOW}正在停止 Redis 服务...${NC}"
    systemctl stop redis-server.service
    show_status
}

# 重启服务
restart_services() {
    echo -e "${YELLOW}正在重启服务...${NC}"
    systemctl restart redis-server.service
    systemctl restart nginx.service
    systemctl restart jiankao.service
    show_status
}

# 查看日志
view_logs() {
    echo -e "${YELLOW}显示最近的服务日志:${NC}"
    journalctl -u jiankao.service -n 100 --no-pager
}

# 实时查看日志
tail_logs() {
    echo -e "${YELLOW}正在实时查看日志...按 Ctrl+C 退出${NC}"
    journalctl -u jiankao.service -f
}

# 申请SSL证书
install_ssl() {
    echo -e "${YELLOW}正在为 ${DOMAIN} 申请SSL证书...${NC}"
    certbot --nginx -d ${DOMAIN} --email ${EMAIL} --agree-tos --non-interactive
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}SSL证书安装成功！${NC}"
    else
        echo -e "${RED}SSL证书安装失败！${NC}"
    fi
}

# 更新SSL证书
renew_ssl() {
    echo -e "${YELLOW}正在更新SSL证书...${NC}"
    certbot renew --non-interactive
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}SSL证书更新成功！${NC}"
        systemctl reload nginx
    else
        echo -e "${RED}SSL证书更新失败！${NC}"
    fi
}

# 检查SSL证书状态
check_ssl() {
    echo -e "${YELLOW}检查SSL证书状态：${NC}"
    certbot certificates
}

# 显示帮助信息
show_help() {
    echo -e "${GREEN}jiankao 网站维护脚本${NC}"
    echo "使用方法: $0 [选项]"
    echo "选项:"
    echo "  start    - 启动所有服务"
    echo "  stop     - 停止所有服务"
    echo "  restart  - 重启所有服务"
    echo "  status   - 查看服务状态"
    echo "  logs     - 查看最近的日志"
    echo "  tail     - 实时查看日志"
    echo "  ssl      - 申请SSL证书"
    echo "  renew    - 更新SSL证书"
    echo "  cert     - 查看证书状态"
    echo "  help     - 显示此帮助信息"
}

# 主程序
check_root

case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        view_logs
        ;;
    tail)
        tail_logs
        ;;
    ssl)
        install_ssl
        ;;
    renew)
        renew_ssl
        ;;
    cert)
        check_ssl
        ;;
    help|*)
        show_help
        ;;
esac 