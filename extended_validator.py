#!/usr/bin/env python
# -*- coding: utf-8 -*-

from excel_validator import ExcelValidator
import pandas as pd
import numpy as np
from typing import List, Dict, Any
import logging
from datetime import datetime
import re

logger = logging.getLogger('jiankao_app.extended_validator')

class ExtendedExcelValidator(ExcelValidator):
    """扩展的Excel验证器，增加了更多的验证规则"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.strict_validation_failed = False  # 严格验证是否失败
        self.warnings = []  # 警告信息列表

    def validate(self) -> bool:
        """
        执行所有验证步骤，包括基础验证和扩展验证

        返回:
            bool: 验证是否通过
        """
        try:
            # 更新进度：开始验证
            self._update_progress(0, "开始验证...")

            # 首先执行基础验证
            self._update_progress(10, "执行基础验证...")
            if not super().validate():
                self.is_valid = False
                self._update_progress(100, "基础验证失败")
                return False
            self._update_progress(30, "基础验证完成")

            # 执行严格验证
            self._update_progress(40, "执行严格验证规则...")
            self._validate_strict_rules()
            self._update_progress(70, "严格验证完成")
            
            # 执行警告级别的验证
            self._update_progress(80, "执行警告级别验证...")
            self._validate_warning_rules()
            self._update_progress(90, "警告验证完成")

            # 更新最终验证结果
            self.is_valid = not self.strict_validation_failed
            
            # 如果验证失败，生成错误报告
            if not self.is_valid:
                error_report_path = self._generate_error_report()
                if error_report_path:
                    self.validation_messages['error_report'] = error_report_path

            self._update_progress(100, "验证完成")
            return self.is_valid

        except Exception as e:
            logger.error(f"扩展验证过程发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"验证过程发生错误: {str(e)}")
            self.is_valid = False
            self._update_progress(100, "验证失败")
            return False

    def _validate_strict_rules(self) -> None:
        """执行严格验证规则"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            self._update_progress(45, "读取监考员数据...")
            
            # 1. 监考员基本信息检查
            self._update_progress(50, "检查监考员基本信息...")
            self._validate_teacher_basic_info(teacher_df)
            
            # 2. 场次限制有效性检查
            self._update_progress(55, "检查场次限制...")
            self._validate_session_limits(teacher_df)
            
            # 3. 数据格式规范检查
            self._update_progress(60, "检查数据格式...")
            self._validate_data_format(teacher_df)

            # 如果没有错误，确保strict_validation_failed为False
            if len(self.errors) == 0:
                self.strict_validation_failed = False
                logger.info("严格验证通过")
                self._update_progress(65, "严格验证规则通过")

        except Exception as e:
            logger.error(f"执行严格验证规则时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.errors.append(f"严格验证失败: {str(e)}")
            self._update_progress(65, "严格验证规则出错")

    def _validate_warning_rules(self) -> None:
        """执行警告级别的验证规则"""
        try:
            # 1. 检查监考员任教科目与考试科目的匹配度
            self._check_subject_matching()
            
            # 2. 检查考试时间分布是否合理
            self._check_exam_time_distribution()
            
            # 3. 检查监考员工作量分布
            self._check_workload_distribution()

        except Exception as e:
            logger.warning(f"执行警告级别验证时发生错误: {str(e)}", exc_info=True)
            self.warnings.append(f"警告级别验证过程出现异常: {str(e)}")

    def _check_subject_matching(self) -> None:
        """检查监考员任教科目与考试科目的匹配度"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 获取所有考试科目
            exam_subjects = set(subject_df['课程名称'].unique())
            
            # 收集所有不匹配的监考员信息
            unmatch_teachers = []
            
            # 检查每个监考员的任教科目
            for _, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    # 只有当任教科目不为'0'时才进行匹配检查
                    if subject_str != '0':
                        teacher_subjects = {s.strip() for s in subject_str.split(',') if s.strip()}
                        
                        # 如果监考员没有任教考试科目中的任何一门
                        if not teacher_subjects & exam_subjects:
                            unmatch_teachers.append({
                                'name': row['监考老师'],
                                'subjects': ','.join(teacher_subjects)
                            })
            
            # 如果存在不匹配的监考员，生成汇总警告信息
            if unmatch_teachers:
                # 按照教师姓名排序
                unmatch_teachers.sort(key=lambda x: x['name'])
                
                # 生成汇总信息
                teacher_count = len(unmatch_teachers)
                teacher_list = '\n'.join([
                    f"- {t['name']}（任教科目：{t['subjects']}）" 
                    for t in unmatch_teachers
                ])
                
                warning_msg = (
                    f"发现{teacher_count}位监考员的任教科目与考试科目无重叠，可能不熟悉考试内容：\n"
                    f"{teacher_list}\n"
                    f"建议检查这些监考员的安排是否合理。"
                )
                
                self.add_warning(warning_msg)

        except Exception as e:
            logger.warning(f"检查科目匹配度时发生错误: {str(e)}", exc_info=True)

    def _check_exam_time_distribution(self) -> None:
        """检查考试时间分布是否合理"""
        try:
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            
            # 将时间字符串转换为datetime
            subject_df['开始时间'] = pd.to_datetime(subject_df['开始时间'], format='%H:%M').dt.time
            subject_df['结束时间'] = pd.to_datetime(subject_df['结束时间'], format='%H:%M').dt.time
            
            # 检查是否存在考试时间过于集中的情况
            time_slots = []
            for _, row in subject_df.iterrows():
                time_slots.append((row['开始时间'], row['结束时间']))
            
            # 统计同时进行的考试数量
            max_concurrent = 0
            for i, (start1, end1) in enumerate(time_slots):
                concurrent = 1
                for j, (start2, end2) in enumerate(time_slots):
                    if i != j:
                        if (start1 <= start2 < end1) or (start2 <= start1 < end2):
                            concurrent += 1
                max_concurrent = max(max_concurrent, concurrent)
            
            if max_concurrent > len(subject_df) / 2:
                self.add_warning(
                    f"考试时间分布过于集中，最多有{max_concurrent}场考试同时进行，建议适当分散考试时间"
                )

        except Exception as e:
            logger.warning(f"检查考试时间分布时发生错误: {str(e)}", exc_info=True)

    def _check_workload_distribution(self) -> None:
        """检查监考员工作量分布是否合理"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            
            # 计算平均场次限制
            avg_limit = teacher_df['场次限制'].mean()
            std_limit = teacher_df['场次限制'].std()
            
            # 检查场次限制差异过大的情况
            for _, row in teacher_df.iterrows():
                if abs(row['场次限制'] - avg_limit) > 2 * std_limit:
                    self.add_warning(
                        f"监考员 {row['监考老师']} 的场次限制({row['场次限制']})与平均值({avg_limit:.1f})差异较大，" +
                        "建议适当调整以保证工作量均衡"
                    )

        except Exception as e:
            logger.warning(f"检查工作量分布时发生错误: {str(e)}", exc_info=True)

    def _validate_teacher_basic_info(self, teacher_df: pd.DataFrame) -> None:
        """验证监考员基本信息"""
        try:
            # 检查是否有重复的监考员
            duplicate_teachers = teacher_df[teacher_df['监考老师'].duplicated()]['监考老师'].unique()
            if len(duplicate_teachers) > 0:
                self.errors.append(f"发现重复的监考员: {', '.join(duplicate_teachers)}")
                self.strict_validation_failed = True

            # 检查每一行的监考员信息
            for idx, row in teacher_df.iterrows():
                row_num = idx + 2  # Excel行号从1开始，且有表头
                
                # 检查监考员姓名是否为空
                if pd.isna(row['监考老师']) or str(row['监考老师']).strip() == '':
                    self.errors.append(f"监考员设置表第{row_num}行监考老师姓名不能为空")
                    self.strict_validation_failed = True
                    continue

                # 检查任教科目格式
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    if subject_str != '0' and not all(s.strip() for s in subject_str.split(',')):
                        self.errors.append(f"监考员设置表第{row_num}行任教科目格式无效: {subject_str}")
                        self.strict_validation_failed = True

                # 检查必监考科目格式
                if '必监考科目' in row and pd.notna(row['必监考科目']):
                    required_subjects = str(row['必监考科目']).strip()
                    if required_subjects != '0' and not all(s.strip() for s in required_subjects.split(',')):
                        self.errors.append(f"监考员设置表第{row_num}行必监考科目格式无效: {required_subjects}")
                        self.strict_validation_failed = True

                # 检查不监考科目格式
                if '不监考科目' in row and pd.notna(row['不监考科目']):
                    unavailable_subjects = str(row['不监考科目']).strip()
                    if unavailable_subjects != '0' and not all(s.strip() for s in unavailable_subjects.split(',')):
                        self.errors.append(f"监考员设置表第{row_num}行不监考科目格式无效: {unavailable_subjects}")
                        self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证监考员基本信息时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"验证监考员信息时发生错误: {str(e)}")
            self.strict_validation_failed = True

    def _validate_session_limits(self, teacher_df: pd.DataFrame) -> None:
        """验证场次限制的有效性"""
        # 检查场次限制是否为正整数
        invalid_limits = teacher_df[~teacher_df['场次限制'].apply(lambda x: isinstance(x, (int, float)) and x > 0)]
        if not invalid_limits.empty:
            rows = invalid_limits.index.tolist()
            error_msg = f"以下行的场次限制必须为正整数: {[r+2 for r in rows]}"
            self.add_error_detail('监考员设置', rows[0]+2, '场次限制', 
                                str(invalid_limits.iloc[0]['场次限制']), '数据格式错误', error_msg)
            self.strict_validation_failed = True

        # 检查场次限制是否为0
        zero_limits = teacher_df[teacher_df['场次限制'] == 0]
        if not zero_limits.empty:
            rows = zero_limits.index.tolist()
            error_msg = f"以下行的场次限制不能为0: {[r+2 for r in rows]}"
            self.add_error_detail('监考员设置', rows[0]+2, '场次限制', '0', '数据值错误', error_msg)
            self.strict_validation_failed = True

        # 检查场次限制总和是否大于等于考试科目总场次
        subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
        total_sessions = len(subject_df)
        total_limits = teacher_df['场次限制'].sum()
        if total_limits < total_sessions:
            error_msg = f"监考员场次限制总和({total_limits})必须大于等于考试总场次数({total_sessions})"
            self.add_error_detail('监考员设置', 2, '场次限制', str(total_limits), '数据值错误', error_msg)
            self.strict_validation_failed = True

    def _validate_data_format(self, teacher_df: pd.DataFrame) -> None:
        """验证数据格式的规范性"""
        try:
            # 检查序号的连续性
            expected_seq = range(1, len(teacher_df) + 1)
            actual_seq = teacher_df['序号'].tolist()
            if not all(a == e for a, e in zip(actual_seq, expected_seq)):
                error_msg = "序号不连续或不从1开始"
                self.errors.append(error_msg)
                self.add_error_detail('监考员设置', 1, '序号', str(actual_seq), '序号错误', error_msg)
                self.strict_validation_failed = True

            # 检查任教科目格式
            for idx, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    # 如果值不是'0'且不是'nan'，则进行格式验证
                    if subject_str != '0' and subject_str.lower() != 'nan':
                        if not bool(re.match(r'^[\u4e00-\u9fa5,]+$', subject_str)):
                            error_msg = f"监考员设置表第{idx+1}行（{row['监考老师']}）的任教科目格式无效（应为中文科目名，多个科目用逗号分隔）"
                            self.errors.append(error_msg)
                            self.add_error_detail('监考员设置', idx+1, '任教科目', subject_str, '格式错误', error_msg)
                            self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证数据格式时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.errors.append(f"验证数据格式时发生错误: {str(e)}")

    def _validate_subject_sheet(self):
        """验证考试科目设置表"""
        try:
            sheet = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            
            # 检查必要的列是否存在
            required_columns = ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
            missing_columns = [col for col in required_columns if col not in sheet.columns]
            if missing_columns:
                self.errors.append(f"考试科目设置表缺少必要的列: {', '.join(missing_columns)}")
                return

            # 检查每一行的数据
            time_slots = []  # 用于存储时间段，检查重叠
            for index, row in sheet.iterrows():
                row_num = index + 2  # Excel行号从1开始，且有表头
                
                # 检查必填字段
                for field in ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']:
                    if pd.isna(row.get(field)):
                        self.errors.append(f"考试科目设置表第{row_num}行{field}不能为空")
                        continue

                # 检查日期格式
                date_str = str(row['考试日期']).strip()
                try:
                    exam_date = datetime.strptime(date_str, '%Y/%m/%d')
                except ValueError:
                    self.errors.append(f"考试科目设置表第{row_num}行考试日期格式错误，应为 YYYY/MM/DD 格式")
                    continue

                # 检查时间格式
                start_time_str = str(row['开始时间']).strip()
                end_time_str = str(row['结束时间']).strip()
                
                try:
                    start_time = datetime.strptime(start_time_str, '%H:%M')
                    end_time = datetime.strptime(end_time_str, '%H:%M')
                    
                    # 将日期和时间组合
                    start_dt = datetime.combine(exam_date.date(), start_time.time())
                    end_dt = datetime.combine(exam_date.date(), end_time.time())
                    
                    # 检查开始时间是否早于结束时间
                    if start_dt >= end_dt:
                        self.errors.append(f"考试科目设置表第{row_num}行结束时间必须晚于开始时间")
                        continue
                    
                    # 检查时间重叠（仅检查同一天的考试）
                    for other_start, other_end, other_row, other_subj in time_slots:
                        if other_start.date() == start_dt.date():  # 只检查同一天的考试
                            if start_dt < other_end and end_dt > other_start:
                                self.errors.append(
                                    f"考试科目设置表第{row_num}行的考试时间与第{other_row}行（{other_subj}）时间重合"
                                )
                    
                    # 添加到时间段列表
                    time_slots.append((start_dt, end_dt, row_num, row['课程名称']))
                    
                except ValueError:
                    self.errors.append(f"考试科目设置表第{row_num}行时间格式错误，应为 HH:MM 格式")
                    continue

            # 存储科目数据
            self.subjects_data = sheet.to_dict('records')

        except Exception as e:
            self.errors.append(f"验证考试科目设置表时发生错误: {str(e)}")
            logger.error(f"验证考试科目设置表时发生错误: {str(e)}", exc_info=True)

    def get_validation_messages(self) -> Dict[str, Any]:
        """
        获取验证消息，包括错误、警告和错误报告路径

        返回:
            Dict: 包含验证结果的字典
        """
        messages = super().get_validation_messages()
        if hasattr(self, 'validation_messages') and 'error_report' in self.validation_messages:
            messages['error_report'] = self.validation_messages['error_report']
        return messages 