#!/usr/bin/env python3
"""
测试监考员页面表格结构
验证监考员信息是否带有完整的列头信息
"""

import os

def check_table_structure():
    """检查表格结构"""
    print("🔍 检查监考员表格结构...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    if not os.path.exists(template_file):
        print(f"❌ 页面模板不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查表格基本结构
        table_checks = {
            '表格容器': 'table-responsive' in content,
            '表格元素': '<table' in content and 'id="proctors-table"' in content,
            '表头区域': '<thead' in content,
            '表体区域': '<tbody' in content and 'id="proctors-list"' in content,
            '表格样式': 'table-hover' in content
        }
        
        all_passed = True
        for check_name, result in table_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_column_headers():
    """检查列头信息"""
    print("\n🔍 检查列头信息...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有必需的列头
        required_headers = [
            '序号',
            '监考老师',
            '任教科目',
            '必监考科目',
            '不监考科目',
            '必监考考场',
            '不监考考场',
            '场次限制'
        ]
        
        missing_headers = []
        present_headers = []
        
        for header in required_headers:
            if header in content:
                present_headers.append(header)
            else:
                missing_headers.append(header)
        
        print(f"   📊 包含的列头: {len(present_headers)}/{len(required_headers)}")
        
        for header in present_headers:
            print(f"      ✅ {header}")
        
        for header in missing_headers:
            print(f"      ❌ {header}")
        
        # 检查是否有额外的功能列
        extra_checks = {
            '选择列': 'checkbox' in content and 'proctor-select' in content,
            '操作列': '操作' in content or 'btn-group' in content
        }
        
        for check_name, result in extra_checks.items():
            status = "✅" if result else "❌"
            print(f"      {status} {check_name}")
        
        return len(missing_headers) == 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_table_template():
    """检查表格行模板"""
    print("\n🔍 检查表格行模板...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查模板结构
        template_checks = {
            '模板定义': 'id="proctor-template"' in content,
            '表格行结构': '<tr>' in content and '</tr>' in content,
            '表格单元格': '<td>' in content,
            '输入字段': 'name="proctor_name"' in content,
            '选择框': 'name="required_subjects"' in content,
            '复选框': 'proctor-select' in content,
            '操作按钮': 'copy-proctor-btn' in content and 'remove-proctor-btn' in content
        }
        
        all_passed = True
        for check_name, result in template_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_form_fields():
    """检查表单字段"""
    print("\n🔍 检查表单字段...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有必需的表单字段
        required_fields = [
            'proctor_name',
            'teaching_subject',
            'required_subjects',
            'unavailable_subjects',
            'required_rooms',
            'unavailable_rooms',
            'session_limit'
        ]
        
        missing_fields = []
        present_fields = []
        
        for field in required_fields:
            if f'name="{field}"' in content:
                present_fields.append(field)
            else:
                missing_fields.append(field)
        
        print(f"   📊 包含的字段: {len(present_fields)}/{len(required_fields)}")
        
        for field in present_fields:
            print(f"      ✅ {field}")
        
        for field in missing_fields:
            print(f"      ❌ {field}")
        
        # 检查字段类型
        field_type_checks = {
            '文本输入': 'type="text"' in content,
            '数字输入': 'type="number"' in content,
            '多选下拉': 'multiple' in content,
            '复选框': 'type="checkbox"' in content
        }
        
        for check_name, result in field_type_checks.items():
            status = "✅" if result else "❌"
            print(f"      {status} {check_name}")
        
        return len(missing_fields) == 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_responsive_design():
    """检查响应式设计"""
    print("\n📱 检查响应式设计...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查响应式设计元素
        responsive_checks = {
            '响应式表格': 'table-responsive' in content,
            '小尺寸控件': 'form-control-sm' in content,
            '小尺寸按钮': 'btn-sm' in content,
            '表格悬停': 'table-hover' in content,
            '紧凑布局': 'btn-group-sm' in content
        }
        
        all_passed = True
        for check_name, result in responsive_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_table_layout():
    """分析表格布局"""
    print("\n📊 分析表格布局...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计列数
        th_count = content.count('<th')
        td_count_in_template = content[content.find('id="proctor-template"'):].count('<td')
        
        print(f"   📊 表头列数: {th_count}")
        print(f"   📊 模板列数: {td_count_in_template}")
        
        # 检查列宽设置
        width_settings = content.count('width:') + content.count('style="width')
        print(f"   📊 列宽设置: {width_settings}个列有明确宽度")
        
        # 分析表格特性
        table_features = {
            '固定表头': 'thead' in content,
            '斑马纹': 'table-striped' in content,
            '悬停效果': 'table-hover' in content,
            '边框': 'table-bordered' in content,
            '紧凑模式': 'table-sm' in content
        }
        
        active_features = sum(table_features.values())
        print(f"   📊 激活的表格特性: {active_features}/{len(table_features)}")
        
        for feature_name, active in table_features.items():
            status = "✅" if active else "❌"
            print(f"      {status} {feature_name}")
        
        # 评估布局质量
        if th_count >= 8 and td_count_in_template >= 8 and width_settings >= 5:
            print(f"   ✅ 表格布局质量: 优秀")
            return True
        elif th_count >= 6 and td_count_in_template >= 6:
            print(f"   ⚠️  表格布局质量: 良好")
            return True
        else:
            print(f"   ❌ 表格布局质量: 需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def test_data_binding():
    """测试数据绑定"""
    print("\n🔗 测试数据绑定...")
    
    template_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据绑定相关代码
        binding_checks = {
            'JavaScript变量': 'wizardData' in content and 'proctors' in content,
            '表格容器': 'proctors-list' in content,
            '模板引用': 'proctor-template' in content,
            '表单数据': 'proctors_data' in content,
            '动态渲染': 'tableBody' in content or 'append' in content
        }
        
        all_passed = True
        for check_name, result in binding_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 监考员页面表格结构测试")
    print("=" * 50)
    print("测试监考员信息是否带有完整的列头信息")
    print("=" * 50)
    
    test_results = []
    
    # 1. 检查表格结构
    test_results.append(("表格结构", check_table_structure()))
    
    # 2. 检查列头信息
    test_results.append(("列头信息", check_column_headers()))
    
    # 3. 检查表格行模板
    test_results.append(("表格行模板", check_table_template()))
    
    # 4. 检查表单字段
    test_results.append(("表单字段", check_form_fields()))
    
    # 5. 检查响应式设计
    test_results.append(("响应式设计", check_responsive_design()))
    
    # 6. 分析表格布局
    test_results.append(("表格布局", analyze_table_layout()))
    
    # 7. 测试数据绑定
    test_results.append(("数据绑定", test_data_binding()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 表格特点:")
        print("✅ 完整的列头信息（8个核心列）")
        print("✅ 响应式表格设计")
        print("✅ 规范的表单字段")
        print("✅ 友好的用户界面")
        print("✅ 完善的数据绑定")
        
        print("\n📊 列头信息:")
        print("1. 序号 - 监考员编号")
        print("2. 监考老师 - 监考员姓名")
        print("3. 任教科目 - 教师任教的科目")
        print("4. 必监考科目 - 必须监考的科目")
        print("5. 不监考科目 - 不能监考的科目")
        print("6. 必监考考场 - 必须监考的考场")
        print("7. 不监考考场 - 不能监考的考场")
        print("8. 场次限制 - 最大监考场次数")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
