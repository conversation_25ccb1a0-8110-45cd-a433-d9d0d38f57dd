#!/usr/bin/env python3
"""
测试引导页面生成的Excel文件格式
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入应用模块
from app import generate_excel_from_wizard_data

def test_wizard_excel_generation():
    """测试引导页面Excel生成功能"""
    
    # 模拟引导页面数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'},
            {'code': 'C', 'name': '英语', 'start_time': '2025/02/09 09:00', 'end_time': '2025/02/09 11:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3},
            {'name': '李老师', 'session_limit': 2},
            {'name': '王老师', 'session_limit': 2}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1, '英语': 2}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2, '英语': 1}},
            {'name': 'B201', 'demands': {'语文': 1, '数学': 1, '英语': 1}}
        ]
    }
    
    # 生成测试文件
    test_file = 'test_wizard_output.xlsx'
    
    try:
        print("正在生成测试Excel文件...")
        generate_excel_from_wizard_data(wizard_data, test_file)
        print(f"✅ 成功生成测试文件: {test_file}")
        
        # 验证生成的文件
        print("\n正在验证生成的文件...")
        
        # 读取并检查考试科目设置表
        df_subjects = pd.read_excel(test_file, sheet_name='考试科目设置')
        print("\n=== 考试科目设置表 ===")
        print("列名:", list(df_subjects.columns))
        print("数据类型:")
        print(df_subjects.dtypes)
        print("\n数据内容:")
        print(df_subjects.to_string())
        
        # 检查时间格式
        print("\n时间格式检查:")
        for i, row in df_subjects.iterrows():
            start_time = row['开始时间']
            end_time = row['结束时间']
            print(f"  {row['课程名称']}: 开始时间={start_time} (类型: {type(start_time).__name__}), 结束时间={end_time} (类型: {type(end_time).__name__})")
        
        # 读取监考员设置表
        df_proctors = pd.read_excel(test_file, sheet_name='监考员设置')
        print("\n=== 监考员设置表 ===")
        print("列名:", list(df_proctors.columns))
        print("数据内容:")
        print(df_proctors.to_string())
        
        # 读取考场设置表
        df_rooms = pd.read_excel(test_file, sheet_name='考场设置')
        print("\n=== 考场设置表 ===")
        print("列名:", list(df_rooms.columns))
        print("数据内容:")
        print(df_rooms.to_string())
        
        # 与原始监考安排.xlsx格式对比
        print("\n=== 与原始监考安排.xlsx格式对比 ===")
        if os.path.exists('监考安排.xlsx'):
            original_df = pd.read_excel('监考安排.xlsx', sheet_name='考试科目设置')
            print("原始文件列名:", list(original_df.columns))
            print("生成文件列名:", list(df_subjects.columns))
            
            # 检查列名是否匹配
            if list(original_df.columns) == list(df_subjects.columns):
                print("✅ 列名完全匹配")
            else:
                print("❌ 列名不匹配")
                print("  差异:", set(original_df.columns) - set(df_subjects.columns))
            
            # 检查时间格式是否匹配
            original_start_type = type(original_df['开始时间'].iloc[0]).__name__
            generated_start_type = type(df_subjects['开始时间'].iloc[0]).__name__
            
            print(f"原始文件时间类型: {original_start_type}")
            print(f"生成文件时间类型: {generated_start_type}")
            
            if original_start_type == generated_start_type:
                print("✅ 时间格式类型匹配")
            else:
                print("❌ 时间格式类型不匹配")
        else:
            print("⚠️  原始监考安排.xlsx文件不存在，无法对比")
        
        print(f"\n✅ 测试完成，生成的文件: {test_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_wizard_excel_generation()
