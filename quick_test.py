import pandas as pd

# 简单测试
try:
    # 检查生成的文件
    df = pd.read_excel('test_fixed_format.xlsx', sheet_name='监考员设置')
    print("监考员设置表列名:", list(df.columns))
    print("数据:")
    print(df)
    
    # 检查必需列
    required = ['序号', '监考老师', '任教科目', '场次限制']
    missing = [col for col in required if col not in df.columns]
    
    if missing:
        print(f"缺少列: {missing}")
    else:
        print("✅ 包含所有必需列")
        
    # 检查数据
    for i, row in df.iterrows():
        if pd.isna(row['序号']):
            print(f"第{i+1}行序号为空")
        if pd.isna(row['监考老师']) or str(row['监考老师']).strip() == '':
            print(f"第{i+1}行监考老师为空")
        if pd.isna(row['场次限制']):
            print(f"第{i+1}行场次限制为空")
            
except Exception as e:
    print(f"错误: {e}")
