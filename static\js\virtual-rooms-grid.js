/**
 * 虚拟滚动考场网格组件
 * 支持大规模考场数据的高性能显示
 */

class VirtualRoomsGrid {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            rowHeight: 45,
            visibleRowsBuffer: 5,
            ...options
        };
        
        this.data = [];
        this.subjects = [];
        this.filteredData = [];
        this.scrollTop = 0;
        this.visibleStartIndex = 0;
        this.visibleEndIndex = 0;
        
        this.elements = {};
        this.isInitialized = false;
        
        this.init();
    }
    
    init() {
        this.createElements();
        this.bindEvents();
        this.isInitialized = true;
    }
    
    createElements() {
        // 获取主要元素
        this.elements.headerSubjects = this.container.querySelector('#header-subjects');
        this.elements.roomNamesColumn = this.container.querySelector('#room-names-column');
        this.elements.dataGrid = this.container.querySelector('#data-grid');
        this.elements.roomsCount = this.container.querySelector('#rooms-count');
        this.elements.totalRooms = this.container.querySelector('#total-rooms');
        this.elements.totalDemand = this.container.querySelector('#total-demand');
        this.elements.avgDemand = this.container.querySelector('#avg-demand');
    }
    
    bindEvents() {
        // 数据网格滚动事件
        this.elements.dataGrid.addEventListener('scroll', () => {
            this.handleScroll();
        });
        
        // 考场名称列同步滚动
        this.elements.dataGrid.addEventListener('scroll', () => {
            this.elements.roomNamesColumn.scrollTop = this.elements.dataGrid.scrollTop;
        });
        
        // 科目表头同步滚动
        this.elements.dataGrid.addEventListener('scroll', () => {
            this.elements.headerSubjects.scrollLeft = this.elements.dataGrid.scrollLeft;
        });
        
        // 搜索功能
        const searchInput = document.getElementById('room-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterData(e.target.value);
            });
        }
        
        // 批量设置功能
        const batchSetBtn = document.getElementById('batch-set-btn');
        if (batchSetBtn) {
            batchSetBtn.addEventListener('click', () => {
                this.showBatchSetDialog();
            });
        }
    }
    
    setData(rooms, subjects) {
        this.data = rooms || [];
        this.subjects = subjects || [];
        this.filteredData = [...this.data];
        
        this.createHeaders();
        this.render();
        this.updateStats();
    }
    
    createHeaders() {
        // 创建科目表头
        this.elements.headerSubjects.innerHTML = '';
        this.subjects.forEach(subject => {
            const header = document.createElement('div');
            header.className = 'subject-header';
            header.textContent = subject.subject_name;
            header.title = subject.subject_name; // 完整名称提示
            this.elements.headerSubjects.appendChild(header);
        });
    }
    
    handleScroll() {
        const scrollTop = this.elements.dataGrid.scrollTop;
        const containerHeight = this.elements.dataGrid.clientHeight;
        
        // 计算可见范围
        const visibleRowCount = Math.ceil(containerHeight / this.options.rowHeight);
        const startIndex = Math.floor(scrollTop / this.options.rowHeight);
        const endIndex = Math.min(
            startIndex + visibleRowCount + this.options.visibleRowsBuffer,
            this.filteredData.length
        );
        
        // 只在范围变化时重新渲染
        if (startIndex !== this.visibleStartIndex || endIndex !== this.visibleEndIndex) {
            this.visibleStartIndex = Math.max(0, startIndex - this.options.visibleRowsBuffer);
            this.visibleEndIndex = endIndex;
            this.renderVisibleRows();
        }
    }
    
    render() {
        this.renderRoomNames();
        this.renderDataGrid();
    }
    
    renderRoomNames() {
        this.elements.roomNamesColumn.innerHTML = '';
        
        this.filteredData.forEach((room, index) => {
            const cell = document.createElement('div');
            cell.className = 'room-name-cell';
            cell.textContent = room.name || `${index + 1}考场`;
            cell.style.height = `${this.options.rowHeight}px`;
            this.elements.roomNamesColumn.appendChild(cell);
        });
    }
    
    renderDataGrid() {
        // 设置容器高度
        const totalHeight = this.filteredData.length * this.options.rowHeight;
        this.elements.dataGrid.style.height = `${totalHeight}px`;
        this.elements.dataGrid.style.position = 'relative';
        
        // 初始渲染可见行
        this.handleScroll();
    }
    
    renderVisibleRows() {
        // 清空现有内容
        this.elements.dataGrid.innerHTML = '';
        
        // 渲染可见行
        for (let i = this.visibleStartIndex; i < this.visibleEndIndex; i++) {
            if (i >= this.filteredData.length) break;
            
            const row = this.createDataRow(this.filteredData[i], i);
            this.elements.dataGrid.appendChild(row);
        }
    }
    
    createDataRow(roomData, index) {
        const row = document.createElement('div');
        row.className = 'virtual-row';
        row.style.top = `${index * this.options.rowHeight}px`;
        row.style.position = 'absolute';
        row.style.width = '100%';
        
        this.subjects.forEach(subject => {
            const cell = document.createElement('div');
            cell.className = 'data-cell';
            
            const input = document.createElement('input');
            input.type = 'number';
            input.min = '0';
            input.max = '99';
            input.value = (roomData.demands && roomData.demands[subject.subject_name]) || '';
            input.placeholder = '0';
            input.dataset.roomIndex = index;
            input.dataset.subjectName = subject.subject_name;
            
            // 绑定数据更新事件
            input.addEventListener('change', (e) => {
                this.updateRoomDemand(index, subject.subject_name, e.target.value);
            });
            
            // 键盘导航支持
            input.addEventListener('keydown', (e) => {
                this.handleKeyNavigation(e, index, subject.subject_name);
            });
            
            cell.appendChild(input);
            row.appendChild(cell);
        });
        
        return row;
    }
    
    updateRoomDemand(roomIndex, subjectName, value) {
        const room = this.filteredData[roomIndex];
        if (!room.demands) {
            room.demands = {};
        }
        
        const numValue = parseInt(value) || 0;
        room.demands[subjectName] = numValue;
        
        // 更新统计信息
        this.updateStats();
        
        // 触发数据更新事件
        this.onDataUpdate && this.onDataUpdate(this.data);
    }
    
    handleKeyNavigation(event, roomIndex, subjectName) {
        const key = event.key;
        let newRoomIndex = roomIndex;
        let newSubjectIndex = this.subjects.findIndex(s => s.subject_name === subjectName);
        
        switch (key) {
            case 'ArrowUp':
                newRoomIndex = Math.max(0, roomIndex - 1);
                event.preventDefault();
                break;
            case 'ArrowDown':
                newRoomIndex = Math.min(this.filteredData.length - 1, roomIndex + 1);
                event.preventDefault();
                break;
            case 'ArrowLeft':
                newSubjectIndex = Math.max(0, newSubjectIndex - 1);
                event.preventDefault();
                break;
            case 'ArrowRight':
                newSubjectIndex = Math.min(this.subjects.length - 1, newSubjectIndex + 1);
                event.preventDefault();
                break;
            case 'Enter':
            case 'Tab':
                // Tab到下一个输入框
                if (event.shiftKey) {
                    newSubjectIndex = Math.max(0, newSubjectIndex - 1);
                } else {
                    newSubjectIndex = newSubjectIndex + 1;
                    if (newSubjectIndex >= this.subjects.length) {
                        newSubjectIndex = 0;
                        newRoomIndex = Math.min(this.filteredData.length - 1, roomIndex + 1);
                    }
                }
                event.preventDefault();
                break;
            default:
                return; // 不处理其他按键
        }
        
        // 聚焦到新位置
        this.focusCell(newRoomIndex, this.subjects[newSubjectIndex].subject_name);
    }
    
    focusCell(roomIndex, subjectName) {
        // 确保目标行可见
        this.scrollToRow(roomIndex);
        
        // 延迟聚焦，确保DOM已更新
        setTimeout(() => {
            const input = this.elements.dataGrid.querySelector(
                `input[data-room-index="${roomIndex}"][data-subject-name="${subjectName}"]`
            );
            if (input) {
                input.focus();
                input.select();
            }
        }, 50);
    }
    
    scrollToRow(rowIndex) {
        const targetScrollTop = rowIndex * this.options.rowHeight;
        const containerHeight = this.elements.dataGrid.clientHeight;
        const currentScrollTop = this.elements.dataGrid.scrollTop;
        
        // 检查是否需要滚动
        if (targetScrollTop < currentScrollTop || 
            targetScrollTop > currentScrollTop + containerHeight - this.options.rowHeight) {
            this.elements.dataGrid.scrollTop = targetScrollTop - containerHeight / 2;
        }
    }
    
    filterData(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredData = [...this.data];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredData = this.data.filter(room => 
                (room.name || '').toLowerCase().includes(term)
            );
        }
        
        this.render();
        this.updateStats();
    }
    
    updateStats() {
        const totalRooms = this.filteredData.length;
        let totalDemand = 0;
        
        this.filteredData.forEach(room => {
            if (room.demands) {
                Object.values(room.demands).forEach(demand => {
                    totalDemand += parseInt(demand) || 0;
                });
            }
        });
        
        const avgDemand = totalRooms > 0 ? (totalDemand / totalRooms).toFixed(1) : 0;
        
        this.elements.roomsCount.textContent = `${totalRooms} 个考场`;
        this.elements.totalRooms.textContent = totalRooms;
        this.elements.totalDemand.textContent = totalDemand;
        this.elements.avgDemand.textContent = avgDemand;
    }
    
    showBatchSetDialog() {
        // 创建批量设置对话框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量设置监考员需求</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">选择科目:</label>
                            <select class="form-select" id="batch-subject">
                                ${this.subjects.map(s => `<option value="${s.subject_name}">${s.subject_name}</option>`).join('')}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">监考员数量:</label>
                            <input type="number" class="form-control" id="batch-value" min="0" max="99" value="2">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">应用范围:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="batch-range" id="range-all" value="all" checked>
                                <label class="form-check-label" for="range-all">所有考场</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="batch-range" id="range-empty" value="empty">
                                <label class="form-check-label" for="range-empty">仅空白考场</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="apply-batch">应用</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // 绑定应用按钮事件
        modal.querySelector('#apply-batch').addEventListener('click', () => {
            const subject = modal.querySelector('#batch-subject').value;
            const value = parseInt(modal.querySelector('#batch-value').value) || 0;
            const range = modal.querySelector('input[name="batch-range"]:checked').value;
            
            this.applyBatchSet(subject, value, range);
            bsModal.hide();
        });
        
        // 清理模态框
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }
    
    applyBatchSet(subjectName, value, range) {
        this.filteredData.forEach(room => {
            if (!room.demands) {
                room.demands = {};
            }
            
            // 根据范围决定是否应用
            if (range === 'all' || !room.demands[subjectName]) {
                room.demands[subjectName] = value;
            }
        });
        
        this.render();
        this.updateStats();
        this.onDataUpdate && this.onDataUpdate(this.data);
    }
    
    // 公共方法
    addRoom(roomData) {
        this.data.push(roomData);
        this.filteredData = [...this.data];
        this.render();
        this.updateStats();
    }
    
    removeRoom(index) {
        this.data.splice(index, 1);
        this.filteredData = [...this.data];
        this.render();
        this.updateStats();
    }
    
    getData() {
        return this.data;
    }
    
    destroy() {
        // 清理事件监听器和DOM元素
        this.elements.dataGrid.removeEventListener('scroll', this.handleScroll);
        this.isInitialized = false;
    }
}
