#!/usr/bin/env python3
"""
测试新格式的Excel生成功能
"""

import pandas as pd
from datetime import datetime

def test_generate_excel_new_format(wizard_data, file_path):
    """测试新格式的Excel生成"""
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')
        
        exam_date = ''
        start_time = ''
        end_time = ''
        
        try:
            if start_time_str:
                start_time_str = start_time_str.strip()
                start_datetime = None
                
                for fmt in ['%Y/%m/%d %H:%M', '%Y-%m-%d %H:%M']:
                    try:
                        start_datetime = datetime.strptime(start_time_str, fmt)
                        break
                    except ValueError:
                        continue
                
                if start_datetime:
                    exam_date = start_datetime.strftime('%Y/%m/%d')
                    start_time = start_datetime.strftime('%H:%M')
                        
            if end_time_str:
                end_time_str = end_time_str.strip()
                end_datetime = None
                
                for fmt in ['%Y/%m/%d %H:%M', '%Y-%m-%d %H:%M']:
                    try:
                        end_datetime = datetime.strptime(end_time_str, fmt)
                        break
                    except ValueError:
                        continue
                
                if end_datetime:
                    end_time = end_datetime.strftime('%H:%M')
                        
        except Exception as e:
            print(f"解析时间时出错: {e}")
            
        if not exam_date:
            exam_date = '2025/01/01'
        if not start_time:
            start_time = '09:00'
        if not end_time:
            end_time = '11:00'
            
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': exam_date,
            '开始时间': start_time,
            '结束时间': end_time
        })
    
    df_subjects = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 监考员设置
        proctors_data = []
        for proctor in wizard_data.get('proctors', []):
            proctors_data.append({
                '监考老师': proctor.get('name', '').strip(),
                '场次限制': proctor.get('session_limit', 99)
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 考场设置
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

def main():
    # 测试数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3},
            {'name': '李老师', 'session_limit': 2}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2}}
        ]
    }

    test_file = 'test_validator_format.xlsx'
    print("生成符合验证器要求的Excel文件...")
    test_generate_excel_new_format(wizard_data, test_file)

    # 验证结果
    df = pd.read_excel(test_file, sheet_name='考试科目设置')
    print("\n生成的考试科目设置表:")
    print(df)
    print("\n列名:", list(df.columns))

    # 检查是否包含验证器要求的所有列
    required_columns = ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        print(f"❌ 缺少列: {missing_columns}")
    else:
        print("✅ 包含所有必需的列")

    # 检查格式
    print("\n格式检查:")
    for i, row in df.iterrows():
        course_name = row['课程名称']
        exam_date = row['考试日期']
        start_time = row['开始时间']
        end_time = row['结束时间']
        print(f"  {course_name}: 日期={exam_date} 开始={start_time} 结束={end_time}")

    print(f"\n✅ 已生成符合验证器要求的文件: {test_file}")

if __name__ == '__main__':
    main()
