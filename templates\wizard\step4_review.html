{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    .summary-card {
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">向导第四步：预览与验证</h4>
                </div>
                <div class="card-body">
                    <p class="card-text">请仔细核对以下信息。确认无误后，输入任务标题并点击"验证配置"按钮。验证通过后即可生成任务。</p>
                    
                    <!-- 考试科目预览 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-book-open me-2"></i>考试科目</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>序号</th>
                                        <th>课程代码</th>
                                        <th>课程名称</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subject in wizard_data.get('subjects', []) %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ subject.subject_code }}</td>
                                        <td>{{ subject.subject_name }}</td>
                                        <td>{{ subject.start_time }}</td>
                                        <td>{{ subject.end_time }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 监考员预览 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-user-shield me-2"></i>监考员</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>序号</th>
                                        <th>监考老师</th>
                                        <th>任教科目</th>
                                        <th>必监考科目</th>
                                        <th>不监考科目</th>
                                        <th>必监考考场</th>
                                        <th>不监考考场</th>
                                        <th>场次限制</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for proctor in wizard_data.get('proctors', []) %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ proctor.name }}</td>
                                        <td>{{ proctor.teaching_subject }}</td>
                                        <td>{{ proctor.required_subjects | join(', ') }}</td>
                                        <td>{{ proctor.unavailable_subjects | join(', ') }}</td>
                                        <td>{{ proctor.required_rooms }}</td>
                                        <td>{{ proctor.unavailable_rooms }}</td>
                                        <td>{{ proctor.session_limit }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 考场预览 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-school me-2"></i>考场与需求</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>考场名称</th>
                                        {% for subject in wizard_data.get('subjects', []) %}
                                        <th>{{ subject.subject_name }} (人数)</th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for room in wizard_data.get('rooms', []) %}
                                    <tr>
                                        <td>{{ room.name }}</td>
                                        {% for subject in wizard_data.get('subjects', []) %}
                                        <td>{{ room.demands.get(subject.subject_name, 'N/A') }}</td>
                                        {% endfor %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <hr>

                    <!-- Validation Section -->
                    <div id="validation-section">
                        <div class="row">
                             <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label"><strong>任务标题</strong></label>
                                    <input type="text" class="form-control" id="title" name="title" value="监考安排 {{ now.strftime('%Y-%m-%d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">任务描述 (可选)</label>
                                    <input type="text" class="form-control" id="description" name="description" placeholder="例如：期中考试">
                                </div>
                            </div>
                        </div>
                        <button id="validate-btn" class="btn btn-primary w-100">
                            <i class="fas fa-check-double me-2"></i> 验证配置
                        </button>
                    </div>

                    <!-- Validation Progress Section -->
                    <div id="progress-section" style="display: none;">
                        <h5>正在验证...</h5>
                        <div class="progress">
                            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                    </div>
                    
                    <!-- Validation Result Section -->
                    <div id="result-section" class="mt-4" style="display: none;">
                        <h5>验证结果</h5>
                        <div id="validation-output"></div>
                        
                        <!-- Form to create task, only shown on success -->
                        <form id="create-task-form" method="POST" style="display: none;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" id="task-title-final" name="title">
                            <input type="hidden" id="task-description-final" name="description">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-check-circle me-2"></i>确认并生成任务
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step3_proctors') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#validate-btn').click(function() {
        var btn = $(this);
        var title = $('#title').val();

        if (!title.trim()) {
            alert('请输入任务标题。');
            return;
        }

        btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 正在请求验证...');
        
        // Use AJAX to submit the validation request
        $.ajax({
            url: "{{ url_for('wizard_validate') }}",
            type: 'POST',
            data: {
                csrf_token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#validation-section').hide();
                    $('#progress-section').show();
                    pollValidationProgress(response.validation_id);
                } else {
                    alert('验证请求失败: ' + response.error);
                    btn.prop('disabled', false).html('<i class="fas fa-check-double me-2"></i> 验证配置');
                }
            },
            error: function() {
                alert('请求验证时发生网络错误。');
                btn.prop('disabled', false).html('<i class="fas fa-check-double me-2"></i> 验证配置');
            }
        });
    });

    function pollValidationProgress(validationId) {
        var interval = setInterval(function() {
            $.get('/task/validate/' + validationId + '/progress', function(data) {
                $('#progress-bar').css('width', data.progress + '%').text(data.progress + '%');
                if (data.completed) {
                    clearInterval(interval);
                    displayValidationResult(data.result);
                }
            }).fail(function() {
                clearInterval(interval);
                alert('获取验证进度失败。');
            });
        }, 1000);
    }

    function displayValidationResult(result) {
        $('#progress-section').hide();
        $('#result-section').show();
        
        var output = $('#validation-output');
        output.empty();

        if (result.is_valid) {
            output.append('<div class="alert alert-success"><strong>配置有效！</strong> 您现在可以生成任务。</div>');
            $('#create-task-form').show().attr('action', '/task/create-from-validated/' + result.validation_id);
            $('#task-title-final').val($('#title').val());
            $('#task-description-final').val($('#description').val());
        } else {
            var errorHtml = '<div class="alert alert-danger"><strong>配置验证失败</strong><p>请返回前面的步骤修改错误后重试。</p><ul>';
            result.errors.forEach(function(error) {
                errorHtml += '<li>' + error + '</li>';
            });
            errorHtml += '</ul></div>';
            output.append(errorHtml);
        }

        if (result.warnings && result.warnings.length > 0) {
            var warningHtml = '<div class="alert alert-warning"><strong>警告:</strong><ul>';
            result.warnings.forEach(function(warning) {
                warningHtml += '<li>' + warning + '</li>';
            });
            warningHtml += '</ul></div>';
            output.append(warningHtml);
        }
    }

    // Handle the final task creation form submission
    $('#create-task-form').submit(function(e) {
        e.preventDefault();
        var form = $(this);
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            success: function(response) {
                if(response.success) {
                    window.location.href = '/task/' + response.task_id;
                } else {
                    alert('创建任务失败: ' + response.message);
                }
            },
            error: function() {
                alert('创建任务时发生网络错误。');
            }
        });
    });
});
</script>
{% endblock %} 