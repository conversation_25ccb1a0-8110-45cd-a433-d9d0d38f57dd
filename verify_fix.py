#!/usr/bin/env python3
"""
验证修复结果
"""

import pandas as pd
from datetime import datetime

def verify_generated_file():
    """验证生成的文件格式"""
    
    # 测试数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3, 'teaching_subject': '语文'},
            {'name': '李老师', 'session_limit': 2, 'teaching_subject': '数学'}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2}}
        ]
    }
    
    # 生成测试文件
    test_file = 'verify_fix.xlsx'
    
    # 1. 考试科目设置表
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')
        
        start_datetime = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
        end_datetime = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
        
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': start_datetime.strftime('%Y/%m/%d'),
            '开始时间': start_datetime,  # core程序需要的完整datetime
            '结束时间': end_datetime     # core程序需要的完整datetime
        })
    
    df_subjects = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 2. 监考员设置表
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            proctors_data.append({
                '序号': i,
                '监考老师': proctor.get('name', '').strip(),
                '任教科目': proctor.get('teaching_subject', ''),
                '场次限制': proctor.get('session_limit', 99),
                '必监考科目': '',
                '不监考科目': '',
                '必监考考场': '',
                '不监考考场': ''
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 3. 考场设置表
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)
    
    print(f"已生成验证文件: {test_file}")
    
    # 验证文件内容
    print("\n=== 验证文件内容 ===")
    
    # 检查考试科目设置表
    df_check = pd.read_excel(test_file, sheet_name='考试科目设置')
    print("考试科目设置表:")
    print("  列名:", list(df_check.columns))
    print("  时间格式检查:")
    for i, row in df_check.iterrows():
        start_time = row['开始时间']
        end_time = row['结束时间']
        print(f"    {row['课程名称']}: {start_time} ({type(start_time).__name__}) - {end_time} ({type(end_time).__name__})")
    
    # 检查监考员设置表
    df_proctors_check = pd.read_excel(test_file, sheet_name='监考员设置')
    print("\n监考员设置表:")
    print("  列名:", list(df_proctors_check.columns))
    print("  数据:")
    for i, row in df_proctors_check.iterrows():
        print(f"    {row['序号']}: {row['监考老师']} - {row['任教科目']} - {row['场次限制']}")
    
    # 检查考场设置表
    df_rooms_check = pd.read_excel(test_file, sheet_name='考场设置')
    print("\n考场设置表:")
    print("  列名:", list(df_rooms_check.columns))
    print("  数据:")
    for i, row in df_rooms_check.iterrows():
        room_info = f"    {row['考场']}: "
        for col in df_rooms_check.columns:
            if col != '考场':
                room_info += f"{col}={row[col]} "
        print(room_info)
    
    # 检查core程序兼容性要求
    print("\n=== Core程序兼容性检查 ===")
    
    # 检查必需列
    required_subject_cols = ['课程代码', '课程名称', '开始时间', '结束时间']
    required_teacher_cols = ['序号', '监考老师', '任教科目', '场次限制']
    required_room_cols = ['考场']
    
    missing_subject = [col for col in required_subject_cols if col not in df_check.columns]
    missing_teacher = [col for col in required_teacher_cols if col not in df_proctors_check.columns]
    missing_room = [col for col in required_room_cols if col not in df_rooms_check.columns]
    
    if missing_subject:
        print(f"❌ 考试科目设置表缺少列: {missing_subject}")
    else:
        print("✅ 考试科目设置表包含所有必需列")
    
    if missing_teacher:
        print(f"❌ 监考员设置表缺少列: {missing_teacher}")
    else:
        print("✅ 监考员设置表包含所有必需列")
    
    if missing_room:
        print(f"❌ 考场设置表缺少列: {missing_room}")
    else:
        print("✅ 考场设置表包含所有必需列")
    
    # 检查时间格式
    time_format_ok = True
    for i, row in df_check.iterrows():
        if not isinstance(row['开始时间'], pd.Timestamp):
            print(f"❌ 开始时间格式错误: {type(row['开始时间'])}")
            time_format_ok = False
        if not isinstance(row['结束时间'], pd.Timestamp):
            print(f"❌ 结束时间格式错误: {type(row['结束时间'])}")
            time_format_ok = False
    
    if time_format_ok:
        print("✅ 时间格式正确（Timestamp类型）")
    
    print("\n=== 总结 ===")
    if not missing_subject and not missing_teacher and not missing_room and time_format_ok:
        print("🎉 所有检查通过！文件格式与core程序完全兼容！")
        print("\n修复成功的关键点:")
        print("  ✅ 时间格式使用完整的datetime对象（Timestamp）")
        print("  ✅ 监考员设置表包含所有必需列")
        print("  ✅ 考场设置表格式正确")
        print("  ✅ 数据完整性良好")
        return True
    else:
        print("❌ 还有问题需要解决")
        return False

if __name__ == '__main__':
    verify_generated_file()
