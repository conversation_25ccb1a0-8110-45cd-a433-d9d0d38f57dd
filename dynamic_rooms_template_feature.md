# 动态考场模板生成功能

## 🎯 功能概述

第二步考场页面的模板下载功能现在会根据第一步设置的考试科目动态生成考场设置模板，确保模板与实际的考试科目完全匹配。

## ✨ 功能特点

### 1. 动态科目适配
- **自动获取科目**: 从第一步的科目设置中获取所有已配置的科目
- **动态列生成**: 根据科目数量和名称动态生成Excel列
- **实时同步**: 模板始终与当前的科目设置保持同步

### 2. 智能模板结构
- **标准格式**: 第一列为"考场"，后续列为各个科目名称
- **示例数据**: 提供5个示例考场（1考场-5考场）
- **默认需求**: 每个科目默认需要2个监考员
- **易于编辑**: 用户可以直接修改考场名称和监考员需求数量

### 3. 智能文件命名
- **科目信息**: 文件名包含科目信息，便于识别
- **长度控制**: 最多显示前3个科目名称
- **数量提示**: 超过3个科目时显示总数量

## 🔧 技术实现

### 路由修改
```python
@app.route('/wizard/download-template/<template_type>')
@login_required
def wizard_download_template(template_type):
    if template_type == 'rooms':
        # 考场模板根据已设置的科目动态生成
        return generate_rooms_template()
```

### 动态生成函数
```python
def generate_rooms_template():
    """根据第一步设置的科目动态生成考场模板"""
    # 1. 检查向导数据和科目设置
    # 2. 获取已设置的科目名称
    # 3. 创建包含科目列的模板结构
    # 4. 生成示例考场数据
    # 5. 创建Excel文件并返回
```

### 模板结构
```
考场    | 科目1 | 科目2 | 科目3 | ...
1考场   |   2   |   2   |   2   | ...
2考场   |   2   |   2   |   2   | ...
3考场   |   2   |   2   |   2   | ...
4考场   |   2   |   2   |   2   | ...
5考场   |   2   |   2   |   2   | ...
```

## 📋 使用流程

### 第一步：设置考试科目
1. 访问 http://localhost:5000/wizard/step1_subjects
2. 添加或导入考试科目（如：语文、数学、英语）
3. 确保科目设置完整并保存

### 第二步：下载动态考场模板
1. 访问 http://localhost:5000/wizard/step2_rooms
2. 点击"下载导入模板"按钮
3. 系统自动生成包含已设置科目的考场模板
4. 下载的文件名如：`考场设置模板_语文_数学_英语.xlsx`

### 第三步：填写考场信息
1. 在Excel中编辑考场名称（如：A101、A102等）
2. 设置每个科目在每个考场需要的监考员数量
3. 保存Excel文件

### 第四步：导入考场设置
1. 在第二步页面点击"从Excel导入"
2. 选择填写好的Excel文件
3. 系统自动解析并导入考场设置

## 🎯 使用示例

### 示例1：基础三科
**第一步设置的科目**:
- 语文 (A)
- 数学 (B) 
- 英语 (C)

**生成的模板**:
```
考场    | 语文 | 数学 | 英语
1考场   |  2   |  2   |  2
2考场   |  2   |  2   |  2
3考场   |  2   |  2   |  2
4考场   |  2   |  2   |  2
5考场   |  2   |  2   |  2
```

**下载文件名**: `考场设置模板_语文_数学_英语.xlsx`

### 示例2：理工科目
**第一步设置的科目**:
- 高等数学 (MATH)
- 大学物理 (PHYS)
- 线性代数 (LINALG)
- 概率论 (PROB)

**生成的模板**:
```
考场    | 高等数学 | 大学物理 | 线性代数 | 概率论
A101    |    2     |    2     |    2     |   2
A102    |    2     |    2     |    2     |   2
B201    |    2     |    2     |    2     |   2
B202    |    2     |    2     |    2     |   2
B203    |    2     |    2     |    2     |   2
```

**下载文件名**: `考场设置模板_高等数学_大学物理_线性代数等4科.xlsx`

## ✅ 功能优势

### 1. 数据一致性
- **避免错误**: 模板与实际科目完全匹配，避免手动输入错误
- **自动同步**: 科目变更时模板自动更新
- **格式统一**: 确保导入数据格式的一致性

### 2. 用户体验
- **操作简便**: 无需手动创建模板结构
- **即时可用**: 下载即可直接填写数据
- **清晰明了**: 文件名和内容都包含科目信息

### 3. 系统集成
- **无缝衔接**: 与现有的导入功能完美配合
- **向后兼容**: 不影响现有的静态模板功能
- **错误处理**: 完善的错误提示和异常处理

## 🔍 错误处理

### 1. 科目未设置
- **检查机制**: 访问考场模板下载前检查科目设置
- **友好提示**: "请先完成第一步：设置考试科目"
- **自动跳转**: 引导用户回到第一步完成科目设置

### 2. 数据异常
- **异常捕获**: 完整的try-catch错误处理
- **日志记录**: 详细的错误日志便于调试
- **用户反馈**: 清晰的错误提示信息

### 3. 文件生成失败
- **备用方案**: 生成失败时的降级处理
- **重试机制**: 支持用户重新尝试下载
- **技术支持**: 提供详细的错误信息

## 🚀 未来扩展

### 1. 更多定制选项
- 自定义考场数量
- 自定义默认监考员需求
- 考场命名规则设置

### 2. 模板样式优化
- Excel格式美化
- 数据验证规则
- 下拉选择列表

### 3. 批量操作支持
- 多个考场模板同时生成
- 不同科目组合的模板
- 模板版本管理

## 📝 总结

动态考场模板生成功能显著提升了监考安排系统的易用性和准确性。通过根据实际设置的科目动态生成模板，确保了数据的一致性，减少了用户的操作复杂度，提供了更加流畅的用户体验。

**核心价值**:
- ✅ **智能化**: 自动适配科目设置
- ✅ **准确性**: 避免手动输入错误  
- ✅ **便捷性**: 一键生成即用模板
- ✅ **一致性**: 确保数据格式统一
