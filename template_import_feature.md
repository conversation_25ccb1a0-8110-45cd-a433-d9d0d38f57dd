# 监考信息设置向导 - 模板下载和Excel导入功能

## 功能概述

为监考信息设置向导的三个步骤页面添加了下载模板和从Excel导入的功能：

1. **科目设置页面** (step1_subjects): 下载科目设置模板，导入科目信息
2. **考场设置页面** (step2_rooms): 下载考场设置模板，导入考场信息  
3. **监考员设置页面** (step3_proctors): 下载监考员设置模板，导入监考员信息

## 实现的功能

### 1. 模板文件生成
- 从原始 `监考安排.xlsx` 文件中提取各个工作簿，生成独立的模板文件
- 模板文件存放在 `/template-guide/` 目录下：
  - `kemu.xlsx` - 科目设置模板
  - `kaochang.xlsx` - 考场设置模板  
  - `jiankaoyuan.xlsx` - 监考员设置模板

### 2. 后端API接口

#### 模板下载路由
```python
@app.route('/wizard/download-template/<template_type>')
@login_required
def wizard_download_template(template_type):
```
- 支持三种模板类型：subjects, rooms, proctors
- 返回对应的Excel模板文件供用户下载

#### Excel导入路由
```python
@app.route('/wizard/import-excel/<step_type>', methods=['POST'])
@login_required  
def wizard_import_excel(step_type):
```
- 接收用户上传的Excel文件
- 调用对应的解析函数处理数据
- 返回JSON格式的结果

#### 数据解析函数
- `import_subjects_from_excel()` - 解析科目数据
- `import_rooms_from_excel()` - 解析考场数据
- `import_proctors_from_excel()` - 解析监考员数据

### 3. 前端功能增强

#### 科目设置页面 (step1_subjects.html)
- 添加"下载导入模板"按钮
- 添加"从Excel导入"按钮
- 使用Ajax上传和处理Excel文件
- 导入成功后自动更新表格数据

#### 考场设置页面 (step2_rooms.html)  
- 添加模板下载和Excel导入工具栏
- 集成到现有的考场管理界面

#### 监考员设置页面 (step3_proctors.html)
- 更新现有的Excel导入功能，使用新的后端API
- 保持原有的批量操作功能

### 4. 数据格式支持

#### 科目设置模板格式
| 课程代码 | 课程名称 | 开始时间 | 结束时间 |
|---------|---------|---------|---------|
| A | 语文 | 2025-02-08 09:00:00 | 2025-02-08 11:30:00 |

#### 考场设置模板格式
| 考场 | 语文 | 数学 | 英语 | ... |
|-----|-----|-----|-----|-----|
| 1考场 | 2 | 2 | 2 | ... |

#### 监考员设置模板格式
| 序号 | 监考老师 | 任教科目 | 必监考科目 | 不监考科目 | 必监考考场 | 不监考考场 | 场次限制 |
|-----|---------|---------|-----------|-----------|-----------|-----------|---------|
| 1 | 张老师 | 语文 | | | | | 5 |

## 使用流程

1. **下载模板**: 用户点击"下载导入模板"按钮，获取对应步骤的Excel模板
2. **填写数据**: 在模板中填写相关信息
3. **导入数据**: 点击"从Excel导入"按钮，上传填写好的Excel文件
4. **数据验证**: 系统自动验证数据格式和内容
5. **更新界面**: 导入成功后，页面数据自动更新

## 错误处理

- 文件格式验证：只接受.xlsx和.xls格式
- 文件大小限制：最大5MB
- 数据格式验证：检查必需列和数据类型
- 用户友好的错误提示和成功反馈

## 技术特性

- 支持多种工作簿名称（兼容不同的Excel文件格式）
- 自动数据清洗和类型转换
- 前端使用Ajax异步上传，提供加载提示
- 后端使用pandas处理Excel文件，确保数据准确性
- 临时文件自动清理，防止服务器存储占用

## 安全特性

- 需要用户登录才能访问
- 文件上传大小限制
- 临时文件存储在安全目录
- 文件处理后自动删除临时文件 