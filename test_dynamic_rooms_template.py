#!/usr/bin/env python3
"""
测试动态考场模板生成功能
验证考场模板是否根据第一步设置的科目动态生成
"""

import sys
import os
import pandas as pd
import io

def test_generate_rooms_template():
    """测试考场模板生成函数"""
    print("🧪 测试考场模板生成函数...")
    
    try:
        sys.path.append('.')
        
        # 模拟科目数据
        subjects = [
            {'subject_name': '语文', 'subject_code': 'A'},
            {'subject_name': '数学', 'subject_code': 'B'},
            {'subject_name': '英语', 'subject_code': 'C'}
        ]
        
        # 模拟生成模板的逻辑
        subject_names = [subject['subject_name'] for subject in subjects]
        
        # 创建考场模板数据
        template_data = []
        
        # 添加表头
        header = ['考场'] + subject_names
        template_data.append(header)
        
        # 添加示例数据行
        example_rooms = ['1考场', '2考场', '3考场', '4考场', '5考场']
        for room_name in example_rooms:
            row = [room_name] + [2] * len(subject_names)  # 每个科目默认需要2个监考员
            template_data.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(template_data[1:], columns=template_data[0])
        
        print(f"✅ 模板生成成功")
        print(f"   科目数量: {len(subject_names)}")
        print(f"   科目列表: {', '.join(subject_names)}")
        print(f"   考场数量: {len(example_rooms)}")
        print(f"   表格形状: {df.shape}")
        
        # 显示模板内容
        print("\n📋 生成的模板内容:")
        print(df.to_string(index=False))
        
        # 验证模板结构
        expected_columns = ['考场'] + subject_names
        if list(df.columns) == expected_columns:
            print(f"\n✅ 模板结构正确")
            return True
        else:
            print(f"\n❌ 模板结构错误")
            print(f"   期望列: {expected_columns}")
            print(f"   实际列: {list(df.columns)}")
            return False
            
    except Exception as e:
        print(f"❌ 模板生成失败: {e}")
        return False

def test_excel_generation():
    """测试Excel文件生成"""
    print("\n🧪 测试Excel文件生成...")
    
    try:
        # 模拟科目数据
        subjects = [
            {'subject_name': '高等数学', 'subject_code': 'MATH'},
            {'subject_name': '大学物理', 'subject_code': 'PHYS'},
            {'subject_name': '线性代数', 'subject_code': 'LINALG'}
        ]
        
        subject_names = [subject['subject_name'] for subject in subjects]
        
        # 创建考场模板数据
        template_data = []
        header = ['考场'] + subject_names
        template_data.append(header)
        
        example_rooms = ['A101', 'A102', 'B201', 'B202']
        for room_name in example_rooms:
            row = [room_name] + [2] * len(subject_names)
            template_data.append(row)
        
        df = pd.DataFrame(template_data[1:], columns=template_data[0])
        
        # 创建Excel文件到内存
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='考场设置', index=False)
            
            # 获取工作表
            worksheet = writer.sheets['考场设置']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 15  # 考场列
            for i in range(len(subject_names)):
                col_letter = chr(66 + i)  # B, C, D, ...
                worksheet.column_dimensions[col_letter].width = 12
        
        output.seek(0)
        excel_size = len(output.getvalue())
        
        print(f"✅ Excel文件生成成功")
        print(f"   文件大小: {excel_size} bytes")
        print(f"   工作表名: 考场设置")
        print(f"   数据行数: {len(df)}")
        print(f"   数据列数: {len(df.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel文件生成失败: {e}")
        return False

def test_filename_generation():
    """测试文件名生成逻辑"""
    print("\n🧪 测试文件名生成...")
    
    test_cases = [
        # (科目列表, 期望的文件名模式)
        (['语文', '数学'], '考场设置模板_语文_数学.xlsx'),
        (['语文', '数学', '英语'], '考场设置模板_语文_数学_英语.xlsx'),
        (['语文', '数学', '英语', '物理'], '考场设置模板_语文_数学_英语等4科.xlsx'),
        (['高等数学', '大学物理', '线性代数', '概率论', '离散数学'], '考场设置模板_高等数学_大学物理_线性代数等5科.xlsx')
    ]
    
    all_passed = True
    
    for subject_names, expected_pattern in test_cases:
        # 生成文件名
        subject_list = '_'.join(subject_names[:3])  # 最多显示前3个科目名
        if len(subject_names) > 3:
            subject_list += f'等{len(subject_names)}科'
        download_name = f'考场设置模板_{subject_list}.xlsx'
        
        if download_name == expected_pattern:
            print(f"   ✅ {len(subject_names)}科目: {download_name}")
        else:
            print(f"   ❌ {len(subject_names)}科目: 期望 {expected_pattern}, 实际 {download_name}")
            all_passed = False
    
    return all_passed

def test_integration_scenario():
    """测试完整的集成场景"""
    print("\n🧪 测试完整集成场景...")
    
    # 模拟用户在第一步设置的不同科目组合
    scenarios = [
        {
            'name': '基础三科',
            'subjects': [
                {'subject_name': '语文', 'subject_code': 'A'},
                {'subject_name': '数学', 'subject_code': 'B'},
                {'subject_name': '英语', 'subject_code': 'C'}
            ]
        },
        {
            'name': '理工科目',
            'subjects': [
                {'subject_name': '高等数学', 'subject_code': 'MATH'},
                {'subject_name': '大学物理', 'subject_code': 'PHYS'},
                {'subject_name': '线性代数', 'subject_code': 'LINALG'},
                {'subject_name': '概率论', 'subject_code': 'PROB'}
            ]
        },
        {
            'name': '单科考试',
            'subjects': [
                {'subject_name': '计算机基础', 'subject_code': 'CS'}
            ]
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        subjects = scenario['subjects']
        subject_names = [s['subject_name'] for s in subjects]
        
        try:
            # 生成模板
            header = ['考场'] + subject_names
            template_data = [header]
            
            for i in range(3):  # 生成3个考场
                room_name = f'{i+1}考场'
                row = [room_name] + [2] * len(subject_names)
                template_data.append(row)
            
            df = pd.DataFrame(template_data[1:], columns=template_data[0])
            
            print(f"   ✅ 科目数: {len(subject_names)}")
            print(f"   ✅ 列数: {len(df.columns)}")
            print(f"   ✅ 行数: {len(df)}")
            print(f"   ✅ 列名: {list(df.columns)}")
            
        except Exception as e:
            print(f"   ❌ 场景失败: {e}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🔧 动态考场模板生成功能测试")
    print("=" * 50)
    print("测试考场模板是否根据第一步设置的科目动态生成")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试模板生成函数
    test_results.append(("模板生成函数", test_generate_rooms_template()))
    
    # 2. 测试Excel文件生成
    test_results.append(("Excel文件生成", test_excel_generation()))
    
    # 3. 测试文件名生成
    test_results.append(("文件名生成", test_filename_generation()))
    
    # 4. 测试集成场景
    test_results.append(("集成场景测试", test_integration_scenario()))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 功能特点:")
        print("✅ 根据第一步设置的科目动态生成考场模板")
        print("✅ 模板包含考场列和所有科目列")
        print("✅ 提供示例考场和默认监考员需求")
        print("✅ 生成有意义的文件名")
        print("✅ 支持任意数量的科目")
        
        print("\n🚀 使用方法:")
        print("1. 在第一步设置考试科目")
        print("2. 在第二步点击'下载导入模板'")
        print("3. 获得包含已设置科目的考场模板")
        print("4. 填写考场和监考员需求数据")
        print("5. 上传模板完成考场设置")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
