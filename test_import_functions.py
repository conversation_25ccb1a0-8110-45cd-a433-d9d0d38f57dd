#!/usr/bin/env python3
"""
直接测试Excel导入函数的脚本
不需要通过Web界面，直接测试后端的导入函数
"""

import sys
import os
sys.path.append('.')

# 导入应用的导入函数
try:
    from app import import_subjects_from_excel, import_rooms_from_excel, import_proctors_from_excel
    print("✅ 成功导入应用模块")
except ImportError as e:
    print(f"❌ 无法导入应用模块: {e}")
    sys.exit(1)

def test_subjects_import():
    """测试科目导入功能"""
    print("\n=== 测试科目导入功能 ===")
    template_file = 'template-guide/kemu.xlsx'
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        subjects = import_subjects_from_excel(template_file)
        print(f"✅ 科目导入成功，共导入 {len(subjects)} 个科目")
        
        # 显示导入的数据
        for i, subject in enumerate(subjects[:3]):  # 只显示前3个
            print(f"  {i+1}. {subject}")
        
        if len(subjects) > 3:
            print(f"  ... 还有 {len(subjects) - 3} 个科目")
        
        return True
        
    except Exception as e:
        print(f"❌ 科目导入失败: {e}")
        return False

def test_rooms_import():
    """测试考场导入功能"""
    print("\n=== 测试考场导入功能 ===")
    template_file = 'template-guide/kaochang.xlsx'
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        rooms = import_rooms_from_excel(template_file)
        print(f"✅ 考场导入成功，共导入 {len(rooms)} 个考场")
        
        # 显示导入的数据
        for i, room in enumerate(rooms[:3]):  # 只显示前3个
            print(f"  {i+1}. {room}")
        
        if len(rooms) > 3:
            print(f"  ... 还有 {len(rooms) - 3} 个考场")
        
        return True
        
    except Exception as e:
        print(f"❌ 考场导入失败: {e}")
        return False

def test_proctors_import():
    """测试监考员导入功能"""
    print("\n=== 测试监考员导入功能 ===")
    template_file = 'template-guide/jiankaoyuan.xlsx'
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        proctors = import_proctors_from_excel(template_file)
        print(f"✅ 监考员导入成功，共导入 {len(proctors)} 个监考员")
        
        # 显示导入的数据
        for i, proctor in enumerate(proctors[:3]):  # 只显示前3个
            print(f"  {i+1}. {proctor}")
        
        if len(proctors) > 3:
            print(f"  ... 还有 {len(proctors) - 3} 个监考员")
        
        return True
        
    except Exception as e:
        print(f"❌ 监考员导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试Excel导入函数...")
    
    success_count = 0
    total_count = 3
    
    # 测试各个导入函数
    if test_subjects_import():
        success_count += 1
    
    if test_rooms_import():
        success_count += 1
    
    if test_proctors_import():
        success_count += 1
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有Excel导入函数测试通过！")
        return 0
    else:
        print("❌ 部分Excel导入函数测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
