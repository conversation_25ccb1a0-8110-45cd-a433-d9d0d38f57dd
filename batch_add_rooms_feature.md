# 批量添加考场功能

## 🎯 功能概述

为第二步考场页面添加了批量添加考场功能，用户可以一次性添加多个考场，并为每个科目设置默认的监考员数量，大大提高了考场设置的效率。

## ✨ 功能特点

### 1. 智能批量添加
- **数量控制**: 一次可添加1-50个考场
- **自定义命名**: 支持考场前缀和起始编号设置
- **实时预览**: 显示将要添加的考场名称
- **默认设置**: 为每个科目设置默认监考员数量

### 2. 灵活的命名规则
- **无前缀**: 1考场、2考场、3考场...
- **字母前缀**: A1考场、A2考场、A3考场...
- **数字前缀**: 101考场、102考场、103考场...
- **文字前缀**: 1号楼1考场、1号楼2考场...

### 3. 智能默认设置
- **科目适配**: 自动显示第一步设置的所有科目
- **默认人数**: 每个科目可设置不同的默认监考员数量
- **批量应用**: 所有新增考场都使用相同的默认设置

## 🔧 界面设计

### 批量添加按钮
```html
<button type="button" id="batch-add-room-btn" class="btn btn-success" 
        data-bs-toggle="modal" data-bs-target="#batchAddModal">
    <i class="fas fa-layer-group me-2"></i>批量添加
</button>
```

### 模态框界面
- **考场数量**: 数字输入框，范围1-50
- **考场前缀**: 文本输入框，可选
- **起始编号**: 数字输入框，默认为1
- **默认监考人数**: 每个科目的监考员数量设置
- **实时预览**: 显示将要添加的考场名称

## 📋 使用流程

### 第一步：打开批量添加
1. 在考场设置页面点击"批量添加"按钮
2. 弹出批量添加模态框

### 第二步：设置考场信息
1. **设置数量**: 输入要添加的考场数量（1-50）
2. **设置前缀**: 输入考场名称前缀（可选）
3. **设置起始编号**: 输入考场编号的起始数字
4. **查看预览**: 实时查看将要添加的考场名称

### 第三步：设置监考人数
1. 为每个科目设置默认的监考员数量
2. 通常设置为2人，特殊情况可调整

### 第四步：确认添加
1. 检查预览信息是否正确
2. 点击"确认添加"按钮
3. 系统自动添加所有考场到表格中

## 🎯 使用示例

### 示例1：基础数字编号
**设置**:
- 考场数量: 5
- 考场前缀: (留空)
- 起始编号: 1
- 默认监考人数: 语文2人，数学2人，英语2人

**结果**:
```
考场    | 语文 | 数学 | 英语
1考场   |  2   |  2   |  2
2考场   |  2   |  2   |  2
3考场   |  2   |  2   |  2
4考场   |  2   |  2   |  2
5考场   |  2   |  2   |  2
```

### 示例2：字母前缀编号
**设置**:
- 考场数量: 3
- 考场前缀: A
- 起始编号: 101
- 默认监考人数: 高等数学2人，大学物理1人

**结果**:
```
考场      | 高等数学 | 大学物理
A101考场  |    2     |    1
A102考场  |    2     |    1
A103考场  |    2     |    1
```

### 示例3：楼层前缀编号
**设置**:
- 考场数量: 4
- 考场前缀: 1号楼
- 起始编号: 1
- 默认监考人数: 计算机基础3人

**结果**:
```
考场        | 计算机基础
1号楼1考场  |     3
1号楼2考场  |     3
1号楼3考场  |     3
1号楼4考场  |     3
```

## 🔧 技术实现

### JavaScript核心逻辑
```javascript
// 预览更新函数
function updateBatchAddPreview() {
    var count = parseInt($('#room-count').val()) || 0;
    var prefix = $('#room-prefix').val().trim();
    var startNumber = parseInt($('#start-number').val()) || 1;
    
    var roomNames = [];
    for (var i = 0; i < Math.min(count, 5); i++) {
        var roomName = prefix ? (prefix + (startNumber + i) + '考场') : ((startNumber + i) + '考场');
        roomNames.push(roomName);
    }
    
    var previewText = '将添加 ' + count + ' 个考场：' + roomNames.join(', ');
    if (count > 5) {
        previewText += ' ...';
    }
    
    $('#preview-text').text(previewText);
}

// 批量添加确认
$('#confirm-batch-add').click(function() {
    // 收集设置
    var count = parseInt($('#room-count').val()) || 0;
    var prefix = $('#room-prefix').val().trim();
    var startNumber = parseInt($('#start-number').val()) || 1;
    
    // 收集默认监考人数
    var defaultDemands = {};
    $('.default-demand').each(function() {
        var subject = $(this).data('subject');
        var demand = parseInt($(this).val()) || 0;
        defaultDemands[subject] = demand;
    });
    
    // 批量添加考场
    for (var i = 0; i < count; i++) {
        var roomName = prefix ? (prefix + (startNumber + i) + '考场') : ((startNumber + i) + '考场');
        rooms.push({
            name: roomName,
            demands: Object.assign({}, defaultDemands)
        });
    }
    
    // 重新渲染表格
    renderRooms();
});
```

### 输入验证
- **数量验证**: 1-50范围检查
- **编号验证**: 正整数检查
- **前缀验证**: 可选文本输入
- **监考人数验证**: 非负整数检查

## ✅ 功能优势

### 1. 效率提升
- **批量操作**: 一次添加多个考场，避免重复操作
- **默认设置**: 统一的监考员数量设置
- **智能命名**: 自动生成规范的考场名称

### 2. 灵活性强
- **自定义前缀**: 适应不同的考场命名规则
- **可调编号**: 支持任意起始编号
- **个性化设置**: 每个科目可设置不同的监考员数量

### 3. 用户友好
- **实时预览**: 即时查看添加结果
- **输入验证**: 防止无效输入
- **成功反馈**: 清晰的操作结果提示

## 🔍 错误处理

### 1. 输入验证
- **数量超限**: "请输入有效的考场数量（1-50）"
- **无效编号**: 自动修正为有效的正整数
- **空值处理**: 提供合理的默认值

### 2. 操作反馈
- **成功提示**: 显示添加的考场数量
- **自动关闭**: 5秒后自动关闭成功提示
- **模态框管理**: 操作完成后自动关闭模态框

## 🚀 使用建议

### 1. 命名规范
- **统一前缀**: 同一楼层或区域使用相同前缀
- **连续编号**: 使用连续的数字编号便于管理
- **简洁明了**: 避免过长的考场名称

### 2. 监考人数设置
- **标准配置**: 一般考场设置2名监考员
- **特殊考场**: 大型考场或特殊考试可增加人数
- **科目差异**: 不同科目可根据需要设置不同人数

### 3. 批量操作
- **分批添加**: 大量考场可分批添加，便于管理
- **及时保存**: 添加完成后及时保存设置
- **检查确认**: 添加后检查考场信息是否正确

## 📝 总结

批量添加考场功能显著提升了考场设置的效率和便利性。通过智能的命名规则、灵活的默认设置和友好的用户界面，用户可以快速完成大量考场的添加工作，为监考安排的后续步骤奠定了良好的基础。

**核心价值**:
- ✅ **高效批量**: 一次操作添加多个考场
- ✅ **智能命名**: 自动生成规范考场名称
- ✅ **灵活设置**: 支持多种命名和配置方式
- ✅ **用户友好**: 直观的界面和实时预览
