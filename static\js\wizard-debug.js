/**
 * 向导页面Excel导入功能诊断脚本
 * 在浏览器控制台中运行此脚本来诊断问题
 */

(function() {
    'use strict';
    
    console.log('%c=== 向导页面Excel导入诊断工具 ===', 'color: blue; font-size: 16px; font-weight: bold;');
    
    // 等待DOM加载完成
    function waitForDOM(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }
    
    waitForDOM(function() {
        console.log('\n📋 页面基础信息:');
        console.log('当前URL:', window.location.href);
        console.log('Document Ready State:', document.readyState);
        console.log('jQuery版本:', typeof $ !== 'undefined' ? $.fn.jquery : '未加载');
        
        // 检查各步骤的Excel导入按钮
        const stepConfigs = [
            { name: '科目设置', btnId: 'import-excel-btn-subjects', resultId: 'import-result-subjects' },
            { name: '考场设置', btnId: 'import-excel-btn-rooms', resultId: 'import-result-rooms' },
            { name: '监考员设置', btnId: 'import-excel-btn-proctors', resultId: 'import-result-proctors' }
        ];
        
        console.log('\n🔍 检查Excel导入按钮:');
        stepConfigs.forEach(config => {
            const btn = document.getElementById(config.btnId);
            const resultArea = document.getElementById(config.resultId);
            
            console.log(`\n${config.name}:`);
            console.log(`  按钮元素 (#${config.btnId}):`, btn ? '✅ 存在' : '❌ 不存在');
            console.log(`  结果区域 (#${config.resultId}):`, resultArea ? '✅ 存在' : '❌ 不存在');
            
            if (btn) {
                // 检查事件监听器
                const hasClickListener = getEventListeners ? 
                    getEventListeners(btn).click?.length > 0 : 
                    '需要在Chrome DevTools中运行';
                console.log(`  点击事件监听器:`, hasClickListener ? '✅ 已绑定' : '❌ 未绑定');
                
                // 添加测试点击事件
                console.log(`  按钮可点击性: ✅ 可测试 (运行 testButton('${config.btnId}') 来测试)`);
                
                // 在全局作用域添加测试函数
                window[`test${config.name.replace(/设置/g, '')}Button`] = function() {
                    console.log(`🧪 测试 ${config.name} 导入按钮...`);
                    try {
                        btn.click();
                        console.log(`✅ ${config.name} 按钮点击成功`);
                    } catch (error) {
                        console.error(`❌ ${config.name} 按钮点击失败:`, error);
                    }
                };
            }
        });
        
        // 检查CSRF令牌
        console.log('\n🔐 检查CSRF令牌:');
        const csrfToken = document.querySelector('input[name=csrf_token]');
        console.log('CSRF令牌元素:', csrfToken ? '✅ 存在' : '❌ 不存在');
        if (csrfToken) {
            console.log('CSRF令牌值:', csrfToken.value ? '✅ 有值' : '❌ 无值');
        }
        
        // 检查JavaScript错误
        console.log('\n🐛 JavaScript错误监控已启用');
        window.addEventListener('error', function(e) {
            console.error('🚨 JavaScript错误:', e.error);
            console.error('文件:', e.filename);
            console.error('行号:', e.lineno);
            console.error('列号:', e.colno);
        });
        
        // 提供快速测试功能
        window.testAllButtons = function() {
            console.log('\n🧪 测试所有Excel导入按钮...');
            stepConfigs.forEach(config => {
                const btn = document.getElementById(config.btnId);
                if (btn) {
                    console.log(`测试 ${config.name}...`);
                    try {
                        btn.click();
                        console.log(`✅ ${config.name} 按钮正常`);
                    } catch (error) {
                        console.error(`❌ ${config.name} 按钮失败:`, error);
                    }
                } else {
                    console.warn(`⚠️ ${config.name} 按钮不存在`);
                }
            });
        };
        
        console.log('\n💡 可用命令:');
        console.log('- testAllButtons(): 测试所有Excel导入按钮');
        console.log('- test科目Button(): 测试科目导入按钮');
        console.log('- test考场Button(): 测试考场导入按钮');
        console.log('- test监考员Button(): 测试监考员导入按钮');
        
        console.log('\n✅ 诊断完成！查看上方结果找出问题。');
    });
})(); 