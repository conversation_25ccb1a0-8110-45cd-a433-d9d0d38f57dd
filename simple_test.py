#!/usr/bin/env python3
"""
简单测试脚本
"""

import pandas as pd
from datetime import datetime

# 测试数据
wizard_data = {
    'subjects': [
        {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
        {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'}
    ],
    'proctors': [
        {'name': '张老师', 'session_limit': 3}
    ],
    'rooms': [
        {'name': 'A101', 'demands': {'语文': 2, '数学': 1}}
    ]
}

# 简化的生成函数
def simple_generate(wizard_data, file_path):
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        
        # 解析时间
        exam_date = '2025/01/01'
        start_time = '09:00'
        end_time = '11:00'
        
        if start_time_str:
            try:
                dt = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
                exam_date = dt.strftime('%Y/%m/%d')
                start_time = dt.strftime('%H:%M')
            except:
                pass
        
        end_time_str = subject.get('end_time', '')
        if end_time_str:
            try:
                dt = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
                end_time = dt.strftime('%H:%M')
            except:
                pass
        
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': exam_date,
            '开始时间': start_time,
            '结束时间': end_time
        })
    
    df = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 监考员设置
        proctors_data = []
        for proctor in wizard_data.get('proctors', []):
            proctors_data.append({
                '监考老师': proctor.get('name', '').strip(),
                '场次限制': proctor.get('session_limit', 99)
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 考场设置
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

# 生成测试文件
test_file = 'simple_test.xlsx'
print("生成测试文件...")
simple_generate(wizard_data, test_file)

# 验证结果
df = pd.read_excel(test_file, sheet_name='考试科目设置')
print("\n生成的文件内容:")
print("列名:", list(df.columns))
print("数据:")
print(df.to_string())

# 检查必需的列
required_columns = ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
missing = [col for col in required_columns if col not in df.columns]

if missing:
    print(f"\n❌ 缺少列: {missing}")
else:
    print("\n✅ 包含所有必需的列")

print(f"\n✅ 测试完成，生成文件: {test_file}")
