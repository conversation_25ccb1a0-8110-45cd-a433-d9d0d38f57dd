<!-- Excel导入通用组件 -->
<div class="excel-import-component">
    <div class="mb-4">
        <div class="d-flex align-items-center flex-wrap gap-2">
            <button type="button" id="import-excel-btn-{{ component_id }}" class="btn btn-outline-primary">
                <i class="fas fa-file-excel me-2"></i>从Excel导入
            </button>
            <input type="file" id="excel-file-{{ component_id }}" accept=".xlsx,.xls" style="display: none;">
            <a href="{{ template_download_url }}" id="download-template-btn-{{ component_id }}" class="btn btn-outline-secondary">
                <i class="fas fa-download me-2"></i>下载{{ template_name }}模板
            </a>
            <div class="flex-grow-1"></div>
            {% if show_add_button %}
            <button type="button" id="add-item-btn-{{ component_id }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ add_button_text }}
            </button>
            {% endif %}
        </div>
    </div>
    
    <!-- 导入结果显示区域 -->
    <div id="import-result-{{ component_id }}" class="import-result-area"></div>
</div>

<script>
// Excel导入组件JavaScript
(function() {
    'use strict';
    
    const componentId = '{{ component_id }}';
    const importType = '{{ import_type }}';
    const importUrl = '/wizard/import-excel/' + importType;
    
    console.log(`初始化Excel导入组件: ${componentId}, 类型: ${importType}`);
    
    // 获取组件元素
    const importBtn = document.getElementById(`import-excel-btn-${componentId}`);
    const fileInput = document.getElementById(`excel-file-${componentId}`);
    const resultArea = document.getElementById(`import-result-${componentId}`);
    
    if (!importBtn || !fileInput || !resultArea) {
        console.error('Excel导入组件元素未找到');
        return;
    }
    
    // 导入按钮点击事件
    importBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log(`${componentId}: Excel导入按钮被点击`);
        
        // 创建动态文件输入
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.style.display = 'none';
        
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (!file) {
                console.log(`${componentId}: 没有选择文件`);
                return;
            }
            
            console.log(`${componentId}: 文件已选择:`, file.name, file.size, 'bytes');
            handleFileUpload(file);
            
            // 清理临时输入元素
            if (input.parentNode) {
                input.parentNode.removeChild(input);
            }
        };
        
        document.body.appendChild(input);
        input.click();
    });
    
    // 文件处理函数
    function handleFileUpload(file) {
        console.log(`${componentId}: 开始处理文件:`, file.name);
        
        // 验证文件类型
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        if (!allowedTypes.includes(file.type)) {
            showMessage('请上传Excel文件（.xls或.xlsx格式）', 'danger');
            return;
        }
        
        // 验证文件大小（5MB限制）
        if (file.size > 5 * 1024 * 1024) {
            showMessage('文件大小不能超过5MB', 'danger');
            return;
        }
        
        // 获取CSRF令牌
        const csrfToken = document.querySelector('input[name=csrf_token]')?.value;
        if (!csrfToken) {
            showMessage('CSRF令牌未找到，请刷新页面重试', 'danger');
            return;
        }
        
        // 显示加载状态
        showLoadingMessage();
        
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('csrf_token', csrfToken);
        
        // 发送请求
        fetch(importUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log(`${componentId}: 服务器响应状态:`, response.status);
            return response.json();
        })
        .then(data => {
            console.log(`${componentId}: 导入成功:`, data);
            hideLoadingMessage();
            
            if (data.success) {
                showMessage(data.message || '导入成功', 'success');
                
                // 触发自定义事件，通知页面数据已更新
                const event = new CustomEvent('excelImportSuccess', {
                    detail: {
                        componentId: componentId,
                        importType: importType,
                        data: data.data
                    }
                });
                document.dispatchEvent(event);
                
                // 延迟关闭成功消息
                setTimeout(() => {
                    hideMessage();
                }, 5000);
            } else {
                showMessage(data.message || '导入失败', 'danger');
            }
        })
        .catch(error => {
            console.error(`${componentId}: 导入失败:`, error);
            hideLoadingMessage();
            
            let errorMessage = '导入失败，请稍后重试';
            if (error.message.includes('403')) {
                errorMessage = '权限不足或CSRF验证失败，请刷新页面重试';
            } else if (error.message.includes('413')) {
                errorMessage = '文件过大，请选择较小的文件';
            } else if (error.message.includes('500')) {
                errorMessage = '服务器内部错误，请联系管理员';
            }
            
            showMessage(errorMessage, 'danger');
        });
    }
    
    // 显示加载消息
    function showLoadingMessage() {
        resultArea.innerHTML = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-spinner fa-spin me-2"></i>正在导入Excel文件，请稍候...
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
    
    // 隐藏加载消息
    function hideLoadingMessage() {
        const loadingAlert = resultArea.querySelector('.alert-info');
        if (loadingAlert) {
            loadingAlert.remove();
        }
    }
    
    // 显示消息
    function showMessage(message, type = 'info') {
        const alertClass = `alert-${type}`;
        const iconClass = type === 'success' ? 'fa-check-circle' : 
                         type === 'danger' ? 'fa-exclamation-circle' : 
                         'fa-info-circle';
        
        resultArea.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas ${iconClass} me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
    
    // 隐藏消息
    function hideMessage() {
        resultArea.innerHTML = '';
    }
    
    console.log(`Excel导入组件 ${componentId} 初始化完成`);
})();
</script> 