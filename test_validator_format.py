#!/usr/bin/env python3
"""
测试验证器格式兼容性
"""

import pandas as pd
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def generate_validator_compatible_excel(wizard_data, file_path):
    """生成与验证器兼容的Excel文件"""
    
    # 1. 考试科目设置表 - 验证器兼容格式
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')
        
        start_datetime = None
        end_datetime = None
        
        try:
            if start_time_str:
                start_datetime = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
                        
            if end_time_str:
                end_datetime = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
        except Exception as e:
            print(f"解析时间时出错: {e}")
            
        if not start_datetime:
            start_datetime = datetime(2025, 1, 1, 9, 0)
        if not end_datetime:
            end_datetime = datetime(2025, 1, 1, 11, 0)
            
        subjects_data.append({
            '课程代码': subject.get('code', '').strip(),
            '课程名称': subject.get('name', '').strip(),
            '考试科目': subject.get('name', '').strip(),
            '考试日期': start_datetime.strftime('%Y/%m/%d'),
            '开始时间': start_datetime.strftime('%H:%M'),  # 验证器要求的HH:MM格式
            '结束时间': end_datetime.strftime('%H:%M')    # 验证器要求的HH:MM格式
        })
    
    df_subjects = pd.DataFrame(subjects_data)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)
        
        # 2. 监考员设置表
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            session_limit = proctor.get('session_limit', 99)
            try:
                session_limit = int(session_limit)
                if session_limit <= 0:
                    session_limit = 99
            except (ValueError, TypeError):
                session_limit = 99
            
            proctors_data.append({
                '序号': i,
                '监考老师': proctor.get('name', '').strip(),
                '任教科目': proctor.get('teaching_subject', ''),
                '场次限制': session_limit,
                '必监考科目': proctor.get('required_subjects', ''),
                '不监考科目': proctor.get('forbidden_subjects', ''),
                '必监考考场': proctor.get('required_rooms', ''),
                '不监考考场': proctor.get('forbidden_rooms', '')
            })
        
        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)
        
        # 3. 考场设置表
        rooms_data = []
        subject_names = [s['name'].strip() for s in wizard_data.get('subjects', [])]
        
        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})
            
            for subj in subject_names:
                room_data[subj] = demands.get(subj, 0)
            
            rooms_data.append(room_data)
        
        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

def test_validator():
    """测试验证器"""
    try:
        from excel_validator import ExcelValidator
        
        test_file = 'test_validator_format_new.xlsx'
        if not os.path.exists(test_file):
            print(f"测试文件 {test_file} 不存在")
            return False
        
        print(f"使用验证器测试文件: {test_file}")
        validator = ExcelValidator(test_file)
        is_valid = validator.validate()
        
        if is_valid:
            print("✅ 验证器测试通过")
        else:
            print("❌ 验证器测试失败")
        
        if validator.errors:
            print("\n错误信息:")
            for error in validator.errors:
                print(f"  - {error}")
        
        if validator.warnings:
            print("\n警告信息:")
            for warning in validator.warnings:
                print(f"  - {warning}")
        
        return is_valid
        
    except Exception as e:
        print(f"验证器测试时出错: {e}")
        return False

def main():
    # 测试数据
    wizard_data = {
        'subjects': [
            {'code': 'A', 'name': '语文', 'start_time': '2025/02/08 09:00', 'end_time': '2025/02/08 11:30'},
            {'code': 'B', 'name': '数学', 'start_time': '2025/02/08 14:00', 'end_time': '2025/02/08 16:00'},
            {'code': 'C', 'name': '英语', 'start_time': '2025/02/09 09:00', 'end_time': '2025/02/09 11:00'}
        ],
        'proctors': [
            {'name': '张老师', 'session_limit': 3, 'teaching_subject': '语文'},
            {'name': '李老师', 'session_limit': 2, 'teaching_subject': '数学'},
            {'name': '王老师', 'session_limit': 2, 'teaching_subject': '英语'}
        ],
        'rooms': [
            {'name': 'A101', 'demands': {'语文': 2, '数学': 1, '英语': 1}},
            {'name': 'A102', 'demands': {'语文': 1, '数学': 2, '英语': 1}},
            {'name': 'B201', 'demands': {'语文': 1, '数学': 1, '英语': 2}}
        ]
    }

    test_file = 'test_validator_format_new.xlsx'
    print("生成验证器兼容的Excel文件...")
    generate_validator_compatible_excel(wizard_data, test_file)
    print(f"✅ 已生成文件: {test_file}")

    # 检查文件内容
    df = pd.read_excel(test_file, sheet_name='考试科目设置')
    print("\n=== 文件内容检查 ===")
    print("考试科目设置表:")
    print("列名:", list(df.columns))
    print("数据:")
    print(df.to_string())
    
    print("\n时间格式检查:")
    for i, row in df.iterrows():
        start_time = row['开始时间']
        end_time = row['结束时间']
        exam_date = row['考试日期']
        print(f"  {row['课程名称']}: 日期={exam_date}, 开始={start_time}, 结束={end_time}")
        print(f"    类型: 日期={type(exam_date).__name__}, 开始={type(start_time).__name__}, 结束={type(end_time).__name__}")

    # 测试验证器
    success = test_validator()
    
    if success:
        print(f"\n🎉 验证器测试成功！文件 {test_file} 通过验证")
    else:
        print(f"\n❌ 验证器测试失败")

if __name__ == '__main__':
    main()
