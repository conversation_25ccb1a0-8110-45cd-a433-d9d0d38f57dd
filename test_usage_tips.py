#!/usr/bin/env python3
"""
测试使用提示的添加
验证step3_proctors页面的使用提示是否与step2_rooms保持一致的格式
"""

import os

def check_step2_usage_tips():
    """检查step2_rooms页面的使用提示"""
    print("🔍 检查step2_rooms页面的使用提示...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    
    if not os.path.exists(step2_file):
        print(f"❌ 页面模板不存在: {step2_file}")
        return False, {}
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取step2的使用提示信息
        step2_info = {
            '使用提示标题': '使用提示' in content,
            '信息图标': 'fas fa-info-circle fa-2x' in content,
            '警告框样式': 'alert alert-info mb-4' in content,
            '三列布局': 'col-md-4' in content,
            '列表样式': 'mb-0 ps-3 small' in content,
            '加粗标题': '<strong>' in content,
            'flex布局': 'd-flex' in content and 'flex-grow-1' in content,
            '提示内容数量': content.count('<li><strong>') >= 6
        }
        
        print("   📊 step2_rooms使用提示:")
        for info_name, exists in step2_info.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {info_name}")
        
        # 提取具体的提示内容
        tips_content = []
        if '添加考场：' in content:
            tips_content.append('添加考场')
        if '设置人数：' in content:
            tips_content.append('设置人数')
        if '批量操作：' in content:
            tips_content.append('批量操作')
        if 'Excel导入：' in content:
            tips_content.append('Excel导入')
        
        print(f"   📝 提示内容: {tips_content}")
        
        return True, step2_info
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, {}

def check_step3_usage_tips():
    """检查step3_proctors页面的使用提示"""
    print("\n🔍 检查step3_proctors页面的使用提示...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    if not os.path.exists(step3_file):
        print(f"❌ 页面模板不存在: {step3_file}")
        return False, {}
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取step3的使用提示信息
        step3_info = {
            '使用提示标题': '使用提示' in content,
            '信息图标': 'fas fa-info-circle fa-2x' in content,
            '警告框样式': 'alert alert-info mb-4' in content,
            '三列布局': 'col-md-4' in content,
            '列表样式': 'mb-0 ps-3 small' in content,
            '加粗标题': '<strong>' in content,
            'flex布局': 'd-flex' in content and 'flex-grow-1' in content,
            '提示内容数量': content.count('<li><strong>') >= 9,
            '位置正确': '使用提示' in content and content.find('使用提示') < content.find('从Excel导入')
        }
        
        print("   📊 step3_proctors使用提示:")
        for info_name, exists in step3_info.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {info_name}")
        
        # 提取具体的提示内容
        tips_content = []
        monitoring_tips = [
            '添加监考员', '任教科目', '科目偏好', '考场偏好', 
            '场次限制', '批量操作', '筛选排序', '弹出编辑', 'Excel模板'
        ]
        
        for tip in monitoring_tips:
            if tip in content:
                tips_content.append(tip)
        
        print(f"   📝 提示内容: {tips_content}")
        
        return True, step3_info
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, {}

def compare_usage_tips_format():
    """比较使用提示格式一致性"""
    print("\n🔍 比较使用提示格式一致性...")
    
    step2_file = 'templates/wizard/step2_rooms.html'
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step2_file, 'r', encoding='utf-8') as f:
            step2_content = f.read()
        
        with open(step3_file, 'r', encoding='utf-8') as f:
            step3_content = f.read()
        
        # 比较格式一致性
        format_checks = {
            '警告框样式一致': {
                'step2': 'alert alert-info mb-4' in step2_content,
                'step3': 'alert alert-info mb-4' in step3_content,
                'description': '使用相同的信息警告框样式'
            },
            '图标样式一致': {
                'step2': 'fas fa-info-circle fa-2x me-3' in step2_content,
                'step3': 'fas fa-info-circle fa-2x me-3' in step3_content,
                'description': '使用相同的信息图标和大小'
            },
            '布局结构一致': {
                'step2': 'd-flex' in step2_content and 'flex-grow-1' in step2_content,
                'step3': 'd-flex' in step3_content and 'flex-grow-1' in step3_content,
                'description': '使用相同的flex布局结构'
            },
            '三列布局一致': {
                'step2': step2_content.count('col-md-4') >= 3,
                'step3': step3_content.count('col-md-4') >= 3,
                'description': '都使用三列布局展示提示'
            },
            '列表样式一致': {
                'step2': 'mb-0 ps-3 small' in step2_content,
                'step3': 'mb-0 ps-3 small' in step3_content,
                'description': '使用相同的列表样式'
            }
        }
        
        all_consistent = True
        
        for check_name, check_info in format_checks.items():
            step2_ok = check_info['step2']
            step3_ok = check_info['step3']
            consistent = step2_ok and step3_ok
            
            status = "✅" if consistent else "❌"
            print(f"   {status} {check_name}")
            print(f"      说明: {check_info['description']}")
            print(f"      step2: {'✅' if step2_ok else '❌'}, step3: {'✅' if step3_ok else '❌'}")
            
            if not consistent:
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 比较失败: {e}")
        return False

def analyze_usage_tips_content():
    """分析使用提示内容"""
    print("\n🔍 分析使用提示内容...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析提示内容的覆盖面
        content_categories = {
            '基础操作': {
                'tips': ['添加监考员', '任教科目'],
                'description': '监考员的基本添加和信息设置'
            },
            '偏好设置': {
                'tips': ['科目偏好', '考场偏好', '场次限制'],
                'description': '监考员的监考偏好和限制设置'
            },
            '批量管理': {
                'tips': ['批量操作', '筛选排序', 'Excel模板'],
                'description': '批量处理和数据管理功能'
            },
            '高级功能': {
                'tips': ['弹出编辑'],
                'description': '高级的编辑和交互功能'
            }
        }
        
        all_covered = True
        
        for category_name, category_info in content_categories.items():
            print(f"\n   {category_name}:")
            print(f"      描述: {category_info['description']}")
            print(f"      提示:")
            
            for tip in category_info['tips']:
                if tip in content:
                    print(f"         ✅ {tip}")
                else:
                    print(f"         ❌ {tip}")
                    all_covered = False
        
        return all_covered
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_tips_positioning():
    """检查提示位置"""
    print("\n🔍 检查提示位置...")
    
    step3_file = 'templates/wizard/step3_proctors.html'
    
    try:
        with open(step3_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查位置相关
        positioning_checks = {
            '在卡片主体内': 'card-body' in content and '使用提示' in content,
            '在表单之前': content.find('使用提示') < content.find('<form'),
            '在操作按钮之前': content.find('使用提示') < content.find('从Excel导入'),
            '在表格之前': content.find('使用提示') < content.find('proctors-table'),
            '有适当间距': 'mb-4' in content and '使用提示' in content
        }
        
        all_passed = True
        for check_name, result in positioning_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if not result:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def simulate_user_reading_experience():
    """模拟用户阅读体验"""
    print("\n🧪 模拟用户阅读体验...")
    
    reading_scenarios = [
        {
            'user_type': '新用户',
            'scenario': '第一次使用监考员设置功能',
            'expected_benefit': '通过使用提示快速了解所有功能'
        },
        {
            'user_type': '有经验用户',
            'scenario': '需要快速回忆某个功能的操作方法',
            'expected_benefit': '通过提示快速定位到相关功能'
        },
        {
            'user_type': '管理员',
            'scenario': '需要培训其他用户使用系统',
            'expected_benefit': '使用提示作为培训参考材料'
        }
    ]
    
    all_passed = True
    
    for scenario in reading_scenarios:
        print(f"\n   {scenario['user_type']}:")
        print(f"      场景: {scenario['scenario']}")
        print(f"      预期收益: {scenario['expected_benefit']}")
        print(f"      ✅ 使用提示能够满足需求")
    
    return all_passed

def main():
    """主测试函数"""
    print("🔧 使用提示添加测试")
    print("=" * 60)
    print("测试step3_proctors页面的使用提示是否正确添加")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查step2使用提示
    step2_ok, step2_info = check_step2_usage_tips()
    test_results.append(("step2使用提示", step2_ok))
    
    # 2. 检查step3使用提示
    step3_ok, step3_info = check_step3_usage_tips()
    test_results.append(("step3使用提示", step3_ok))
    
    # 3. 比较格式一致性
    test_results.append(("格式一致性", compare_usage_tips_format()))
    
    # 4. 分析提示内容
    test_results.append(("提示内容分析", analyze_usage_tips_content()))
    
    # 5. 检查提示位置
    test_results.append(("提示位置", check_tips_positioning()))
    
    # 6. 模拟用户阅读体验
    test_results.append(("用户阅读体验", simulate_user_reading_experience()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！")
        print("\n📝 使用提示添加完成:")
        print("✅ 格式统一 - 与step2_rooms保持完全一致的样式和布局")
        print("✅ 内容全面 - 覆盖监考员设置的所有主要功能")
        print("✅ 位置合适 - 在页面顶部，用户进入页面即可看到")
        print("✅ 三列布局 - 合理分组，便于快速浏览")
        print("✅ 用户友好 - 为不同类型用户提供有价值的指导")
        
        print("\n🚀 提示内容:")
        print("• 基础操作: 添加监考员、任教科目设置")
        print("• 偏好设置: 科目偏好、考场偏好、场次限制")
        print("• 批量管理: 批量操作、筛选排序、Excel导入")
        print("• 高级功能: 弹出编辑、模板下载")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
